<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.pig4cloud</groupId>
		<artifactId>disaster</artifactId>
		<version>5.7.0</version>
	</parent>

	<artifactId>disaster-biz</artifactId>
	<packaging>jar</packaging>

	<description>地质灾害业务处理模块</description>

	<dependencies>


		<!--poi-tl word文档-->
		<dependency>
			<groupId>com.deepoove</groupId>
			<artifactId>poi-tl</artifactId>
			<version>1.12.2</version>
		</dependency>

		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.9.2</version> <!-- 或者你项目支持的版本 -->
		</dependency>


		<!--EasyExcel-->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>3.3.2</version> <!-- 可根据需要调整版本 -->
		</dependency>


		<!--swagger  注解-->
		<dependency>
			<groupId>io.swagger.core.v3</groupId>
			<artifactId>swagger-annotations-jakarta</artifactId>
		</dependency>

		<!--upms api、model 模块-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-upms-api</artifactId>
		</dependency>

		<!--文件管理-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-oss</artifactId>
		</dependency>


		<!--feign 调用-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-feign</artifactId>
		</dependency>


		<!--安全模块-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-security</artifactId>
		</dependency>
		<!--日志处理-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-log</artifactId>
		</dependency>
		<!--接口文档-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-swagger</artifactId>
		</dependency>
		<!-- orm 模块-->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-spring-boot3-starter</artifactId>
		</dependency>
		<!--        <dependency>
					<groupId>com.mysql</groupId>
					<artifactId>mysql-connector-j</artifactId>
				</dependency>-->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
		<!--注册中心客户端-->
<!--		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>-->
		<!--配置中心客户端-->
<!--		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>-->
		<!--xss 过滤-->
		<dependency>
			<groupId>com.pig4cloud</groupId>
			<artifactId>pigx-common-xss</artifactId>
		</dependency>
		<!--undertow容器-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-undertow</artifactId>
		</dependency>
        <dependency>
            <groupId>com.pig4cloud</groupId>
            <artifactId>pigx-upms-biz</artifactId>
            <version>5.7.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>


	<profiles>
		<profile>
			<id>boot</id>
		</profile>
		<profile>
			<id>cloud</id>
			<activation>
				<!-- 默认环境 -->
				<activeByDefault>true</activeByDefault>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.springframework.boot</groupId>
						<artifactId>spring-boot-maven-plugin</artifactId>
					</plugin>
					<plugin>
						<groupId>io.fabric8</groupId>
						<artifactId>docker-maven-plugin</artifactId>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<excludes>
					<exclude>**/*.xlsx</exclude>
					<exclude>**/*.xls</exclude>
					<exclude>**/*.docx</exclude>   <!-- 🚨 把 docx 排除掉 -->
					<exclude>**/*.doc</exclude>    <!-- 建议 doc 也排除 -->
					<exclude>**/*.pptx</exclude>   <!-- 如果有 PPT 也排除 -->
					<exclude>**/*.pdf</exclude>    <!-- PDF 也排除 -->
				</excludes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>false</filtering>
				<includes>
					<include>**/*.xlsx</include>
					<include>**/*.xls</include>
					<include>**/*.docx</include>
					<include>**/*.doc</include>
					<include>**/*.pptx</include>
					<include>**/*.pdf</include>
				</includes>
			</resource>
		</resources>
	</build>
</project>