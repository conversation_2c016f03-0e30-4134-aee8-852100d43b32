package com.pig4cloud.pigx.gis;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.commons.io.FilenameUtils;

import java.io.File;

public class JsonToGeojsonConverter {
    public static void main(String[] args) {
        String outputDir = "D:\\01-doc\\2025-08-11-黄冈地灾-对接文档\\精细化气象预警对接\\";
        // 1. 输入JSON字符串（示例数据）
        String inputJson = "D:\\work\\gdrw-master\\target\\1747906381606.json";

        try {
            ObjectMapper mapper = new ObjectMapper();

            // 2. 解析输入JSON
            File jsonFile = new File(inputJson);
            ObjectNode rootNode = (ObjectNode) mapper.readTree(jsonFile);
            ArrayNode dsArray = (ArrayNode) rootNode.get("DS");

            // 3. 创建GeoJSON FeatureCollection
            ObjectNode featureCollection = mapper.createObjectNode();
            featureCollection.put("type", "FeatureCollection");
            ArrayNode featuresArray = mapper.createArrayNode(); // 存储所有Feature

            // 4. 遍历DS数组中的每个点位
            for (int i = 0; i < dsArray.size(); i++) {
                ObjectNode pointNode = (ObjectNode) dsArray.get(i);

                // 提取坐标和属性
                double lon = pointNode.get("Lon").asDouble();
                double lat = pointNode.get("Lat").asDouble();
                int stationId = pointNode.get("Station_Id_d").asInt();
                double pre1h = pointNode.get("PRE_1h").asDouble();
                String datetime = pointNode.get("Datetime").asText();

                // 4.1 构建geometry对象 (Point)
                ObjectNode geometry = mapper.createObjectNode();
                geometry.put("type", "Point");
                ArrayNode coordinates = mapper.createArrayNode();
                coordinates.add(lon); // 经度
                coordinates.add(lat); // 纬度
                geometry.set("coordinates", coordinates);

                // 4.2 构建properties对象
                ObjectNode properties = mapper.createObjectNode();
                properties.put("Station_Id_d", stationId);
                properties.put("PRE_1h", pre1h);
                properties.put("Datetime", datetime);

                // 4.3 构建Feature对象
                ObjectNode feature = mapper.createObjectNode();
                feature.put("type", "Feature");
                feature.set("geometry", geometry);
                feature.set("properties", properties);

                // 添加到features数组
                featuresArray.add(feature);
            }

            // 5. 将features数组放入FeatureCollection
            featureCollection.set("features", featuresArray);

            // 6. 输出GeoJSON字符串（格式化后）
            String geoJsonOutput = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(featureCollection);
            System.out.println("转换后的GeoJSON:\n" + geoJsonOutput);

            mapper.writeValue(new File(outputDir, FilenameUtils.getBaseName(jsonFile.getName()) + ".geojson"), featureCollection);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

