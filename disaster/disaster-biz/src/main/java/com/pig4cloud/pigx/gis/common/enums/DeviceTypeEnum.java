package com.pig4cloud.pigx.gis.common.enums;

/**
 * 设备类型枚举
 */
public enum DeviceTypeEnum {

    LAXIAN_WEIYI_ZHAN(1, "拉线式位移站", "Cable displacement station"),
    GNSS_WEIYI_JIANCE_ZHAN(2, "GNSS位移监测站", "GNSS displacement monitoring station"),
    YULIANG_ZHAN(3, "雨量站", "Rainfall station"),
    GNSS_JIZHUN_ZHAN(4, "GNSS基准站", "GNSS reference station"),
    ZONGHE_ZHAN(5, "综合站", "Comprehensive station"),
    CELIANG_SHIPIN_ZHUABO(6, "测量型视觉捕捉设备", "Measurement visual capture device"),
    TONGYONG_SHIPIN_ZHUABO(7, "通用型视觉捕捉(摄像头)设备", "General visual capture device"),
    GNSS_JIANCE_SHEBEI(8, "GNSS监测设备", "GNSS monitoring device"),
    TURANG_HANSHUILV(9, "土壤含水率设备", "Soil moisture device"),
    JIZAI_LEIDA(10, "机载雷达", "Airborne radar");

    private final int code;
    private final String name;
    private final String description;

    DeviceTypeEnum(int code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据 code 获取枚举
     */
    public static DeviceTypeEnum getByCode(int code) {
        for (DeviceTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据 code 获取 name（中文名称）
     */
    public static String getNameByCode(int code) {
        DeviceTypeEnum type = getByCode(code);
        return type != null ? type.getName() : null;
    }
}