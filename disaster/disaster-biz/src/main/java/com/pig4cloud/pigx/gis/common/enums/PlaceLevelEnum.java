package com.pig4cloud.pigx.gis.common.enums;

/**
 * 行政区层级，1省，2市，3县区，4乡镇，5村
 *
 * <AUTHOR>
 */
public enum PlaceLevelEnum {

    COUNTRY("0", "国家"),
    PROVINCE("1", "省"),
    CITY("2", "市州"),
    COUNTY("3", "区县"),
    TOWN("4", "乡镇"),
    VILLAGE("5", "村");

    private final String code;
    private final String info;


    PlaceLevelEnum(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }
}
