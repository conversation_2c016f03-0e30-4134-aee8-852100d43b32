package com.pig4cloud.pigx.gis.common.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.pig4cloud.pigx.gis.dto.DangerPointStaffExcelDTO;
import com.pig4cloud.pigx.gis.dto.DangerPointsGridMembersMismatchDTO;
import com.pig4cloud.pigx.gis.service.DangerPointsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@Component
@Scope("prototype")
public class GridMemberExcelListener extends AnalysisEventListener<DangerPointStaffExcelDTO> {
    private final DangerPointsService gridMemberService;
    private final List<DangerPointStaffExcelDTO> allData = new ArrayList<>();
    private String currentCountry = "";
    private String firstRow = ""; // 用来保存第一行数据

    private List<DangerPointsGridMembersMismatchDTO> mismatchData;

    public GridMemberExcelListener(DangerPointsService gridMemberService) {
        this.gridMemberService = gridMemberService;
    }

    @Override
    public void invoke(DangerPointStaffExcelDTO data, AnalysisContext context) {
        int rowIndex = context.readRowHolder().getRowIndex();

        // 保存绝对第一行
        if (rowIndex == 0) {
            firstRow = dataToString(data);
            log.info("读取到 Excel 第一行数据:{}", firstRow);
        }

        // 从第6行开始才做真正处理
        if (rowIndex < 5) {
            return; // 前5行丢掉
        }


        // 处理合并单元格：如果当前行乡镇为空则使用上一行的乡镇
        if (StringUtils.isNotBlank(data.getCountry())) {
            currentCountry = data.getCountry();
        } else {
            data.setCountry(currentCountry);
        }

        // 移除单元格中的换行符
        data.setCountry(data.getCountry().replace("\n", ""));
        data.setVillage(data.getVillage().replace("\n", ""));

        allData.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (!allData.isEmpty()) {
            mismatchData =gridMemberService.batchUpdateGridMembers(allData,getFirstThreeChineseCharacters(firstRow));
        }
    }


    public List<DangerPointsGridMembersMismatchDTO> mismatchData() {
        return mismatchData;
    }

    /**
     * 通用方法：把 DTO 所有字段拼接成字符串
     */
    private String dataToString(DangerPointStaffExcelDTO data) {
        if (data == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        try {
            // 获取类的所有字段（包括 private）
            Field[] fields = data.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true); // 允许访问 private 字段
                Object value = field.get(data);
                if (value != null) {
                    sb.append(value).append(" ");
                }
            }
        } catch (IllegalAccessException e) {
            log.error("反射读取 DTO 字段失败", e);
        }
        return sb.toString().trim();
    }


    /**
     * 获取字符串的前三个汉字
     * @param text 输入字符串
     * @return 前三个汉字
     */
    private String getFirstThreeChineseCharacters(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 使用正则表达式提取前三个汉字
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("[\\u4e00-\\u9fff]");
        java.util.regex.Matcher matcher = pattern.matcher(text);

        StringBuilder result = new StringBuilder();
        int count = 0;

        while (matcher.find() && count < 3) {
            result.append(matcher.group());
            count++;
        }

        return result.toString();
    }


}