package com.pig4cloud.pigx.gis.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.gis.common.listener.GridMemberExcelListener;
import com.pig4cloud.pigx.gis.domain.DangerPoints;
import com.pig4cloud.pigx.gis.domain.EchartsProperty;
import com.pig4cloud.pigx.gis.dto.DangerPointStaffExcelDTO;
import com.pig4cloud.pigx.gis.dto.DangerPointsGridMembersMismatchDTO;
import com.pig4cloud.pigx.gis.param.DangerPointsPictureA;
import com.pig4cloud.pigx.gis.param.DangerPointsPictureB;
import com.pig4cloud.pigx.gis.service.DangerPointsService;
import com.pig4cloud.pigx.gis.vo.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springdoc.core.annotations.ParameterObject;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.util.List;

/**
 * 隐患点controller
 *
 * <AUTHOR>
 * @Date 2022/5/23
 */

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/disaster/dangerPoints")
@Tag(description = "dangerPoints", name = "隐患点管理模块")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DangerPointsController {

    @Resource
    private DangerPointsService dangerPointsService;


    /**
     * 导出防灾工作明白卡Word
     *
     * @param id
     * @param response
     * @throws Exception
     */
    @GetMapping("/exportWorkFile/{id}")
    public void exportWorkFile(@PathVariable String id, HttpServletResponse response) {
        dangerPointsService.exportWorkFile(id, response);
    }


    /**
     * 导出防灾避险明白卡Word
     *
     * @param id
     * @param response
     * @throws Exception
     */
    @GetMapping("/exportAvoidRiskFile/{id}")
    public void exportAvoidRiskFile(@PathVariable String id, HttpServletResponse response) {
        dangerPointsService.exportAvoidRiskFile(id, response);
    }


    /**
     * 上传 Excel，按 乡镇 + 网格名（村） 更新人员信息
     */
    @PostMapping(value = "/uploadGridMembersExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<List<DangerPointsGridMembersMismatchDTO>> uploadGridMembers(@RequestPart("file") MultipartFile file) {
        if (file.isEmpty()) {
            return R.failed("请上传文件");
        }

        try {
            // 创建监听器实例
            GridMemberExcelListener listener = new GridMemberExcelListener(dangerPointsService);

            // 使用 EasyExcel 读取文件
            EasyExcel.read(file.getInputStream(), DangerPointStaffExcelDTO.class, listener)
                    .sheet(0) // 第一个Sheet
                    .headRowNumber(0) // 从第0行开始读取
                    .doRead();

            List<DangerPointsGridMembersMismatchDTO> dangerPointsGridMembersMismatchDTOS = listener.mismatchData();

            return R.ok(dangerPointsGridMembersMismatchDTOS);
        } catch (Exception e) {
            return R.failed("文件处理失败");
        }
    }


    /**
     * 隐患点批量导入
     *
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/dangerPointImport")
    @ApiOperation(value = "隐患点批量导入")
    public R<Integer> dangerPointImport(final MultipartFile file) throws Exception {
        Integer integer = dangerPointsService.dangerPointImport(file);
        return R.ok(integer);
    }


    /**
     * 导出隐患点
     *
     * @param reqAO
     * @return
     */
    @GetMapping("/export")
    public void export(@RequestParam(defaultValue = "1") Long pageNum,   //@ParameterObject Page<HgDangerPoints> page,
                       @RequestParam(defaultValue = "10") Long pageSize,
                       @ParameterObject DangerPointsReqVO reqAO, HttpServletResponse response) throws IOException {
        IPage<DangerPoints> pointPage = new Page<>(pageNum, pageSize);
        List<DangerPoints> list = dangerPointsService.selectHgDangerPointsList(pointPage, reqAO).getRecords();
        // 设置响应头
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("隐患点数据", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");


        // 创建标题行样式
        // 创建一个用于写入单元格样式的对象，此处用于设置表头的样式
        WriteCellStyle headStyle = new WriteCellStyle();
        // 设置表头单元格的填充前景色为灰色，以提高表头的可视区分度
        headStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        //设置表头文字加粗
        headStyle.setBorderTop(BorderStyle.THIN);
        // 创建一个用于写入字体样式的对象，用于定制表头文本的字体样式
        WriteFont headFont = new WriteFont();
        // 设置表头字体的大小为12点，以确保表头信息的清晰可读
        headFont.setFontHeightInPoints((short) 12);
        // 将表头字体设置为粗体，以强调表头的重要性
        headFont.setBold(false);
        // 将定制的表头字体样式应用到表头单元格样式中，以统一表头的视觉效果
        headStyle.setWriteFont(headFont);


        // 创建内容行样式（可选）
        WriteCellStyle contentStyle = new WriteCellStyle();
        WriteFont contentFont = new WriteFont();
        contentFont.setFontHeightInPoints((short) 10);
        contentStyle.setWriteFont(contentFont);

        // 组合样式策略
        HorizontalCellStyleStrategy strategy = new HorizontalCellStyleStrategy(headStyle, contentStyle);

        // 写数据到响应流
        EasyExcel.write(response.getOutputStream(), DangerPoints.class).registerWriteHandler(strategy).sheet("隐患点数据").doWrite(list);
    }


    /**
     * 分页查询隐患点
     *
     * @param reqAO
     * @return
     */
    @GetMapping("/page")
    public R<Page<DangerPoints>> page(@RequestParam(defaultValue = "1") Long pageNum,
                                      @RequestParam(defaultValue = "10") Long pageSize,
                                      @ParameterObject DangerPointsReqVO reqAO) {
        IPage<DangerPoints> pointPage = new Page<>(pageNum, pageSize);
        Page<DangerPoints> list = dangerPointsService.selectHgDangerPointsList(pointPage, reqAO);
        return R.ok(list);
    }


    /**
     * 查询隐患点列表
     *
     * @param reqAO
     * @return
     */
    @PostMapping("/list")
    public R<List<DangerPoints>> list(@RequestBody DangerPointsReqVO reqAO) {
        IPage<DangerPoints> pointPage = new Page<>(1, -1);
        Page<DangerPoints> list = dangerPointsService.selectHgDangerPointsList(pointPage, reqAO);
        return R.ok(list.getRecords());
    }


    /**
     * 获取有预警的隐患点统计  总数 有预警隐患点数  无预警隐患点数
     *
     * @return
     */
    @GetMapping("/getDangerWarningNum")
    public R<DangerPointsStatisticsVO> getDangerWarningNum() {
        return R.ok(dangerPointsService.selectDangerWarningNum());
    }


    /**
     * 查询隐患点类型分组统计数量
     *
     * @return
     */
    @PostMapping("/getStatisticsByDangerPointsType")
    public R<List<EchartsProperty>> getStatisticsByDangerPointsType(@RequestBody DangerPointsReqVO reqAO) {
        return R.ok(dangerPointsService.selectStatisticsByDangerPointsType(reqAO));
    }

    /**
     * 查询稳定性现状名称统计数量
     *
     * @return
     */
    @PostMapping("/getStatisticsByStabilityStatus")
    public R<List<EchartsProperty>> getStatisticsByStabilityStatus(@RequestBody DangerPointsReqVO reqAO) {
        return R.ok(dangerPointsService.getStatisticsByStabilityStatus(reqAO));
    }

    /**
     * 获取隐患点类型名称分组
     *
     * @return List<String>
     */
    @PostMapping("/group/dangerPointsType")
    public R<List<String>> getGroupByDangerPointsType() {
        return R.ok(dangerPointsService.selectGroupByDangerPointsType());
    }

    /**
     * 获取稳定性现状名称分组
     *
     * @return List<String>
     */
    @PostMapping("/group/stabilityStatus")
    public R<List<String>> getGroupByStabilityStatus() {
        return R.ok(dangerPointsService.selectGroupByStabilityStatus());
    }

    /**
     * 获取隐患点详细信息
     */
    @GetMapping(value = "/{id}")
    public R getInfo(@PathVariable("id") String id) {
        return R.ok(dangerPointsService.selectHgDangerPointsById(id));
    }


    /**
     * 隐患点政区分部统计
     *
     * @return
     */
    @GetMapping("/areaCount")
    public R<List<DangerPointsCountVO>> getAreaDangerPointCount() {
        return R.ok(dangerPointsService.getAreaDangerPointCount());
    }

    /**
     * 在地图上按行政区划级别（区县级，乡镇级）统计隐患点数量
     *
     * @return
     */
    @PostMapping("/map/administrativeStatistics")
    public R<DangerPointsMapAdministrativeStatisticsVO> getAdministrativeStatistics(@RequestBody DangerPointsReqVO reqAO) {
        return R.ok(dangerPointsService.getAdministrativeStatistics(reqAO));
    }

    /**
     * 新增隐患点
     */
    @PostMapping
    public R add(@RequestBody DangerPoints dangerPoints) {
        dangerPointsService.insertHgDangerPoints(dangerPoints);
        return R.ok();
    }

    /**
     * 修改隐患点
     */
    @PutMapping
    public R edit(@RequestBody DangerPoints dangerPoints) {
        dangerPointsService.updateHgDangerPoints(dangerPoints);
        return R.ok();
    }


    /**
     * 修改隐患点内现场照片和防灾预案图
     */
    @PutMapping("/editPictureA")
    public R editPictureA(@RequestBody DangerPointsPictureA dangerPointsPictureA) {
        dangerPointsService.updateDangerPointsPictureA(dangerPointsPictureA);
        return R.ok();
    }

    /**
     * 修改隐患点内平面图和剖面图
     */
    @PutMapping("/editPictureB")
    public R editPictureB(@RequestBody DangerPointsPictureB dangerPointsPictureB) {
        dangerPointsService.updateDangerPointsPictureB(dangerPointsPictureB);
        return R.ok();
    }


    /**
     * 删除隐患点
     */
    @DeleteMapping("/{ids}")
    public R remove(@PathVariable String[] ids) {
        for (String id : ids) {
            System.out.println("id为:" + id);
        }

        return R.ok(dangerPointsService.deleteHgDangerPointsByIds(ids));
    }


    /**
     * 是否核销
     *
     * @param ids              隐患点主键ids数组
     * @param verificationFlag 核销标识
     */
    @GetMapping(value = "/changeVerificationFlag")
    public R changeVerificationFlag(@RequestParam("id") String[] ids, @RequestParam("verificationFlag") Boolean verificationFlag) {
        dangerPointsService.changeVerificationFlag(ids, verificationFlag);
        return R.ok();
    }


    /**
     * 通过主键id获取文件信息(现场照片，防灾预案图，平面图,剖面图)
     *
     * @param id 隐患点的主键id
     */
    @GetMapping(value = "/getDangerPointsFile/{id}")
    public R<DangerPointsFileVO> getDangerPointsFile(@PathVariable("id") String id) {
        DangerPointsFileVO result = dangerPointsService.getDangerPointsFile(id);
        return R.ok(result);
    }


}
