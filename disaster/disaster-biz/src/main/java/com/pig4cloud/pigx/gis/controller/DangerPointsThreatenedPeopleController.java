package com.pig4cloud.pigx.gis.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.gis.domain.DangerPointsThreatenedPeople;
import com.pig4cloud.pigx.gis.service.DangerPointsThreatenedPeopleService;
import com.pig4cloud.pigx.gis.vo.DangerPointsThreatenedPeopleVO;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;


/**
 * 隐患点受灾人员管理
 *
 * <AUTHOR>
 * @date 2025-05-30 18:47:18
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/disaster/threatenedPeople")
@Tag(description = "threatenedPeople", name = "受隐患人员模块")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DangerPointsThreatenedPeopleController {

	@Autowired
	private DangerPointsThreatenedPeopleService hgDangerPointsThreatenedPeopleService;

	


	/**
	 * 分页查询受隐患人员
	 *
	 * @param currentPageCount
	 * @param pageSize
	 * @param dangerPointsId   隐患点id
	 * @return
	 */
	@GetMapping("/getList")
	public R<DangerPointsThreatenedPeopleVO> getTagDataList(@RequestParam(defaultValue = "1") Long currentPageCount,
															@RequestParam(defaultValue = "10") Long pageSize,
															@RequestParam(required = true) String dangerPointsId) {
		DangerPointsThreatenedPeopleVO gitList = hgDangerPointsThreatenedPeopleService.getList(new Page<DangerPointsThreatenedPeople>(currentPageCount, pageSize), dangerPointsId);
		return R.ok(gitList);
	}


	/**
	 * 新增受隐患人员
	 *
	 * @param dangerPointsThreatenedPeople
	 * @return
	 */
	@PostMapping("/add")
	public R add(@RequestBody DangerPointsThreatenedPeople dangerPointsThreatenedPeople) {
		log.debug("新增数据：{}", dangerPointsThreatenedPeople);
		hgDangerPointsThreatenedPeopleService.add(dangerPointsThreatenedPeople);
		return R.ok();
	}


	/**
	 * 修改受隐患人员
	 * @param dangerPointsThreatenedPeople
	 * @return
	 */

	@PostMapping("/edit")
	public R edit(@RequestBody DangerPointsThreatenedPeople dangerPointsThreatenedPeople){
		log.debug("修改数据：{}", dangerPointsThreatenedPeople);
		hgDangerPointsThreatenedPeopleService.edit(dangerPointsThreatenedPeople);
		return R.ok();
	}


	/**
	 * 批量删除受隐患人员
	 */
	@DeleteMapping("/{ids}")
	public R remove(@PathVariable Long[] ids) {
		for (Long id : ids) {
			System.out.println("id为:" + id);
		}
		//将ids数组转为集合
		List<Long> idList = Arrays.asList(ids);
		return R.ok(hgDangerPointsThreatenedPeopleService.removeByIds(idList));
	}


}
