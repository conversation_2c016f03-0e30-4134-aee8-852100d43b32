package com.pig4cloud.pigx.gis.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.common.file.core.FileProperties;
import com.pig4cloud.pigx.common.log.annotation.SysLog;
import com.pig4cloud.pigx.gis.entity.DataSource3dEntity;
import com.pig4cloud.pigx.gis.service.DataSource3dService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import com.pig4cloud.pigx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.List;

/**
 * 三维模型数据-区域模型
 *
 * <AUTHOR>
 * @date 2025-09-02 13:35:41
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/disaster/DataSource3d")
@Tag(description = "DataSource3d", name = "三维模型数据-区域模型管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DataSource3dController {

    private final DataSource3dService dataSource3dService;



    /**
     * 三维模型数据-区域模型上传
     *
     * @param file zip文件包
     * @param name 模型名称
     * @param date 时项 例如：2025-09-02
     * @return
     */
    @PostMapping("/upload")
    public R<String> uploadModel(@RequestParam("file") MultipartFile file,
                                  @RequestParam("name") String name,
                                  @RequestParam() LocalDate date) {
        try {
            String fileName = getRealFileName(file);
            if (StringUtils.isBlank(fileName) || !fileName.endsWith("zip")) {
                return R.failed("文件格式不正确，只能上传zip文件包！");
            }
            String sourceId = dataSource3dService.saveUploadTask(file, name, date);
            dataSource3dService.processAsync(sourceId, file);
            return R.ok(sourceId, "上传任务已提交，sourceId=" + sourceId);
        } catch (Exception e) {
            e.printStackTrace();
            return R.failed("上传失败: " + e.getMessage());
        }
    }


    /**
     * 获取上传任务状态
     *
     * @param sourceId
     * @return
     */
    @GetMapping("/task/{id}")
    public R getTask(@PathVariable("id") String sourceId) {
        DataSource3dEntity task = dataSource3dService.selectById(sourceId);
        if (task == null) {
            return R.failed("任务不存在");
        }
        return R.ok(task);
    }




    public static String getRealFileName(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (StringUtils.isBlank(filename)) {
            return null;
        }
        int unixSep = filename.lastIndexOf('/');
        int winSep = filename.lastIndexOf('\\');
        int pos = (Math.max(winSep, unixSep));
        if (pos != -1) {
            filename = filename.substring(pos + 1);
        }
        return filename;
    }


    /**
     * 分页查询
     *
     * @param page         分页对象
     * @param dataSource3d 三维模型数据-区域模型
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    public R getDataSource3dPage(@ParameterObject Page page, @ParameterObject DataSource3dEntity dataSource3d) {
        LambdaQueryWrapper<DataSource3dEntity> wrapper = Wrappers.lambdaQuery();
        return R.ok(dataSource3dService.page(page, wrapper));
    }


    /**
     * 通过id查询三维模型数据-区域模型
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    public R getById(@PathVariable("id") String id) {
        return R.ok(dataSource3dService.getById(id));
    }



    /**
     * 修改三维模型数据-区域模型
     *
     * @param dataSource3d 三维模型数据-区域模型
     * @return R
     */
    @Operation(summary = "修改三维模型数据-区域模型", description = "修改三维模型数据-区域模型")
    @SysLog("修改三维模型数据-区域模型")
    @PutMapping
    public R updateById(@RequestBody DataSource3dEntity dataSource3d) {
        return R.ok(dataSource3dService.updateById(dataSource3d));
    }

    /**
     * 通过id删除三维模型数据-区域模型
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除三维模型数据-区域模型", description = "通过id删除三维模型数据-区域模型")
    @SysLog("通过id删除三维模型数据-区域模型")
    @DeleteMapping
    public R removeById(@RequestBody String[] ids) {
        return R.ok(dataSource3dService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     *
     * @param dataSource3d 查询条件
     * @param ids          导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('disaster_DataSource3d_export')")
    public List<DataSource3dEntity> export(DataSource3dEntity dataSource3d, String[] ids) {
        return dataSource3dService.list(Wrappers.lambdaQuery(dataSource3d).in(ArrayUtil.isNotEmpty(ids), DataSource3dEntity::getId, ids));
    }
}