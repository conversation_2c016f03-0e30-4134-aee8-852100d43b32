package com.pig4cloud.pigx.gis.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.common.log.annotation.SysLog;
import com.pig4cloud.pigx.gis.entity.DataSourceDisaster3dEntity;
import com.pig4cloud.pigx.gis.service.DataSourceDisaster3dService;
import org.apache.commons.lang3.StringUtils;
import com.pig4cloud.pigx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.List;


/**
 * 三维模型数据-隐患点，风险区模型
 *
 * <AUTHOR>
 * @date 2025-09-03 10:35:47
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/disaster/DataSourceDisaster3d")
@Tag(description = "DataSourceDisaster3d", name = "三维模型数据-隐患点，风险区模型管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DataSourceDisaster3dController {

    private final DataSourceDisaster3dService dataSourceDisaster3dService;


    /**
     * 三维模型数据-隐患点，风险区模型上传
     *
     * @param file zip文件包
     * @param name 模型名称
     * @param date 时项 例如：2025-09-02
     * @return
     */
    @PostMapping("/upload")
    public R<String> uploadModel(@RequestParam("file") MultipartFile file,
                                 @RequestParam("monitorDataType") String monitorDataType,
                                 @RequestParam("provincialCode") String provincialCode,
                                 @RequestParam("name") String name,
                                 @RequestParam LocalDate date) {
        try {
            String fileName = getRealFileName(file);
            if (StringUtils.isBlank(fileName) || !fileName.endsWith("zip")) {
                return R.failed("文件格式不正确，只能上传zip文件包！");
            }
            String sourceId = dataSourceDisaster3dService.saveUploadTask(file, monitorDataType, provincialCode, name, date);
            dataSourceDisaster3dService.processDataSourceDisaster3dAsync(sourceId, file);
            return R.ok(sourceId, "上传任务已提交，sourceId=" + sourceId);
        } catch (Exception e) {
            e.printStackTrace();
            return R.failed("上传失败: " + e.getMessage());
        }
    }


    public static String getRealFileName(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (StringUtils.isBlank(filename)) {
            return null;
        }
        int unixSep = filename.lastIndexOf('/');
        int winSep = filename.lastIndexOf('\\');
        int pos = (Math.max(winSep, unixSep));
        if (pos != -1) {
            filename = filename.substring(pos + 1);
        }
        return filename;
    }


    /**
     * 获取上传任务状态
     *
     * @param sourceId
     * @return
     */
    @GetMapping("/task/{id}")
    public R getTask(@PathVariable("id") String sourceId) {
        DataSourceDisaster3dEntity task = dataSourceDisaster3dService.selectById(sourceId);
        if (task == null) {
            return R.failed("任务不存在");
        }
        return R.ok(task);
    }


    /**
     * 分页查询
     *
     * @param current 页
     * @param size    页数
     * @param name    名称
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    public R getDataSourceDisaster3dPage(@RequestParam(defaultValue = "1") Long current,
                                         @RequestParam(defaultValue = "10") Long size,
                                         @RequestParam String name) {
        List<DataSourceDisaster3dEntity> result = dataSourceDisaster3dService.getDataSourceDisaster3dPage(new Page<DataSourceDisaster3dEntity>(current, size), name);
        return R.ok(result);
    }

    /**
     * 分页查询历史版本
     *
     * @param current 页
     * @param size    页数
     * @param monitorDataType    监测类型，1隐患点，2风险区
     * @param provincialCode   省级平台统一编号
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/versionPage")
    public R getDataSourceDisaster3dPageVersion(@RequestParam(defaultValue = "1") Long current,
                                                @RequestParam(defaultValue = "10") Long size,
                                                @RequestParam(required = true ,defaultValue = "1") String monitorDataType,
                                                @RequestParam String provincialCode
    ) {
        List<DataSourceDisaster3dEntity> result = dataSourceDisaster3dService.getDataSourceDisaster3dPageVersion(new Page<DataSourceDisaster3dEntity>(current, size),monitorDataType ,provincialCode);
        return R.ok(result);
    }



    /**
     * 通过id查询三维模型数据-隐患点，风险区模型
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    public R getById(@PathVariable("id") String id) {
        return R.ok(dataSourceDisaster3dService.getById(id));
    }

    /**
     * 新增三维模型数据-隐患点，风险区模型
     *
     * @param dataSourceDisaster3d 三维模型数据-隐患点，风险区模型
     * @return R
     */
    @Operation(summary = "新增三维模型数据-隐患点，风险区模型", description = "新增三维模型数据-隐患点，风险区模型")
    @SysLog("新增三维模型数据-隐患点，风险区模型")
    @PostMapping
    public R save(@RequestBody DataSourceDisaster3dEntity dataSourceDisaster3d) {
        return R.ok(dataSourceDisaster3dService.save(dataSourceDisaster3d));
    }

    /**
     * 修改三维模型数据-隐患点，风险区模型
     *
     * @param dataSourceDisaster3d 三维模型数据-隐患点，风险区模型
     * @return R
     */
    @Operation(summary = "修改三维模型数据-隐患点，风险区模型", description = "修改三维模型数据-隐患点，风险区模型")
    @SysLog("修改三维模型数据-隐患点，风险区模型")
    @PutMapping
    public R updateById(@RequestBody DataSourceDisaster3dEntity dataSourceDisaster3d) {
        return R.ok(dataSourceDisaster3dService.updateById(dataSourceDisaster3d));
    }

    /**
     * 通过id删除三维模型数据-隐患点，风险区模型
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除三维模型数据-隐患点，风险区模型", description = "通过id删除三维模型数据-隐患点，风险区模型")
    @SysLog("通过id删除三维模型数据-隐患点，风险区模型")
    @DeleteMapping
    public R removeById(@RequestBody String[] ids) {
        return R.ok(dataSourceDisaster3dService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     *
     * @param dataSourceDisaster3d 查询条件
     * @param ids                  导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    public List<DataSourceDisaster3dEntity> export(DataSourceDisaster3dEntity dataSourceDisaster3d, String[] ids) {
        return dataSourceDisaster3dService.list(Wrappers.lambdaQuery(dataSourceDisaster3d).in(ArrayUtil.isNotEmpty(ids), DataSourceDisaster3dEntity::getId, ids));
    }
}