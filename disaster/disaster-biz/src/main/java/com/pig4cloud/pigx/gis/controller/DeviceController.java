package com.pig4cloud.pigx.gis.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.common.log.annotation.SysLog;
import com.pig4cloud.pigx.gis.domain.EchartsProperty;
import com.pig4cloud.pigx.gis.entity.Device;
import com.pig4cloud.pigx.gis.service.DeviceService;
import com.pig4cloud.pigx.gis.vo.DeviceNumberCountVO;
import com.pig4cloud.pigx.gis.vo.DeviceVO;
import com.pig4cloud.pigx.gis.vo.DevicesAllGroupVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import com.pig4cloud.pigx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 监测设备_改
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/disaster/device")
@Tag(description = "Device", name = "监测设备管理_改")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class DeviceController {

    private final DeviceService deviceService;

    /**
     * 分页查询监测设备
     *
     * @param current
     * @param size
     * @param reqVo
     * @return
     */
    @ApiOperation("分页查询监测设备")
    @GetMapping("/devicesPage")
    public R<Page<DeviceVO>> page(@ParameterObject Long current, @ParameterObject Long size, @ParameterObject Device reqVo) {
        Page<DeviceVO> page = deviceService.selectDeviceList(new Page<Device>(current, size), reqVo);
        return R.ok(page);
    }

    /**
     * 查询监测设备列表
     *
     * @param reqVo
     * @return
     */
    @ApiOperation("查询监测设备列表")
    @PostMapping("/devicesList")
    public R<List<DeviceVO>> list(@RequestBody Device reqVo) {
        Page<DeviceVO> page = deviceService.selectDeviceList(new Page<Device>(1, -1), reqVo);
        return R.ok(page.getRecords());
    }

    /**
     * 设备类型(现在叫传感器类型)分组
     *
     * @return
     */
    @PostMapping("/group/sensorType")
    public R groupByStationType() {
        return R.ok(deviceService.selectGroupByStationType());
    }

    /**
     * 一张图右边  设备总数 在线 在线率 视频数
     *
     * @return
     */
    @GetMapping("/deviceNumberCount")
    public R<DeviceNumberCountVO> deviceNumberCount() {
        return R.ok(deviceService.deviceNumberCount());
    }

    /**
     * 一张图右边检测设备分类      各设备类型名+数量
     *
     * @return
     * @Param monitorType  1是隐患点 2是风险区  不填是所有
     */
    @GetMapping("/sensorTypeCount")
    public R<List<EchartsProperty>> sensorTypeCount(@RequestParam(required = false) String monitorType) {
        return R.ok(deviceService.getSensorTypeCount(monitorType));
    }

    /**
     * 设备分组   隐患点和风险区的设备分组    设备制造商的分组
     *
     * @return
     */
    @PostMapping("/group/allGroup")
    public R<DevicesAllGroupVO> allGroup() {
        return R.ok(deviceService.allGroup());
    }

    /**
     * 获取监测设备详细信息
     */
    @GetMapping(value = "/{id}")
    public R<DeviceVO> getInfo(@PathVariable("id") Long id) {
        return R.ok(deviceService.selectHgDevicesById(id));
    }

    /**
     * 一张图设备预警统计     各设备类型名+数量
     *
     * @return
     * @Param monitorType  1是隐患点 2是风险区  不填是所有
     */
    @PostMapping("/statistics/warningLevelType")
    public R<List<EchartsProperty>> statisticsByWarningLevelType(@RequestParam(required = false) Integer monitorType) {
        return R.ok(deviceService.selectStatisticsByWarningLevelType(monitorType));
    }




































    /**
     * 分页查询
     *
     * @param page   分页对象
     * @param device 监测设备
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('gis_device_view')")
    public R getDevicePage(@ParameterObject Page page, @ParameterObject Device device) {
        LambdaQueryWrapper<Device> wrapper = Wrappers.lambdaQuery();
        return R.ok(deviceService.page(page, wrapper));
    }

    /**
     * 通过id查询监测设备
     * @param id id
     * @return R
     */
    /*@Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('gis_device_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(deviceService.getById(id));
    }*/

    /**
     * 新增监测设备
     *
     * @param device 监测设备
     * @return R
     */
    @Operation(summary = "新增监测设备", description = "新增监测设备")
    @SysLog("新增监测设备")
    @PostMapping
    @PreAuthorize("@pms.hasPermission('gis_device_add')")
    public R save(@RequestBody Device device) {
        return R.ok(deviceService.save(device));
    }

    /**
     * 修改监测设备
     *
     * @param device 监测设备
     * @return R
     */
    @Operation(summary = "修改监测设备", description = "修改监测设备")
    @SysLog("修改监测设备")
    @PutMapping
    @PreAuthorize("@pms.hasPermission('gis_device_edit')")
    public R updateById(@RequestBody Device device) {
        return R.ok(deviceService.updateById(device));
    }

    /**
     * 通过id删除监测设备
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除监测设备", description = "通过id删除监测设备")
    @SysLog("通过id删除监测设备")
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('gis_device_del')")
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(deviceService.removeBatchByIds(CollUtil.toList(ids)));
    }

    /**
     * 导出excel 表格
     *
     * @param device 查询条件
     * @param ids    导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('gis_device_export')")
    public List<Device> export(Device device, Long[] ids) {
        return deviceService.list(Wrappers.lambdaQuery(device).in(ArrayUtil.isNotEmpty(ids), Device::getId, ids));
    }
}