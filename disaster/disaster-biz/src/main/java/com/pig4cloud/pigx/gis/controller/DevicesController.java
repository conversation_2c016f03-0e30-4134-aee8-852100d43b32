package com.pig4cloud.pigx.gis.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.gis.domain.EchartsProperty;
import com.pig4cloud.pigx.gis.domain.Devices;
import com.pig4cloud.pigx.gis.domain.DevicesMaintenance;
import com.pig4cloud.pigx.gis.mapper.DevicesMaintenanceMapper;
import com.pig4cloud.pigx.gis.service.DeviceService;
import com.pig4cloud.pigx.gis.vo.DeviceNumberCountVO;
import com.pig4cloud.pigx.gis.vo.DeviceVO;
import com.pig4cloud.pigx.gis.vo.DevicesAllGroupVO;
import com.pig4cloud.pigx.gis.vo.DevicesVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 监测设备Controller
 *
 * <AUTHOR>
 * @date 2025年6月9日09:25:36
 */
//@Api(tags = {"【监测设备】服务接口"})
//@RestController
//@RequestMapping("/disaster/devices")
public class DevicesController {

    @Resource
    private DeviceService deviceService;

    @Resource
    private DevicesMaintenanceMapper devicesMaintenanceMapper;

    /**
     * 分页查询监测设备
     *
     * @param current
     * @param size
     * @param reqVo
     * @return
     */
    @ApiOperation("分页查询监测设备")
    @PostMapping("/devicesPage")
    public R<Page<DevicesVO>> page(@ParameterObject Long current, @ParameterObject Long size, @ParameterObject Devices reqVo) {
        Page<DevicesVO> page = deviceService.selectHgDevicesList(new Page<Devices>(current, size), reqVo);
        return R.ok(page);
    }

    /**
     * 查询监测设备列表
     *
     * @param reqVo
     * @return
     */
    @ApiOperation("查询监测设备列表")
    @PostMapping("/devicesList")
    public R<List<DevicesVO>> list(@RequestBody Devices reqVo) {
        Page<DevicesVO> page = deviceService.selectHgDevicesList(new Page<Devices>(1, Integer.MAX_VALUE), reqVo);
        return R.ok(page.getRecords());
    }

    /**
     * 设备类型分组
     *
     * @return
     */
    @PostMapping("/group/deviceType")
    public R groupByStationType() {
        return R.ok(deviceService.selectGroupByStationType());
    }

    /**
     * 一张图右边检测设备分类      各设备类型名+数量
     * @Param monitorType  1是隐患点 2是风险区  不填是所有
     * @return
     */
    @GetMapping("/deviceTypeCount")
    public R<List<EchartsProperty>> deviceTypeCount(@RequestParam(required = false) String monitorType) {
        return R.ok(deviceService.getSensorTypeCount(monitorType));
    }




    /**
     * 一张图右边  设备总数 在线 在线率 视频数
     *
     * @return
     */
    @GetMapping("/deviceNumberCount")
    public R<DeviceNumberCountVO> deviceNumberCount() {
        return R.ok(deviceService.deviceNumberCount());
    }


    /**
     * 一张图设备预警统计     各设备类型名+数量
     * @Param monitorType  1是隐患点 2是风险区  不填是所有
     * @return
     */
    @PostMapping("/statistics/warningLevelType")
    public R<List<EchartsProperty>> statisticsByWarningLevelType(@RequestParam(required = false) Integer monitorType) {
        return  R.ok(deviceService.selectStatisticsByWarningLevelType(monitorType));
    }



    /**
     * 设备分组   隐患点和风险区的设备分组    设备制造商的分组
     *
     * @return
     */
    @PostMapping("/group/allGroup")
    public R<DevicesAllGroupVO> allGroup() {
        return R.ok(deviceService.allGroup());
    }

    /**
     * 获取监测设备详细信息
     */
    @GetMapping(value = "/{id}")
    public R<DeviceVO> getInfo(@PathVariable("id") Long id) {
        return null;
        //return R.ok(deviceService.selectHgDevicesById(id));
    }

    /**
     * 新增监测设备
     */
    @PostMapping
    public R add(@RequestBody Devices devices) {
        return R.ok(deviceService.insertHgDevices(devices));
    }

    /**
     * 修改监测设备
     */
    @PutMapping
    public R edit(@RequestBody Devices devices) {
        return R.ok(deviceService.updateHgDevices(devices));
    }

    /**
     * 删除监测设备
     */
    @DeleteMapping("/{ids}")
    public R remove(@PathVariable Integer[] ids) {
        return R.ok(deviceService.deleteHgDevicesByIds(ids));
    }

    //=================设备维护部分===================

    /**
     * 新增设备维护
     */
    @PostMapping("/addMaintenance")
    public R addWeiHu(@RequestBody DevicesMaintenance devicesMaintenance) {
        devicesMaintenance.setTime(new Date());
        devicesMaintenance.insert();
        return R.ok();
    }

    /**
     * 根据设备id查询设备维护
     *
     * @param deviceId
     */
    @GetMapping("/getMaintenanceList")
    public R<List<DevicesMaintenance>> list(@RequestParam() Long deviceId) {
        QueryWrapper<DevicesMaintenance> wrapper = new QueryWrapper<>();
        wrapper.eq("device_id", deviceId);
        List<DevicesMaintenance> devicesMaintenances = devicesMaintenanceMapper.selectList(wrapper);
        return R.ok(devicesMaintenances);
    }

}
