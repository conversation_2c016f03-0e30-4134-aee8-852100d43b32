package com.pig4cloud.pigx.gis.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.common.log.annotation.SysLog;
import com.pig4cloud.pigx.gis.entity.MonitorPoint;
import com.pig4cloud.pigx.gis.service.MonitorPointService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.pig4cloud.pigx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 监测点数据
 *
 * <AUTHOR>
 * @date 2025-08-25 09:44:40
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/disaster/monitorPoint" )
@Tag(description = "MonitorPoint" , name = "监测点数据管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class MonitorPointController {

    private final  MonitorPointService monitorPointService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param monitorPoint 监测点数据
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('gis_MonitorPoint_view')" )
    public R getMonitorPointPage(@ParameterObject Page page, @ParameterObject MonitorPoint monitorPoint) {
        LambdaQueryWrapper<MonitorPoint> wrapper = Wrappers.lambdaQuery();
        return R.ok(monitorPointService.page(page, wrapper));
    }


    /**
     * 通过id查询监测点数据
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('gis_MonitorPoint_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(monitorPointService.getById(id));
    }

    /**
     * 新增监测点数据
     * @param monitorPoint 监测点数据
     * @return R
     */
    @Operation(summary = "新增监测点数据" , description = "新增监测点数据" )
    @SysLog("新增监测点数据" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('gis_MonitorPoint_add')" )
    public R save(@RequestBody MonitorPoint monitorPoint) {
        return R.ok(monitorPointService.save(monitorPoint));
    }

    /**
     * 修改监测点数据
     * @param monitorPoint 监测点数据
     * @return R
     */
    @Operation(summary = "修改监测点数据" , description = "修改监测点数据" )
    @SysLog("修改监测点数据" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('gis_MonitorPoint_edit')" )
    public R updateById(@RequestBody MonitorPoint monitorPoint) {
        return R.ok(monitorPointService.updateById(monitorPoint));
    }

    /**
     * 通过id删除监测点数据
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除监测点数据" , description = "通过id删除监测点数据" )
    @SysLog("通过id删除监测点数据" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('gis_MonitorPoint_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(monitorPointService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param monitorPoint 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('gis_MonitorPoint_export')" )
    public List<MonitorPoint> export(MonitorPoint monitorPoint, Long[] ids) {
        return monitorPointService.list(Wrappers.lambdaQuery(monitorPoint).in(ArrayUtil.isNotEmpty(ids), MonitorPoint::getId, ids));
    }
}