package com.pig4cloud.pigx.gis.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.gis.domain.PatrolRecord;
import com.pig4cloud.pigx.gis.service.PatrolRecordService;
import com.pig4cloud.pigx.gis.vo.PatrolRecordVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;


/**
 * 巡查记录模块
 */

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/disaster/patrolRecord")
@Tag(description = "patrolRecord", name = "巡查记录")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class PatrolRecordController {

	@Resource
	private PatrolRecordService patrolRecordService;

	/**
	 * 分页查询巡查记录
	 *
	 * @param page 分页对象
	 * @param patrolRecordVO 查询条件
	 * @return 分页结果
	 */
	@GetMapping("/page")
	@Operation(summary = "分页查询巡查记录", description = "分页查询巡查记录列表")
	public R<Page<PatrolRecord>> page(@ParameterObject Page<PatrolRecord> page,
									  @ParameterObject PatrolRecordVO patrolRecordVO) {
		Page<PatrolRecord> result = patrolRecordService.selectPatrolRecordPage(page, patrolRecordVO);
		return R.ok(result);
	}

	/**
	 * 查询所有巡查记录
	 *
	 * @param patrolRecordVO 查询条件
	 * @return 巡查记录列表
	 */
	@GetMapping("/list")
	@Operation(summary = "查询巡查记录列表", description = "查询所有巡查记录列表")
	public R<List<PatrolRecord>> list(@ParameterObject PatrolRecordVO patrolRecordVO) {
		List<PatrolRecord> result = patrolRecordService.selectPatrolRecordList(patrolRecordVO);
		return R.ok(result);
	}

	/**
	 * 根据ID查询巡查记录详情
	 *
	 * @param id 巡查记录ID
	 * @return 巡查记录详情
	 */
	@GetMapping("/{id}")
	@Operation(summary = "查询巡查记录详情", description = "根据ID查询巡查记录详情")
	public R<PatrolRecord> getById(@PathVariable("id") Long id) {
		PatrolRecord patrolRecord = patrolRecordService.getById(id);
		return R.ok(patrolRecord);
	}

	/**
	 * 新增巡查记录
	 *
	 * @param patrolRecord 巡查记录信息
	 * @return 操作结果
	 */
	@PostMapping
	@Operation(summary = "新增巡查记录", description = "新增巡查记录")
	public R<Void> add(@RequestBody PatrolRecord patrolRecord) {
		log.debug("新增巡查记录：{}", patrolRecord);
		patrolRecordService.insertPatrolRecord(patrolRecord);
		return R.ok();
	}

	/**
	 * 修改巡查记录
	 *
	 * @param patrolRecord 巡查记录信息
	 * @return 操作结果
	 */
	@PutMapping
	@Operation(summary = "修改巡查记录", description = "修改巡查记录")
	public R<Void> edit(@RequestBody PatrolRecord patrolRecord) {
		log.debug("修改巡查记录：{}", patrolRecord);
		patrolRecordService.updatePatrolRecord(patrolRecord);
		return R.ok();
	}

	/**
	 * 批量删除巡查记录
	 *
	 * @param ids 巡查记录ID数组
	 * @return 操作结果
	 */
	@DeleteMapping("/{ids}")
	@Operation(summary = "删除巡查记录", description = "批量删除巡查记录")
	public R<Void> remove(@PathVariable Long[] ids) {
		log.debug("删除巡查记录，IDs：{}", Arrays.toString(ids));
		List<Long> idList = Arrays.asList(ids);
		patrolRecordService.removeByIds(idList);
		return R.ok();
	}

}
