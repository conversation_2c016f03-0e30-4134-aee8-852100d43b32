package com.pig4cloud.pigx.gis.controller;

import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.gis.service.DangerPointsService;
import com.pig4cloud.pigx.gis.service.RiskZoneService;
import com.pig4cloud.pigx.gis.vo.DangerPointsQrCodeVO;
import com.pig4cloud.pigx.gis.vo.RiskZoneQrCodeVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 二维码扫描数据
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Slf4j
@RestController
@RequestMapping("/disaster/qrCode")
public class QrCodeController {

    @Resource
    private RiskZoneService riskZoneService;

    @Resource
    private DangerPointsService dangerPointsService;


    /**
     * 获取风险区二维码信息
     *
     * @param id
     */
    @GetMapping("/detail/riskZone/{id}")
    public R<RiskZoneQrCodeVO> getQrRiskZoneInfo(@PathVariable("id") String id) {
        RiskZoneQrCodeVO result = riskZoneService.getInfo(Integer.valueOf(id));
        return R.ok(result);
    }


    /**
     * 获取隐患点二维码信息
     *
     * @param id
     */
    @GetMapping("/detail/dangerPoints/{id}")
    public R<DangerPointsQrCodeVO> getDangerPointsInfo(@PathVariable("id") String id) {
        DangerPointsQrCodeVO result = dangerPointsService.getInfo(id);
        return R.ok(result);
    }


}
