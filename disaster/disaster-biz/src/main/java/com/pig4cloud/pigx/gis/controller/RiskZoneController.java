package com.pig4cloud.pigx.gis.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.gis.domain.EchartsProperty;
import com.pig4cloud.pigx.gis.domain.RiskZone;
import com.pig4cloud.pigx.gis.param.RiskZonePictureParam;
import com.pig4cloud.pigx.gis.service.RiskZoneService;
import com.pig4cloud.pigx.gis.vo.RiskZoneVO;
import com.pig4cloud.pigx.gis.vo.RiskZoneStatisticsVO;
import com.pig4cloud.pigx.gis.vo.RiskZonesCountVO;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.util.List;

/**
 * 风险区服务接口
 *
 * <AUTHOR>
 * @date 2025年6月2日10:10:13
 */

@Slf4j
@RestController
@RequestMapping("/disaster/riskZone")
public class RiskZoneController {

    @Autowired
    private RiskZoneService riskZoneService;


    /**
     * 导出地质灾害风险区管控清单Word
     *
     * @param id
     * @param response
     * @throws Exception
     */
    @GetMapping("/exportControlListFile/{id}")
    public void exportControlListFile(@PathVariable Integer id, HttpServletResponse response) {
        riskZoneService.exportControlListFile(id, response);
    }


    /**
     * 导出地地质灾害风险区转移避险明白卡Word
     *
     * @param id
     * @param response
     * @throws Exception
     */
    @GetMapping("/exportAvoidRiskFile/{id}")
    public void exportAvoidRiskFile(@PathVariable Integer id, HttpServletResponse response) {
        riskZoneService.exportAvoidRiskFile(id, response);
    }


    /**
     * 分页查询风险区列表
     *
     * @param pageNum
     * @param pageSize
     * @param riskZoneVO
     */
    @GetMapping("/page")
    @ApiOperation("分页查询风险区列表")
    public R<IPage<RiskZone>> list(@RequestParam(defaultValue = "1") Long pageNum,
                                   @RequestParam(defaultValue = "10") Long pageSize,
                                   RiskZoneVO riskZoneVO) {
        IPage<RiskZone> list = riskZoneService.selectList(new Page<>(pageNum, pageSize), riskZoneVO);
        return R.ok(list);
    }


    /**
     * 查询风险区列表
     *
     * @param riskZoneVO
     */
    @GetMapping("/list/all")
    @ApiOperation("查询风险区列表")
    public R<List<RiskZone>> listAll(RiskZoneVO riskZoneVO) {
        // 设置pageSize为负数，则不分页
        IPage<RiskZone> page = riskZoneService.selectList(new Page<>(1, Integer.MAX_VALUE), riskZoneVO);
        return R.ok(page.getRecords());
    }


    /**
     * 风险区统计    总数 重点管控区  次重点管控区
     *
     * @return
     */
    @GetMapping("/getStatisticsByFj")
    @ApiOperation("风险区统计")
    public R<RiskZoneStatisticsVO> getStatisticsByFj() {
        return R.ok(riskZoneService.selectStatisticsByFj(new RiskZoneVO()));
    }


    /**
     * 风险区政区分部统计
     *
     * @return
     */

    @GetMapping("/areaCount")
    public R<List<RiskZonesCountVO>> getAreaDangerPointCount() {
        return R.ok(riskZoneService.getRiskZoneCount());
    }


    /**
     * 所属乡镇分类统计
     *
     * @param riskZoneVO List<EchartsProperty>
     * @return propertyList
     */
    @GetMapping("/statistics/xz")
    public R statisticsByXz(RiskZoneVO riskZoneVO) {
        List<EchartsProperty> propertyList = riskZoneService.selectStatisticsByXz(riskZoneVO);
        return R.ok(propertyList);
    }


    /**
     * 获取分级名称分组     重点管控区or次重点管控区
     *
     * @return
     */
    @GetMapping("/group/fj")
    public R groupByFj() {
        return R.ok(riskZoneService.selectGroupByFj());
    }


    /**
     * 获取区县名称分组     市用户查看全部    区县看本县   乡镇和村村啥也没有
     *
     * @return
     */
    @GetMapping("/group/qx")
    public R groupByQx() {
        return R.ok(riskZoneService.selectGroupByQx());
    }


    /**
     * 获取乡镇名称分组     区看该区下所有乡镇分组     县用户查看该县下所有乡镇分组   村啥也没有
     *
     * @return
     */
    @GetMapping("/group/xz")
    public R groupByXz() {
        return R.ok(riskZoneService.selectGroupByXz());
    }


    /**
     * 通过参数xz乡镇  获取其行政村名称分组   如果村用户只能看自己村的
     *
     * @param xz
     * @return
     */
    @GetMapping("/group/xzc")
    public R groupByFj(String xz) {
        return R.ok(riskZoneService.selectGroupByXzc(xz));
    }


    /**
     * 新增风险区图片详情
     */
    @PostMapping("/editPicture")
    public R editPicture(@RequestBody RiskZonePictureParam riskZonePictureParam) {
        riskZoneService.editPicture(riskZonePictureParam);
        return R.ok();
    }


    /**
     * 获取风险区详细信息
     */
    @GetMapping(value = "/{id}")
    public R getInfo(@PathVariable("id") Integer id) {
        return R.ok(riskZoneService.selectById(id));
    }


    /**
     * 新增风险区
     */
    @PostMapping
    public R add(@RequestBody RiskZone riskZone) {
        return R.ok(riskZoneService.insert(riskZone));
    }


    /**
     * 修改风险区
     */

    @PutMapping
    public R edit(@RequestBody RiskZone riskZone) {
        return R.ok(riskZoneService.update(riskZone));
    }


    /**
     * 删除风险区
     */
    @DeleteMapping("/{ids}")
    public R remove(@PathVariable Integer[] ids) {
        return R.ok(riskZoneService.deleteByIds(ids));
    }


}
