package com.pig4cloud.pigx.gis.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.gis.domain.RiskZoneThreatenedPeople;
import com.pig4cloud.pigx.gis.service.RiskZoneThreatenedPeopleService;
import com.pig4cloud.pigx.gis.vo.RiskZoneThreatenedPeopleVO;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 风险区受灾人员管理
 *
 * <AUTHOR>
 * @date 2025年6月4日09:27:01
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/disaster/riskPeople")
@Tag(description = "riskPeople", name = "风险区人员模块")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class RiskZoneThreatenedPeopleController {


	@Autowired
	private RiskZoneThreatenedPeopleService riskZoneThreatenedPeopleService;




	/**
	 * 分页查询风险区  人员
	 *
	 * @param currentPageCount
	 * @param pageSize
	 * @param riskZoneId   风险区id
	 * @return
	 */
	@GetMapping("/getList")
	public R<RiskZoneThreatenedPeopleVO> getTagDataList(@RequestParam(defaultValue = "1") Long currentPageCount,
														@RequestParam(defaultValue = "10") Long pageSize,
														@RequestParam(required = true) Integer riskZoneId) {
		RiskZoneThreatenedPeopleVO gitList = riskZoneThreatenedPeopleService.getList(new Page<RiskZoneThreatenedPeople>(currentPageCount, pageSize), riskZoneId);
		return R.ok(gitList);
	}


	/**
	 * 新增风险区  人员
	 *
	 * @param riskZoneThreatenedPeople
	 * @return
	 */
	@PostMapping("/add")
	public R add(@RequestBody RiskZoneThreatenedPeople riskZoneThreatenedPeople) {
		log.debug("新增数据：{}", riskZoneThreatenedPeople);
		riskZoneThreatenedPeopleService.add(riskZoneThreatenedPeople);
		return R.ok();
	}


	/**
	 * 修改风险区 人员
	 * @param riskZoneThreatenedPeople
	 * @return
	 */

	@PostMapping("/edit")
	public R edit(@RequestBody RiskZoneThreatenedPeople riskZoneThreatenedPeople){
		log.debug("修改数据：{}", riskZoneThreatenedPeople);
		riskZoneThreatenedPeopleService.edit(riskZoneThreatenedPeople);
		return R.ok();
	}

	/**
	 * 批量删除风险区 人员
	 */
	@DeleteMapping("/{ids}")
	public R remove(@PathVariable Long[] ids) {
		for (Long id : ids) {
			System.out.println("id为:" + id);
		}
		//将ids数组转为集合
		List<Long> idList = Arrays.asList(ids);
		return R.ok(riskZoneThreatenedPeopleService.removeByIds(idList));
	}


}
