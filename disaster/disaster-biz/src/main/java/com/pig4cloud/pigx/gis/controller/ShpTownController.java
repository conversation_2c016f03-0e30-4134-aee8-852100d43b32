package com.pig4cloud.pigx.gis.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.gis.domain.ShpTown;
import com.pig4cloud.pigx.gis.service.ShpTownService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.annotation.Resource;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 乡镇边界数据Controller
 *
 * <AUTHOR>
 * @date 2025年8月1日10:44:02
 */
@Api(value = "【乡镇边界数据】服务接口", tags = {"【乡镇边界数据】服务接口"})
@RestController
@RequestMapping("/disaster/town")
public class ShpTownController {


	@Resource
	private ShpTownService shpTownService;

	@ApiOperation("分页查询乡镇边界数据")
	@PostMapping("/page")
	public R<Page<ShpTown>> page(@RequestParam(defaultValue = "1") Long pageNo,
								 @RequestParam(defaultValue = "10") Long pageSize,
								 @ParameterObject ShpTown shpTown) {
		Page<ShpTown> page = shpTownService.selectShpTownList(new Page<ShpTown>(pageNo, pageSize), shpTown);
		return R.ok(page);
	}



}
