package com.pig4cloud.pigx.gis.controller;

import cn.hutool.core.util.IdUtil;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


import java.io.*;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 门户-文件上传
 */
@Slf4j
@RestController
@RequestMapping("/disaster/portal/upload")
public class UploadFileController {
/*
    @Value("${ftp.uploadConf.tempFtpPath}")
    private String tempFtpPath;

    @Value("${minio.bucketName}")
    private String bucketName;

    @Resource
    private PortalDocumentInfoMapper portalDocumentInfoMapper;

    *//**
     * 常用的文件上传格式
     *//*
    private static final String[] fileTypeArr = new String[]{"jpg", "jpeg", "png", "gif", "bmp", "tif", "tiff", "ico",
            "webp","mp3", "wav", "mp4", "avi", "wmv", "flv", "doc","docx","xls","xlsx","csv","ppt", "pdf"};

    *//**
     * 上传文件(除三维模型外的其他文件)
     * @param file
     * @return
     *//*
    @PostMapping(value = "/upload/file")
    public R<PortalDocumentInfo> upload(@RequestParam(value = "file") MultipartFile file) {

        String fileName = file.getOriginalFilename();
        if (StringUtils.isBlank(fileName)) {
            throw new RuntimeException("文件名称不能为空！");
        }
        String fileFormat = fileName.substring(fileName.lastIndexOf('.') + 1);
        // 限制文件上传类型
        if(!Arrays.asList(fileTypeArr).contains(fileFormat)){
            throw new RuntimeException("不支持的文件扩展名！");
        }
        try {
            String saveFileName = RandomStringUtils.randomAlphanumeric(32) + "_" + fileName;
            String baseUrlStr = DateUtils.getDate() + MinioUtils.SEPARATOR + saveFileName;
            MinioUtils.putObject(bucketName, file, baseUrlStr, file.getContentType());
            PortalDocumentInfo portalDocumentInfo = new PortalDocumentInfo();
            portalDocumentInfo.setResSourceName(fileName);
            portalDocumentInfo.setResSaveName(saveFileName);
            portalDocumentInfo.setResPath(baseUrlStr);
            portalDocumentInfo.setResSize(file.getSize());
            PigxUser user = SecurityUtils.getUser();
            portalDocumentInfo.setCreator(user.getId());
            portalDocumentInfo.setModifier(user.getId());
            portalDocumentInfoMapper.insert(portalDocumentInfo);
            return R.ok(portalDocumentInfo);
        } catch (Exception e) {
            throw new RuntimeException("上传失败！");
        }
    }

    *//**
     * 上传三维模型文件
     * @param multipartFile
     * @return
     * @throws IOException
     *//*
    @PostMapping("/uploadModel")
    public R<String> uploadModel(@RequestParam(value = "multipartFile") MultipartFile multipartFile) throws IOException {
        String fileName = getRealFileName(multipartFile);
        if (StringUtils.isBlank(fileName) || !fileName.endsWith("zip")) {
            return R.failed("文件格式不正确，只能上传zip文件包！");
        }
        String saveName = IdUtil.simpleUUID() + "_" + fileName;
        String path = tempFtpPath + File.separator + saveName;
        File dest = new File(path);
        if (!dest.getParentFile().exists()) {
            dest.getParentFile().mkdirs();
        }

        InputStream input = multipartFile.getInputStream();
        File realFile = new File(path);
        OutputStream bos = Files.newOutputStream(realFile.toPath());
        int len;
        byte[] buf = new byte[1024];
        while ((len = input.read(buf)) != -1) {
            bos.write(buf, 0, len);
        }
        // 关流顺序，先打开的后关闭
        input.close();
        bos.close();
        return R.ok(saveName);
    }


    *//**
     * 编辑模型
     * @param file
     * @param filePath
     * @return
     *//*
    @PostMapping(value = "/editModelZip")
    public R<String> editModelZip(@RequestParam("file") MultipartFile file,
                                  @RequestParam("filePath") String filePath) {
        if (file == null) {
            return R.failed("编辑模型文件失败！");
        }
        // minio路径
        String realFilePath = filePath.substring(0,filePath.lastIndexOf("/")+1);
        // 本地服务器临时路径
        String temporaryPath = tempFtpPath + File.separator + UUIDUtils.generateUUID();
        try {
            ZipInputStream inZip = new ZipInputStream(file.getInputStream(), Charset.forName("GBK"));
            ZipEntry entry;
            while ((entry = inZip.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    String zipEntryName = entry.getName();
                    File realFile = new File((temporaryPath+"/"+zipEntryName).replace("/",File.separator));
                    if (!realFile.getParentFile().exists()) {
                        realFile.getParentFile().mkdirs();
                    }
                    OutputStream bos = Files.newOutputStream(realFile.toPath());
                    int len;
                    byte[] buf = new byte[1024];
                    while ((len = inZip.read(buf)) != -1) {
                        bos.write(buf, 0, len);
                    }
                    bos.close();
                }
            }

            // 将新的模型文件上传到minio,同名文件覆盖
            File temporaryFile = new File(temporaryPath);
            File[] files = temporaryFile.listFiles();
            // 逐个文件上传
            assert files != null;
            for (File file2 : files) {
                if (file2.isFile()) {
                    InputStream inputStream = Files.newInputStream(file2.toPath());
                    String newFilePath  = realFilePath + file2.getName();
                    MinioUtils.putObject(bucketName, newFilePath, inputStream);
                }
            }
            // 删除临时文件
            if(temporaryFile.exists()){
                temporaryFile.delete();
            }
        }catch (Exception e){
            log.error("编辑model文件失败！", e);
        }
        return R.ok("编辑模型文件成功！");
    }

    public static String getRealFileName(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (StringUtils.isBlank(filename)) {
            return null;
        }
        int unixSep = filename.lastIndexOf('/');
        int winSep = filename.lastIndexOf('\\');
        int pos = (Math.max(winSep, unixSep));
        if (pos != -1) {
            filename = filename.substring(pos + 1);
        }
        return filename;
    }*/
}
