package com.pig4cloud.pigx.gis.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.gis.service.WarningRuleTemplateService;
import com.pig4cloud.pigx.gis.vo.WarningRuleTemplateRequestVO;
import com.pig4cloud.pigx.gis.vo.WarningRuleTemplateResponseVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;



/**
 * 后台预警规则模板
 *
 * <AUTHOR>
 * @date 2025年6月5日14:19:35
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/disaster/warningRuleTemplate")
@Tag(description = "warningRuleTemplate", name = "预警规则")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class WarningRuleTemplateController {


	@Resource
	private WarningRuleTemplateService warningRuleTemplateService;

	/**
	 * 分页预警规则
	 *
	 * @param pageNo
	 * @param pageSize
	 * @param ruleName   预警规则名称
	 * @return
	 */
	@GetMapping("/getWaringRuleTemplate")
	public R<IPage<WarningRuleTemplateResponseVO>> getList(@RequestParam(defaultValue = "1") Long pageNo,
														   @RequestParam(defaultValue = "10") Long pageSize,
														   @RequestParam(required = false) String ruleName) {
		IPage<WarningRuleTemplateResponseVO> waringRuleTemplate = warningRuleTemplateService.getWaringRuleTemplate(ruleName, pageNo, pageSize);
		return R.ok(waringRuleTemplate);
	}



	/**
	 * @param hgWarningRuleTemplateRequestVO
	 * 新增预警规则配置模板
	 */
	@PostMapping("/addWaringConfigTemplate")
	@ApiOperation(value = "新增预警配置模板")
	public R<Integer> add(@RequestBody WarningRuleTemplateRequestVO hgWarningRuleTemplateRequestVO) {
		Integer integer = warningRuleTemplateService.addWaringRuleTemplate(hgWarningRuleTemplateRequestVO);
		return R.ok(integer);
	}


	/**
	 * 修改预警规则
	 * @param hgWarningRuleTemplateRequestVO
	 * @return
	 */
	@PostMapping("/updateWaringConfigTemplate")
	@ApiOperation(value = "修改预警配置模板")
	public R<Integer> update(@RequestBody WarningRuleTemplateRequestVO hgWarningRuleTemplateRequestVO) {
		Integer integer = warningRuleTemplateService.updateWaringRuleTemplate(hgWarningRuleTemplateRequestVO);
		return R.ok(integer);
	}


	/**
	 * 删除预警配置模板
	 */
	@DeleteMapping("/deleteWaringConfigTemplate")
	@ApiOperation(value = "删除预警配置模板")
	public R<Integer> deleteWaringRuleTemplate(@Validated @NonNull @RequestParam("code") String code) {
		Integer integer = warningRuleTemplateService.deleteWaringRuleTemplate(code);
		return R.ok(integer);
	}

}
