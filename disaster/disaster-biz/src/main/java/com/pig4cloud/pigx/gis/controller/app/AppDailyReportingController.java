package com.pig4cloud.pigx.gis.controller.app;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.common.log.annotation.SysLog;
import com.pig4cloud.pigx.gis.entity.AppDailyReporting;
import com.pig4cloud.pigx.gis.service.AppDailyReportingService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.pig4cloud.pigx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 日常巡排查
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/dailyReporting" )
@Tag(description = "appDailyReporting" , name = "日常巡排查管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class AppDailyReportingController {

    private final  AppDailyReportingService appDailyReportingService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param appDailyReporting 日常巡排查
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('gis_appDailyReporting_view')" )
    public R getAppDailyReportingPage(@ParameterObject Page page, @ParameterObject AppDailyReporting appDailyReporting) {
        LambdaQueryWrapper<AppDailyReporting> wrapper = Wrappers.lambdaQuery();
        return R.ok(appDailyReportingService.page(page, wrapper));
    }


    /**
     * 通过id查询日常巡排查
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('gis_appDailyReporting_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(appDailyReportingService.getById(id));
    }

    /**
     * 新增日常巡排查
     * @param appDailyReporting 日常巡排查
     * @return R
     */
    @Operation(summary = "新增日常巡排查" , description = "新增日常巡排查" )
    @SysLog("新增日常巡排查" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('gis_appDailyReporting_add')" )
    public R save(@RequestBody AppDailyReporting appDailyReporting) {
        return R.ok(appDailyReportingService.save(appDailyReporting));
    }

    /**
     * 修改日常巡排查
     * @param appDailyReporting 日常巡排查
     * @return R
     */
    @Operation(summary = "修改日常巡排查" , description = "修改日常巡排查" )
    @SysLog("修改日常巡排查" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('gis_appDailyReporting_edit')" )
    public R updateById(@RequestBody AppDailyReporting appDailyReporting) {
        return R.ok(appDailyReportingService.updateById(appDailyReporting));
    }

    /**
     * 通过id删除日常巡排查
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除日常巡排查" , description = "通过id删除日常巡排查" )
    @SysLog("通过id删除日常巡排查" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('gis_appDailyReporting_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(appDailyReportingService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param appDailyReporting 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('gis_appDailyReporting_export')" )
    public List<AppDailyReporting> export(AppDailyReporting appDailyReporting, Long[] ids) {
        return appDailyReportingService.list(Wrappers.lambdaQuery(appDailyReporting).in(ArrayUtil.isNotEmpty(ids), AppDailyReporting::getId, ids));
    }
}