package com.pig4cloud.pigx.gis.controller.app;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.common.log.annotation.SysLog;
import com.pig4cloud.pigx.gis.entity.AppDailyReportingFile;
import com.pig4cloud.pigx.gis.service.AppDailyReportingFileService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.pig4cloud.pigx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 日常巡排查附件
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/dailyReportingFile" )
@Tag(description = "appDailyReportingFile" , name = "日常巡排查附件管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class AppDailyReportingFileController {

    private final  AppDailyReportingFileService appDailyReportingFileService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param appDailyReportingFile 日常巡排查附件
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('gis_appDailyReportingFile_view')" )
    public R getAppDailyReportingFilePage(@ParameterObject Page page, @ParameterObject AppDailyReportingFile appDailyReportingFile) {
        LambdaQueryWrapper<AppDailyReportingFile> wrapper = Wrappers.lambdaQuery();
        return R.ok(appDailyReportingFileService.page(page, wrapper));
    }


    /**
     * 通过id查询日常巡排查附件
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('gis_appDailyReportingFile_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(appDailyReportingFileService.getById(id));
    }

    /**
     * 新增日常巡排查附件
     * @param appDailyReportingFile 日常巡排查附件
     * @return R
     */
    @Operation(summary = "新增日常巡排查附件" , description = "新增日常巡排查附件" )
    @SysLog("新增日常巡排查附件" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('gis_appDailyReportingFile_add')" )
    public R save(@RequestBody AppDailyReportingFile appDailyReportingFile) {
        return R.ok(appDailyReportingFileService.save(appDailyReportingFile));
    }

    /**
     * 修改日常巡排查附件
     * @param appDailyReportingFile 日常巡排查附件
     * @return R
     */
    @Operation(summary = "修改日常巡排查附件" , description = "修改日常巡排查附件" )
    @SysLog("修改日常巡排查附件" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('gis_appDailyReportingFile_edit')" )
    public R updateById(@RequestBody AppDailyReportingFile appDailyReportingFile) {
        return R.ok(appDailyReportingFileService.updateById(appDailyReportingFile));
    }

    /**
     * 通过id删除日常巡排查附件
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除日常巡排查附件" , description = "通过id删除日常巡排查附件" )
    @SysLog("通过id删除日常巡排查附件" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('gis_appDailyReportingFile_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(appDailyReportingFileService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param appDailyReportingFile 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('gis_appDailyReportingFile_export')" )
    public List<AppDailyReportingFile> export(AppDailyReportingFile appDailyReportingFile, Long[] ids) {
        return appDailyReportingFileService.list(Wrappers.lambdaQuery(appDailyReportingFile).in(ArrayUtil.isNotEmpty(ids), AppDailyReportingFile::getId, ids));
    }
}