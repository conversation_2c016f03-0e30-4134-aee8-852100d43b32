package com.pig4cloud.pigx.gis.controller.app;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.common.log.annotation.SysLog;
import com.pig4cloud.pigx.gis.dto.AppDisasterReportingAddDTO;
import com.pig4cloud.pigx.gis.dto.AppDisasterReportingUpdateDTO;
import com.pig4cloud.pigx.gis.entity.AppDisasterReporting;
import com.pig4cloud.pigx.gis.service.AppDisasterReportingService;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import com.pig4cloud.pigx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 一键报灾
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/disasterReporting" )
@Tag(description = "appDisasterReporting" , name = "一键报灾管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class AppDisasterReportingController {

    private final  AppDisasterReportingService disasterReportingService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param appDisasterReporting 一键报灾
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReporting_view')" )
    public R getAppDisasterReportingPage(@ParameterObject Page page, @ParameterObject AppDisasterReporting appDisasterReporting) {
        LambdaQueryWrapper<AppDisasterReporting> wrapper = Wrappers.lambdaQuery();
        return R.ok(disasterReportingService.page(page, wrapper));
    }


    /**
     * 通过id查询一键报灾
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReporting_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(disasterReportingService.getById(id));
    }

    /**
     * 新增一键报灾
     * @param addDTO 一键报灾
     * @return R
     */
    @Operation(summary = "新增一键报灾" , description = "新增一键报灾" )
    @SysLog("新增一键报灾" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReporting_add')" )
    public R save(@Valid @RequestBody AppDisasterReportingAddDTO addDTO) {
        return R.ok(disasterReportingService.addDisasterReporting(addDTO));
    }

    /**
     * 修改一键报灾
     * @param updateDTO 一键报灾
     * @return R
     */
    @Operation(summary = "修改一键报灾" , description = "修改一键报灾" )
    @SysLog("修改一键报灾" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReporting_edit')" )
    public R updateById(@RequestBody AppDisasterReportingUpdateDTO updateDTO) {
        return R.ok(disasterReportingService.updateDisasterReporting(updateDTO));
    }

    /**
     * 通过id删除一键报灾
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除一键报灾" , description = "通过id删除一键报灾" )
    @SysLog("通过id删除一键报灾" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReporting_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(disasterReportingService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param appDisasterReporting 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReporting_export')" )
    public List<AppDisasterReporting> export(AppDisasterReporting appDisasterReporting, Long[] ids) {
        return disasterReportingService.list(Wrappers.lambdaQuery(appDisasterReporting).in(ArrayUtil.isNotEmpty(ids), AppDisasterReporting::getId, ids));
    }
}