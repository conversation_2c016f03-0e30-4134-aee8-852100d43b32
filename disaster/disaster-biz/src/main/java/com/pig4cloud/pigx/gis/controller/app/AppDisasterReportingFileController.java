package com.pig4cloud.pigx.gis.controller.app;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.common.log.annotation.SysLog;
import com.pig4cloud.pigx.gis.entity.AppDisasterReportingFile;
import com.pig4cloud.pigx.gis.service.AppDisasterReportingFileService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.pig4cloud.pigx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 一键报灾附件
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/disasterReportingFile" )
@Tag(description = "appDisasterReportingFile" , name = "一键报灾附件管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class AppDisasterReportingFileController {

    private final  AppDisasterReportingFileService appDisasterReportingFileService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param appDisasterReportingFile 一键报灾附件
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReportingFile_view')" )
    public R getAppDisasterReportingFilePage(@ParameterObject Page page, @ParameterObject AppDisasterReportingFile appDisasterReportingFile) {
        LambdaQueryWrapper<AppDisasterReportingFile> wrapper = Wrappers.lambdaQuery();
        return R.ok(appDisasterReportingFileService.page(page, wrapper));
    }


    /**
     * 通过id查询一键报灾附件
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReportingFile_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(appDisasterReportingFileService.getById(id));
    }

    /**
     * 新增一键报灾附件
     * @param appDisasterReportingFile 一键报灾附件
     * @return R
     */
    @Operation(summary = "新增一键报灾附件" , description = "新增一键报灾附件" )
    @SysLog("新增一键报灾附件" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReportingFile_add')" )
    public R save(@RequestBody AppDisasterReportingFile appDisasterReportingFile) {
        return R.ok(appDisasterReportingFileService.save(appDisasterReportingFile));
    }

    /**
     * 修改一键报灾附件
     * @param appDisasterReportingFile 一键报灾附件
     * @return R
     */
    @Operation(summary = "修改一键报灾附件" , description = "修改一键报灾附件" )
    @SysLog("修改一键报灾附件" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReportingFile_edit')" )
    public R updateById(@RequestBody AppDisasterReportingFile appDisasterReportingFile) {
        return R.ok(appDisasterReportingFileService.updateById(appDisasterReportingFile));
    }

    /**
     * 通过id删除一键报灾附件
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除一键报灾附件" , description = "通过id删除一键报灾附件" )
    @SysLog("通过id删除一键报灾附件" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReportingFile_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(appDisasterReportingFileService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param appDisasterReportingFile 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReportingFile_export')" )
    public List<AppDisasterReportingFile> export(AppDisasterReportingFile appDisasterReportingFile, Long[] ids) {
        return appDisasterReportingFileService.list(Wrappers.lambdaQuery(appDisasterReportingFile).in(ArrayUtil.isNotEmpty(ids), AppDisasterReportingFile::getId, ids));
    }
}