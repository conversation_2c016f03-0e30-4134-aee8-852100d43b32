package com.pig4cloud.pigx.gis.controller.app;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.common.log.annotation.SysLog;
import com.pig4cloud.pigx.gis.entity.AppDisasterReportingReviewer;
import com.pig4cloud.pigx.gis.service.AppDisasterReportingReviewerService;
import org.springframework.security.access.prepost.PreAuthorize;
import com.pig4cloud.pigx.common.excel.annotation.ResponseExcel;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.http.HttpHeaders;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 一键报灾审阅人员
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/app/disasterReportingReviewer" )
@Tag(description = "appDisasterReportingReviewer" , name = "一键报灾审阅人员管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class AppDisasterReportingReviewerController {

    private final  AppDisasterReportingReviewerService appDisasterReportingReviewerService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param appDisasterReportingReviewer 一键报灾审阅人员
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @GetMapping("/page" )
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReportingReviewer_view')" )
    public R getAppDisasterReportingReviewerPage(@ParameterObject Page page, @ParameterObject AppDisasterReportingReviewer appDisasterReportingReviewer) {
        LambdaQueryWrapper<AppDisasterReportingReviewer> wrapper = Wrappers.lambdaQuery();
        return R.ok(appDisasterReportingReviewerService.page(page, wrapper));
    }


    /**
     * 通过id查询一键报灾审阅人员
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReportingReviewer_view')" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(appDisasterReportingReviewerService.getById(id));
    }

    /**
     * 新增一键报灾审阅人员
     * @param appDisasterReportingReviewer 一键报灾审阅人员
     * @return R
     */
    @Operation(summary = "新增一键报灾审阅人员" , description = "新增一键报灾审阅人员" )
    @SysLog("新增一键报灾审阅人员" )
    @PostMapping
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReportingReviewer_add')" )
    public R save(@RequestBody AppDisasterReportingReviewer appDisasterReportingReviewer) {
        return R.ok(appDisasterReportingReviewerService.save(appDisasterReportingReviewer));
    }

    /**
     * 修改一键报灾审阅人员
     * @param appDisasterReportingReviewer 一键报灾审阅人员
     * @return R
     */
    @Operation(summary = "修改一键报灾审阅人员" , description = "修改一键报灾审阅人员" )
    @SysLog("修改一键报灾审阅人员" )
    @PutMapping
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReportingReviewer_edit')" )
    public R updateById(@RequestBody AppDisasterReportingReviewer appDisasterReportingReviewer) {
        return R.ok(appDisasterReportingReviewerService.updateById(appDisasterReportingReviewer));
    }

    /**
     * 通过id删除一键报灾审阅人员
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除一键报灾审阅人员" , description = "通过id删除一键报灾审阅人员" )
    @SysLog("通过id删除一键报灾审阅人员" )
    @DeleteMapping
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReportingReviewer_del')" )
    public R removeById(@RequestBody Long[] ids) {
        return R.ok(appDisasterReportingReviewerService.removeBatchByIds(CollUtil.toList(ids)));
    }


    /**
     * 导出excel 表格
     * @param appDisasterReportingReviewer 查询条件
   	 * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @GetMapping("/export")
    @PreAuthorize("@pms.hasPermission('gis_appDisasterReportingReviewer_export')" )
    public List<AppDisasterReportingReviewer> export(AppDisasterReportingReviewer appDisasterReportingReviewer, Long[] ids) {
        return appDisasterReportingReviewerService.list(Wrappers.lambdaQuery(appDisasterReportingReviewer).in(ArrayUtil.isNotEmpty(ids), AppDisasterReportingReviewer::getId, ids));
    }
}