package com.pig4cloud.pigx.gis.controller.app;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.gis.domain.AppPatrolPlan;
import com.pig4cloud.pigx.gis.param.AppPatrolPlanParam;
import com.pig4cloud.pigx.gis.service.AppPatrolPlanService;
import com.pig4cloud.pigx.gis.service.DangerPointsService;
import com.pig4cloud.pigx.gis.service.RiskZoneService;
import com.pig4cloud.pigx.gis.vo.AppPatrolPlanVO;
import com.pig4cloud.pigx.gis.vo.TaskLocationVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 小程序巡查计划管理
 * <AUTHOR>
 * @date 2025-08-07
 */

@Slf4j
@RestController
@RequestMapping("/patrolPlan")
public class AppPatrolPlanController {

    @Resource
    private AppPatrolPlanService patrolPlanService;

    @Resource
    private DangerPointsService dangerPointsService;

    @Resource
    private RiskZoneService riskZoneService;


    /**
     * 根据政区 村查询隐患点或风险区   ☑️
     *
     * @param dangerOrRisk 1:隐患点  2:风险区
     * @param villageName  村名称
     * @return
     */
    @GetMapping("/taskLocation")
    public R<List<TaskLocationVO>> page(@RequestParam(required = true, defaultValue = "1") Long dangerOrRisk,
                                        @RequestParam(required = true) String villageName) {

        if (dangerOrRisk == 1) {
            return R.ok(dangerPointsService.selectTaskLocation(villageName));
        } else if (dangerOrRisk == 2) {
            return R.ok(riskZoneService.selectTaskLocation(villageName));
        } else {
            return R.failed("参数错误");
        }
    }


    /**
     * 创建巡查任务
     *
     * @param plan
     * @return
     */
    @PostMapping("/add")
    public R create(@RequestBody AppPatrolPlanParam plan) {
        if (plan.getPatrolPlan() == null ) {
            return R.failed("请输入完整数据");
        }
        List<String> pointIdList = plan.getPointIdList();
        if (pointIdList.isEmpty()) {
            return R.failed("隐患点或风险区数据不能为空");
        }
        Boolean hasPatrolPeople = patrolPlanService.selectPatrolPeople(plan.getPatrolPlan().getRegionId());
        if (!hasPatrolPeople) {
            return R.failed("当前村落没有巡查人员,请添加巡查人员");
        }

        patrolPlanService.add(pointIdList, plan.getPatrolPlan());
        return R.ok();
    }


    /**
     * 查询
     *
     * @param currentPageCount
     * @param pageSize
     * @param taskStatus       任务状态，1未开始，2执行中，3待查阅 4已完成
     * @param taskType         任务类型，1巡查任务，2预警任务
     * @param patrolType       巡查类型，1隐患点巡查，2风险区巡查
     * @return
     */
    @GetMapping("/patrolList")
    public R<List<AppPatrolPlanVO>> getpatrolList(@RequestParam(required = true, defaultValue = "1") Long currentPageCount,
                                                  @RequestParam(required = true, defaultValue = "10") Long pageSize,
                                                  @RequestParam(required = false) Integer taskStatus,
                                                  @RequestParam(required = false) Integer taskType,
                                                  @RequestParam(required = false) Integer patrolType,
                                                  @RequestParam(required = false) String startTime,
                                                  @RequestParam(required = false) String endTime) {
        List<AppPatrolPlanVO> result = patrolPlanService.getpatrolList(
                new Page<AppPatrolPlan>(currentPageCount, pageSize),
                taskStatus,
                taskType,
                patrolType,
                startTime,
                endTime
        );
        return R.ok(result);
    }


    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public R<AppPatrolPlan> getById(@PathVariable Long id) {
        return R.ok(patrolPlanService.getById(id));
    }


}
