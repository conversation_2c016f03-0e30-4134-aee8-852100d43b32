package com.pig4cloud.pigx.gis.controller.app;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.gis.param.AppPatrolTaskParam;
import com.pig4cloud.pigx.gis.vo.AppPatrolTaskVO;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.gis.domain.AppPatrolTask;
import com.pig4cloud.pigx.gis.service.AppPatrolTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 巡查任务管理
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Slf4j
@RestController
@RequestMapping("/app/patrolTask")
@Api(tags = "巡查任务管理")
public class AppPatrolTaskController {

    @Autowired
    private AppPatrolTaskService appPatrolTaskService;



    /**
     * 新增巡查任务
     */
    @PostMapping
    @ApiOperation("新增巡查任务")
    public R<Boolean> add(@ApiParam("巡查任务信息") @RequestBody AppPatrolTaskParam appPatrolTaskParam) {
        // 将字符串类型的 photoIds 转换为 Long 类型
        List<Long> photoLongIds = new ArrayList<>();
        if (appPatrolTaskParam.getPhotoIds() != null) {
            photoLongIds = appPatrolTaskParam.getPhotoIds().stream()
                    .map(id -> {
                        try {
                            return Long.parseLong(id);
                        } catch (NumberFormatException e) {
                            throw new IllegalArgumentException("无效的 photoId: " + id);
                        }
                    })
                    .collect(Collectors.toList());
        }

        boolean result = appPatrolTaskService.insertAppPatrolTask(photoLongIds,appPatrolTaskParam.getAppPatrolTask());
        return result ? R.ok(true) : R.failed("新增失败");
    }





    /**
     * 分页查询巡查任务列表
     */
    @GetMapping("/page")
    @ApiOperation("分页查询巡查任务列表")
    public R<List<AppPatrolTaskVO> > page(
            @ApiParam("当前页码") @RequestParam(defaultValue = "1") Long current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Long size,
            @ApiParam("巡查计划ID") @RequestParam(required = false) Long planId,
            @ApiParam("点位ID") @RequestParam(required = false) Long pointId,
            @ApiParam("点位类型") @RequestParam(required = false) Integer pointType,
            @ApiParam("是否存在风险") @RequestParam(required = false) Boolean riskFlag,
            @ApiParam("是否需要技术支撑") @RequestParam(required = false) Boolean technicalSupportFlag,
            @ApiParam("现场情况描述") @RequestParam(required = false) String description) {
        
        Page<AppPatrolTask> page = new Page<>(current, size);
        AppPatrolTask queryParam = new AppPatrolTask();
        queryParam.setPlanId(planId);
        queryParam.setPointId(pointId);
        queryParam.setPointType(pointType);
        queryParam.setRiskFlag(riskFlag);
        queryParam.setTechnicalSupportFlag(technicalSupportFlag);
        queryParam.setDescription(description);

        List<AppPatrolTaskVO> result = appPatrolTaskService.selectAppPatrolTaskPage(page, queryParam);
        return R.ok(result);
    }


    /**
     * 查询所有巡查任务列表（不分页）
     */
    @PostMapping("/list")
    @ApiOperation("查询所有巡查任务列表")
    public R<List<AppPatrolTaskVO>> list(@ApiParam("查询条件") @RequestBody(required = false) AppPatrolTask appPatrolTask) {
        if (appPatrolTask == null) {
            appPatrolTask = new AppPatrolTask();
        }

        Page<AppPatrolTask> page = new Page<>(1, Integer.MAX_VALUE);
        List<AppPatrolTaskVO> result = appPatrolTaskService.selectAppPatrolTaskPage(page, appPatrolTask);
        return R.ok(result);
    }




















    /**
     * 根据ID查询巡查任务详情
     */
    @GetMapping("/{id}")
    @ApiOperation("根据ID查询巡查任务详情")
    public R<AppPatrolTask> getById(@ApiParam("任务ID") @PathVariable Long id) {
        AppPatrolTask task = appPatrolTaskService.getById(id);
        return R.ok(task);
    }


    /**
     * 修改巡查任务
     */
    @PutMapping
    @ApiOperation("修改巡查任务")
    public R<Boolean> edit(@ApiParam("巡查任务信息") @RequestBody AppPatrolTask appPatrolTask) {
        boolean result = appPatrolTaskService.updateAppPatrolTask(appPatrolTask);
        return result ? R.ok(true) : R.failed("修改失败");
    }

    /**
     * 删除巡查任务
     */
    @DeleteMapping("/{ids}")
    @ApiOperation("删除巡查任务")
    public R<Boolean> remove(@ApiParam("任务ID数组") @PathVariable Long[] ids) {
        boolean result = appPatrolTaskService.deleteAppPatrolTaskByIds(ids);
        return result ? R.ok(true) : R.failed("删除失败");
    }

    /**
     * 根据巡查计划ID查询任务列表
     */
    @GetMapping("/plan/{planId}")
    @ApiOperation("根据巡查计划ID查询任务列表")
    public R<List<AppPatrolTask>> getByPlanId(@ApiParam("巡查计划ID") @PathVariable Long planId) {
        List<AppPatrolTask> list = appPatrolTaskService.selectByPlanId(planId);
        return R.ok(list);
    }

    /**
     * 根据点位ID查询任务列表
     */
    @GetMapping("/point/{pointId}")
    @ApiOperation("根据点位ID查询任务列表")
    public R<List<AppPatrolTask>> getByPointId(@ApiParam("点位ID") @PathVariable Long pointId) {
        List<AppPatrolTask> list = appPatrolTaskService.selectByPointId(pointId);
        return R.ok(list);
    }

    /**
     * 根据点位类型查询任务列表
     */
    @GetMapping("/pointType/{pointType}")
    @ApiOperation("根据点位类型查询任务列表")
    public R<List<AppPatrolTask>> getByPointType(@ApiParam("点位类型") @PathVariable Integer pointType) {
        List<AppPatrolTask> list = appPatrolTaskService.selectByPointType(pointType);
        return R.ok(list);
    }


}
