package com.pig4cloud.pigx.gis.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("app_patrol_plan")
public class AppPatrolPlan extends BaseEntity {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 同一周期任务组ID
	 */
	private String groupId;
	/**
	 * 巡查类型，1隐患点巡查，2风险区巡查
	 */
	private Integer patrolType;
	/**
	 * 任务类型，1巡查任务，2预警任务
	 */
	private Integer taskType;
	/**
	 * 所属政区，村ID
	 */
	private Long regionId;

	/**
	 * 所属政区  县政村拼接
	 */
	private String region;
	/**
	 * 紧急程度，1一般，2加急，3特急
	 */
	private Integer urgency;

	/**
	 * 风险区id(省编号)
	 */
	private String riskZoneId;

	/**
	 * 巡查人姓名
	 */
	private String patrolPeopleName;

	/**
	 * 预期完成日期 主要用于表示不带时间的日期（年、月、日）
	 */
	private LocalDate expectedCompletionDate;
	/**
	 * 任务执行周期-数字
	 */
	private Integer taskCycleNum;
	/**
	 * 任务执行周期单位-日周月
	 */
	private String taskCycleUnit;
	/**
	 *任务结束日期  主要用于表示不带时间的日期（年、月、日）
	 */
	private LocalDate taskEndDate;
	/**
	 * 任务描述
	 */
	private String description;
	/**
	 * 任务状态，1未开始，2执行中，3待查阅 4已完成
	 */
	private Integer taskStatus;
	/**
	 * 审批人ID
	 */
	private Long reviewerId;
	/**
	 * 审批时间
	 */
	private LocalDateTime approvalTime;

	/**
	 * 删除标志，0未删除，1已删除
	 */
	private String delFlag;
}
