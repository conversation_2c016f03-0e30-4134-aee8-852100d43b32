package com.pig4cloud.pigx.gis.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("app_patrol_plan_point")
public class AppPatrolPlanPoint {


	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;


	/**
	 * 巡查计划ID
	 */
	private Long planId;


	/**
	 * 巡查点ID
	 */
	private String pointId;

	/**
	 * 排序值，越小越靠前
	 */
	private Integer sortOrder;
}
