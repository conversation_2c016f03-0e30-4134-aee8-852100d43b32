package com.pig4cloud.pigx.gis.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("app_patrol_plan_reviewer")
@ApiModel(value = "AppPatrolPlanReviewer对象", description = "巡查任务审阅人员表")
public class AppPatrolPlanReviewer {


    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 计划ID
     */
    private Long planId;

    /**
     * 审阅人ID
     */
    private Long userId;


}
