package com.pig4cloud.pigx.gis.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("app_patrol_task")
@ApiModel(value = "AppPatrolTask对象", description = "巡查任务表")
public class AppPatrolTask implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 巡查计划ID
	 */
	private Long planId;

	/**
	 * 点位ID
	 */
	private Long pointId;

	/**
	 * 点位类型，1隐患点   3新增巡查点(没有2的原因是2代表的风险区)
	 */
	private Integer pointType;

	/**
	 * 排序值，越小越靠前
	 */
	private Integer sortOrder = 1;

	/**
	 * 经度
	 */
	private Double longitude;

	/**
	 * 纬度
	 */
	private Double latitude;

	/**
	 * 是否存在风险
	 */
	private Boolean riskFlag;

	/**
	 * 威胁住户数量
	 */
	private Integer threateningHousehold;

	/**
	 * 威胁人口数量
	 */
	private Integer threateningPopulation;

	/**
	 * 现场情况描述
	 */
	private String description;

	/**
	 * 是否需要技术支撑
	 */
	private Boolean technicalSupportFlag;

	/**
	 * 删除标志，0未删除，1已删除
	 */
	private String delFlag = "0";

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 修改人
	 */
	private String updateBy;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 修改时间
	 */
	private Date updateTime;

}
