package com.pig4cloud.pigx.gis.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("app_patrol_task_file")
@ApiModel(value = "AppPatrolTaskFile对象", description = "巡查任务附件表")
public class AppPatrolTaskFile {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 文件ID
     */
    private Long fileId;
}
