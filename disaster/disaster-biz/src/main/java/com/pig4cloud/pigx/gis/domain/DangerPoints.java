package com.pig4cloud.pigx.gis.domain;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.pig4cloud.pigx.gis.common.converter.BooleanToYesNoConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;


@Data
@EqualsAndHashCode(callSuper = false)
public class DangerPoints extends BaseEntity{


	/**
	 * 主键id
	 */
	@ExcelIgnore
	private String id;

	/**
	 * 隐患点编号
	 */
	@ExcelProperty("隐患点统一编号")
	private String dangerPointsNumber;

	/**
	 * 名称
	 */
	@ExcelProperty("隐患点名称")
	private String name;


	/**
	 * 点-空间字段
	 */
	@ExcelProperty("点")
	@ExcelIgnore
	private String geom;

	/**
	 * 隐患点类型（1：不稳定斜坡，2：滑坡，3：崩塌，4：泥石流）
	 */
	@ExcelProperty("隐患点类型（1：不稳定斜坡，2：滑坡，3：崩塌，4：泥石流）")
	private String dangerPointsType;

	/**
	 * 网格编号
	 */
	@ExcelProperty("网格编号")
	private String gridNumber;

	/**
	 * 数据库编号
	 */
	@ExcelProperty("数据库编号")
	@ExcelIgnore
	private String dataBaseNumber;

	/**
	 * 省
	 */
	@ExcelProperty("省")
	private String province;

	/**
	 * 市
	 */
	@ExcelProperty("市")
	private String city;

	/**
	 * 区（县）
	 */
	@ExcelProperty("区（县）")
	private String area;

	/**
	 * 乡
	 */
	@ExcelProperty("乡")
	private String country;

	/**
	 * 村
	 */
	@ExcelProperty("村")
	private String village;

	/**
	 * 组（小地方）
	 */
	@ExcelProperty("网格村下的组")
	private String smallPlace;

	/**
	 * x
	 */
	@ExcelProperty("X坐标")
	private String x;

	/**
	 * y
	 */
	@ExcelProperty("Y坐标")
	private String y;

	/**
	 * 经度
	 */
	@ExcelProperty("经度")
	private Double lon;

	/**
	 * 纬度
	 */
	@ExcelProperty("纬度")
	private Double lat;

	/**
	 * 长
	 */
	@ExcelProperty("长(米)")
	private Double longValue;

	/**
	 * 宽
	 */
	@ExcelProperty("宽(米)")
	private Double wide;

	/**
	 * 高
	 */
	@ExcelProperty("高(米)")
	private Double height;

	/**
	 * 面积
	 */
	@ExcelProperty("面积")
	private Double measure;

	/**
	 * 体积
	 */
	@ExcelProperty("体积")
	private Double volume;

	/**
	 * 规模（1：小型，2：中型）
	 */
	@ExcelProperty("规模（1：小型，2：中型）")
	private String scale;

	/**
	 * 管理层级（1：县级，2：村级，3：乡级）
	 */
	@ExcelProperty("管理层级（0: 市级 1：县级，2：村级，3：乡级）")
	private String managementLevel;

	/**
	 * 威胁人口
	 */
	@ExcelProperty("威胁人口")
	private Integer threateningPopulation;

	/**
	 * 威胁财产
	 */
	@ExcelProperty("威胁财产")
	private Double threateningProperty;

	/**
	 * 险情等级（1：小型，2：中型，3：大型）
	 */
	@ExcelProperty("险情等级（1：小型，2：中型，3：大型）")
	private String dangerLevel;

	/**
	 * 次生灾害时间
	 */
	@ExcelProperty("次生灾害时间")
	private String secondaryDisasterTime;

	/**
	 * 地质环境条件
	 */
	@ExcelProperty("地质环境条件")
	private String addressEnvironmentalConditions;

	/**
	 * 变形特征及活动历时
	 */
	@ExcelProperty("变形特征及活动历时")
	private String deformationCharacteristicsActivityDuration;

	/**
	 * 稳定性分析
	 */
	@ExcelProperty("稳定性分析")
	private String stabilityAnalysis;

	/**
	 * 稳定性现状（1：基本稳定，2：稳定，3：不稳定）
	 */
	@ExcelProperty("稳定性现状（1：基本稳定，2：稳定，3：不稳定）")
	private String stabilityStatus;

	/**
	 * 稳定性趋势（1：基本稳定，2：稳定，3：不稳定）
	 */
	@ExcelProperty("稳定性趋势（1：基本稳定，2：稳定，3：不稳定）")
	private String stabilityTrend;

	/**
	 * 引发因素
	 */
	@ExcelProperty("引发因素")
	private String causingFactors;

	/**
	 * 潜在危害
	 */
	@ExcelProperty("潜在危害")
	private String potentialHazards;

	/**
	 * 临灾状态预测
	 */
	@ExcelProperty("临灾状态预测")
	private String predictionImpendingDisasterState;

	/**
	 * 监测方法
	 */
	@ExcelProperty("监测方法")
	private String monitoringMethods;

	/**
	 * 监测人
	 */
	@ExcelProperty("监测人网格人员")
	private String monitor;

	/**
	 * 监测人职务(1：村干部，2：其它）
	 */
	@ExcelProperty("监测人职务(1：村干部，2：其它）")
	private String positionSupervisor;

	/**
	 * 监测人电话
	 */
	@ExcelProperty("监测人电话")
	private String monitorTelephone;

	/**
	 * 警示牌文字内容
	 */
	@ExcelProperty("警示牌文字内容")
	private String textWarningSign;

	/**
	 * 责任人
	 */
	@ExcelProperty("责任人")
	private String personLiable;

	/**
	 * 责任人电话
	 */
	@ExcelProperty("责任人电话")
	private String personLiableTelephone;

	/**
	 * 管理员
	 */
	@ExcelProperty("管理员")
	private String administrators;

	/**
	 * 管理员电话
	 */
	@ExcelProperty("管理员电话")
	private String administratorsTelephone;

	/**
	 * 协管员
	 */
	@ExcelProperty("协管员")
	private String trafficAssistant;

	/**
	 * 协管员电话
	 */
	@ExcelProperty("协管员电话")
	private String trafficAssistantTelephone;

	/**
	 * 专管员
	 */
	@ExcelProperty("专管员")
	private String specialManager;

	/**
	 * 专管员电话
	 */
	@ExcelProperty("专管员电话")
	private String specialManagerTelephone;


	/**
	 * 是否核销
	 */
	@ExcelProperty(value = "是否核销", converter = BooleanToYesNoConverter.class)
	private Boolean verificationFlag;

	/**
	 * 地理位置
	 */
	@ExcelProperty("地理位置")
	private String geographicLocation;

	/**
	 * 填表日期  主要用于表示不带时间的日期（年、月、日）
	 */
	@ExcelProperty("填表日期")
	private LocalDate fillInDate;

	/**
	 * 现场照片
	 */
	@ExcelProperty("现场照片")
	@ExcelIgnore
	private Long pictureLocale;

	/**
	 * 防灾预案图
	 */
	@ExcelProperty("防灾预案图")
	@ExcelIgnore
	private Long pictureDisasterPlan;

	/**
	 * 平面图
	 */
	@ExcelProperty("平面图")
	@ExcelIgnore
	private Long pictureFlat;

	/**
	 * 剖面图
	 */
	@ExcelProperty("剖面图")
	@ExcelIgnore
	private Long pictureSection;
}
