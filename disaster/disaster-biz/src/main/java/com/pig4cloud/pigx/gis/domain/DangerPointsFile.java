package com.pig4cloud.pigx.gis.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("t_danger_points_file")
public class DangerPointsFile extends Model<DangerPointsFile> {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 隐患点省级id
     */
    private String dangerPointsProvincialId;

    /**
     * 文件id
     */
    private Long fileId;


}
