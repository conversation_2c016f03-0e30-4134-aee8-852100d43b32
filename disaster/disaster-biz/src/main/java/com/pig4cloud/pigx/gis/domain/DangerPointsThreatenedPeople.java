package com.pig4cloud.pigx.gis.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("t_danger_points_threatened_people")
public class DangerPointsThreatenedPeople {

	/**
	 * 主键id
	 */
	@TableId(type = IdType.AUTO)
	private Long id;


	/**
	 * 隐患点id
	 */
	private String dangerPointsId;


	/**
	 * 威胁人员姓名
	 */
	private String name;

	/**
	 * 威胁人员地址
	 */
	private String address;

	/**
	 * 户主名字
	 */
	private String householdName;


	/**
	 * 联系电话
	 */
	private String telephone;



	/**
	 * 创建人
	 */
	private Long createUser;

	/**
	 * 修改人
	 */
	private Long updateUser;
	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 修改时间
	 */
	private Date updateTime;


}
