package com.pig4cloud.pigx.gis.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;


/**
 * <AUTHOR> wjj
 * @create 2025年6月6日13:51:21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("t_devices")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "测设备表")
public class Devices extends Model<Devices> {

	@TableId(type = IdType.AUTO)
	@ApiModelProperty(value = "ID", hidden = true)
	private Long id;

	@ApiModelProperty("空间字段")
	private String geom;

	@ApiModelProperty("测站编号")
	@JsonProperty("device_no")
	private String deviceNo;

	@ApiModelProperty("测站名称")
	@JsonProperty("device_name")
	private String deviceName;

	@ApiModelProperty("测站类型")
	@JsonProperty("device_type")
	private String deviceType;

	@ApiModelProperty("经度与location数据保持一致")
	private Double lon;

	@ApiModelProperty("纬度与location数据保持一致")
	private Double lat;

	@ApiModelProperty("隐患点/风险区表关联")
	@JsonProperty("danger_or_risk_id")
	private String dangerOrRiskId;

	@ApiModelProperty("1隐患点,2风险区")
	@JsonProperty("danger_or_risk")
	private Integer dangerOrRisk;

	/**
	 * 设备状态，在线1/离线2，通过定时任务查看当天实时数据表是否有数据来改变此字段
	 */
	@ApiModelProperty("设备状态，在线1/离线2")
	@JsonProperty("device_status")
	private String deviceStatus;

	@ApiModelProperty("设备厂商")
	@JsonProperty("device_company")
	private String deviceCompany;

	@ApiModelProperty("设备类型编号，多个用逗号分隔")
	@JsonProperty("station_type_number")
	private String stationTypeNumber;



	/**
	 * 乡镇id
	 */
	@TableField(exist = false)
	private Long quId;

	/**
	 * 乡镇id
	 */
	@TableField(exist = false)
	private Long townId;

	/**
	 * 村id
	 */
	@TableField(exist = false)
	private Long villageId;

	/**
	 * 1 区  2镇  3村
	 */
	@TableField(exist = false)
	private Integer level;

	/**
	 * 地灾点名称
	 */
	@TableField(exist = false)
	@JsonProperty("disaster_name")
	private String disasterName;

	/**
	 * 测站类型数组
	 */
	@TableField(exist = false)
	//@JsonProperty("station_type_arr")
	private List<String> deviceTypeList;
}
