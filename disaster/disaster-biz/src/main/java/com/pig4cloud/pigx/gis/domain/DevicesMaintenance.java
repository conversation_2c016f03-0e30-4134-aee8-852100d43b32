package com.pig4cloud.pigx.gis.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.util.Date;

/**
 * @ClassName : DevicesMaintenance 设备维护表
 * <AUTHOR> wjj
 * @create 2025年6月6日13:51:21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("t_devices_maintenance")
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "监测设备维护表")
public class DevicesMaintenance extends Model<DevicesMaintenance> {


	/**
	 * 主键id
	 */
	@TableId(type = IdType.AUTO)
	private Long id;


	/**
	 * 设备关联id
	 */
	private Long deviceId;

	/**
	 * 设备名称
	 */
	private String deviceName;

	/**
	 * 设备编号
	 */
	private String deviceNo;


	/**
	 * 维护部门名称
	 */
	private String department;


	/**
	 * 维护时间
	 */
	private Date time;


	/**
	 * 设备状态  1良好 2损坏
	 */
	private Integer status;

	/**
	 * 维护人
	 */
	private String maintainer;

	/**
	 * 维护内容
	 */
	private String content;

}
