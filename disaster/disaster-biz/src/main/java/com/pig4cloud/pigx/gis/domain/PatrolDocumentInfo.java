package com.pig4cloud.pigx.gis.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.util.Date;

/**
 * 文件信息
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("patrol_document_info")
public class PatrolDocumentInfo extends Model<PatrolDocumentInfo> {

	/**
	 * 巡查记录ID
	 */
	@TableId(type = IdType.AUTO)
	private Long id;
	/**
	 * 文件名称
	 */
	private String sourceName;
	/**
	 * 文件后缀
	 */
	private String suffix;

	/**
	 * 文件路径
	 */
	private String path;


	/**
	 * 文件大小
	 */
	private Long size;

	/**
	 * 创建人
	 */
	private Long creator;

	/**
	 * 修改人
	 */
	private Long modifier;

	/**
	 * 创建时间
	 */
	private Date gmtCreate;


	/**
	 * 修改时间
	 */
	private Date gmtModified;



}
