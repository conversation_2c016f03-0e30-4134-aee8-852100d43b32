package com.pig4cloud.pigx.gis.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 巡查记录
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("patrol_record")
public class PatrolRecord {

	/**
	 * 主键id
	 */
	@TableId(type = IdType.AUTO)
	private Long id;


	/**
	 * 巡查任务id
	 */
	private Long patrolTaskId;


	/**
	 * 巡查名称
	 */
	private String name;


	/**
	 * 风险点数量
	 */
	private Integer riskPointNum;

	/**
	 * 威胁住户数量
	 */
	private Integer threatenedPeopleNum;

	/**
	 * 撤离人员
	 */
	private Integer evacuationPeopleNum;



	/**
	 * 创建人
	 */
	private Long createUser;

	/**
	 * 修改人
	 */
	private Long updateUser;
	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 修改时间
	 */
	private Date updateTime;



}
