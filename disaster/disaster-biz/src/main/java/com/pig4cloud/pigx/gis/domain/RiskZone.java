package com.pig4cloud.pigx.gis.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.Pictures;
import lombok.*;

import java.io.File;
import java.util.List;


/**
 * 风险区对象 t_risk_zone
 *
 * <AUTHOR>
 * @date 2025年6月2日10:22:33
 */


@Data
@Builder
@TableName("t_risk_zone")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RiskZone {


	private static final long serialVersionUID = 1L;

	/**
	 *  主键
	 */
	@TableId(type = IdType.AUTO)
	private Integer id;


	/**
	 *  地理坐标
	 */
	private String geom;

	/**
	 *  统一编号
	 */
	private String tybh;


	/**
	 *  分级
	 */
	private String fj;


	/**
	 * 斜坡类型
	 */
	private String xplx;


	/**
	 * 风险区名称
	 */
	private String fxqmc;

	/**
	 *  乡镇
	 */
	private String qx;


	/**
	 *  乡镇
	 */
	private String xz;


	/**
	 * 行政村
	 */
	private String xzc;


	/**
	 *  野外编号
	 */
	private String ywbh;

	/**
	 * 面积m2
	 */
	private Double mjm;


	/**
	 * 面积km2
	 */
	private Double mjkm;

	/**
	 * 风险等级
	 * */
	private String fxdj;

	/**
	 * 管控等级
	 * */
	private String gkdj;


	/**
	 * 地灾点
	 * */
	private String dzd;


	/**
	 *  所在组
	 */
	private String szz;

	/**
	 *  小地名
	 */
	private String xdm;


	/**
	 *  安置点
	 */
	private String xzd;


	/**
	 *  威胁户数
	 */
	private Integer wxhs;


	/**
	 *  威胁人数
	 */
	private Integer wxrs;


	/**
	 *  威胁财产
	 */
	private Double wxcc;


	/**
	 *  镇长
	 */
	private String zz;

	/**
	 *  镇长电话
	 */
	private String zzdh;


	/**
	 *  国土所长
	 */
	private String gtsz;


	/**
	 *  国土所长电话
	 */
	private String gtszdh;


	/**
	 *  技术支撑
	 */
	private String jszc;


	/**
	 *  技术支撑电话
	 */
	private String jszcdh;


	/**
	 * 村书记
	 */
	private String csj;


	/**
	 *  村书记电话
	 */
	private String csjdh;


	/**
	 *  巡查员
	 */
	private String xcy;


	/**
	 *  巡查员电话
	 */
	private String xcydh;


	/**
	 *  监测员
	 */
	private String jcy;


	/**
	 *  监测员电话
	 */
	private String jcydh;

	/**
	 * 人员清单
	 */
	private String ryqd;


	/**
	 * x
	 */
	private Double x;

	/**
	 *  y
	 */
	private Double y;

	/**
	 * 维度
	 */
	private String wd;

	/**
	 *  经度
	 */
	private String jd;

	/**
	 * 时代
	 */
	private String sd;

	/**
	 * 岩性
	 */
	private String yx;

	/**
	 * 产状
	 */
	private String cz;

	/**
	 * 坡高
	 */
	private Double pg;

	/**
	 * 坡度
	 */
	private Double pd;

	/**
	 * 坡顶高程
	 */
	private Double pdgc;

	/**
	 * 坡底高程
	 */
	private Double pdigc;

	/**
	 * 顺坡长度
	 */
	private Double spcd;

	/**
	 * 最大宽度
	 */
	private Double zdkd;

	/**
	 * 坡向
	 */
	private Double px;

	/**
	 * 坡形
	 */
	private String poxing;

	/**
	 * 风险区图片详情
	 */
	private Long pictureDetail;

	/**
	 * 风险区图片详情路径
	 */
	@TableField(exist = false)
	private String pictureDetailUrl;


	/**
	 * 分级，多选参数
	 */
	@TableField(exist = false)
	private List<String> fjList;


	/**
	 * 乡镇，多选参数
	 */
	@TableField(exist = false)
	private List<String> xzList;


	/**
	 * 分类统计的字段
	 */
	@TableField(exist = false)
	private String statisticsField;

}

