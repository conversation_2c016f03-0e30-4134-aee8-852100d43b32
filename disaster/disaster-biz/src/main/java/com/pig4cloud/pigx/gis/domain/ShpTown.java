package com.pig4cloud.pigx.gis.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;


/**
 * 乡镇边界数据对象 ys_shp_town
 *
 * <AUTHOR>
 * @date 2024-05-21
 */
@Data
@Builder
@TableName("t_shp_town")
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ShpTown {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.AUTO)
	private Integer id;

	/**
	 * 地理坐标
	 */
	private String geom;

	/**
	 * 县乡镇名称
	 */
	private String pzwh;

	/**
	 * 乡/镇名称
	 */
	private String name;

	/**
	 * 乡镇类型
	 */
	private String type;

}
