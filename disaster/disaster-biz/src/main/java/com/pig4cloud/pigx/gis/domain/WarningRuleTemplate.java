package com.pig4cloud.pigx.gis.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("t_warning_rule_template")
public class WarningRuleTemplate extends Model<WarningRuleTemplate> {

	/**
	 * 主键id
	 */
	@TableId(type = IdType.INPUT)
	private String id;


	/**
	 * 预警规则名称
	 */
	private String ruleName;


	/**
	 * 预警类型 1 雨量 2 裂缝 3 GNSS 4倾角 5 加速度
	 */
	private Integer ruleType;

	/**
	 * 预警级别 1红 2橙 3黄 4蓝
	 */
	private Integer leave;


	/**
	 * 统计时长
	 */
	private Integer time;


	/**
	 * 阈值设置  1 独立设置 2 统一设置  3 固定阈值 4  权重规则
	 */
	private Integer thresholdSetting;


	/**
	 * x  预警阀值
	 */
	private Integer x;

	/**
	 * y  预警阀值
	 */
	private Integer y;

	/**
	 * z  预警阀值
	 */
	private Integer z;


	/**
	 * 编码
	 */
	private String code;


	/**
	 * 权重
	 */
	private String weightRules;


	/**
	 *   应用到所有设备标识  1否  2是
	 */
	private Integer defaultFlag;


	/**
	 * 创建人姓名
	 */
	private String createBy;

	/**
	 * 创建时间
	 */
	private Date createTime;


	/**
	 * 应用时间
	 */
	private Date applyTime;

}
