package com.pig4cloud.pigx.gis.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class AppDisasterReportingAddDTO {

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 所属行政区ID
     */
    @NotNull(message = "所属行政区不能为空")
    private Integer regionId;

    /**
     * 地址描述
     */
    @NotNull(message = "地址描述不能为空")
    private String address;

    /**
     * 灾害类型
     */
    @NotNull(message = "灾害类型不能为空")
    private Integer disasterType;

    /**
     * 灾情描述
     */
    private String disasterDetails;

    /**
     * 受灾人员姓名
     */
    private String peopleAffectedName;

    /**
     * 受灾人员电话
     */
    private String peopleAffectedPhone;

    /**
     * 紧急程度，1一般，2加急，3特急
     */
    @NotNull(message = "紧急程度不能为空")
    private Integer urgency;

    /**
     * 查阅人ID
     */
    @NotEmpty(message = "通知人员不能为空")
    private List<Long> reviewerIdList;

    /**
     * 照片ID
     */
    @NotEmpty(message = "现场照片不能为空")
    private List<String> fileIdList;
}
