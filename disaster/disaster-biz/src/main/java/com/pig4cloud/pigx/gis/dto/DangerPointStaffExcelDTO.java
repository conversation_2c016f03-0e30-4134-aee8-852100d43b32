package com.pig4cloud.pigx.gis.dto;


import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 按“索引”映射多级表头展开后的列，更稳。
 * 见：Excel 实测下标（0-based）
 */
@Data
public class DangerPointStaffExcelDTO {

    @ExcelProperty(index = 1) // B列: 乡镇
    private String country;

    @ExcelProperty(index = 2) // C列: 网格名（村）
    private String village;

    @ExcelProperty(index = 3) // D列: 网格责任人姓名
    private String personLiable;

    @ExcelProperty(index = 4) // E列: 网格责任人电话
    private String personLiableTelephone;

    @ExcelProperty(index = 5) // F列: 网格管理员姓名
    private String administrators;

    @ExcelProperty(index = 6) // G列: 网格管理员电话
    private String administratorsTelephone;

    @ExcelProperty(index = 7) // H列: 网格协管员姓名
    private String trafficAssistant;

    @ExcelProperty(index = 8) // I列: 网格协管员电话
    private String trafficAssistantTelephone;

    @ExcelProperty(index = 9) // J列: 网格专管员姓名
    private String specialManager;

    @ExcelProperty(index = 10) // K列: 网格专管员电话
    private String specialManagerTelephone;


}
