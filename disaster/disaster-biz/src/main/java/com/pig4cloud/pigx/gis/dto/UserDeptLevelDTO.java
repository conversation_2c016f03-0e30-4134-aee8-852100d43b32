package com.pig4cloud.pigx.gis.dto;

import com.pig4cloud.pigx.admin.api.entity.SysDept;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户所属的行政区划层级DTO
 *
 * <AUTHOR>
 * @date 2024-05-19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDeptLevelDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 用户的部门信息
	 */
	private SysDept dept;

	/**
	 * 用户所属部门的级别，0：市级用户，1：区/县用户，2：乡/镇用户，3:村级用户，null：用户无部门
	 */
	private Integer level;

	/**
	 * 市ID
	 */
	private Long cityId;

	/**
	 * 区/县ID
	 */
	private Long quId;

	/**
	 * 乡/镇ID
	 */
	private Long townId;

	/**
	 * 村ID
	 */
	private Long villageId;
}