package com.pig4cloud.pigx.gis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 日常巡排查
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@TableName("app_daily_reporting")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "日常巡排查")
public class AppDailyReporting extends Model<AppDailyReporting> {


	/**
	* 主键
	*/
	@JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 经度
	*/
    @Schema(description="经度")
    private Double longitude;

	/**
	* 纬度
	*/
    @Schema(description="纬度")
    private Double latitude;

	/**
	* 位置
	*/
    @Schema(description="位置")
    private String address;

	/**
	* 隐患点类型，1：不稳定斜坡，2：滑坡，3：崩塌，4：泥石流
	*/
    @Schema(description="隐患点类型，1：不稳定斜坡，2：滑坡，3：崩塌，4：泥石流")
    private String dangerPointsType;

	/**
	* 灾(险)情分级，1小型，2中型，3大型，4特大型
	*/
    @Schema(description="灾(险)情分级，1小型，2中型，3大型，4特大型")
    private String dangerLevel;

	/**
	* 威胁户数
	*/
    @Schema(description="威胁户数")
    private Integer threateningHousehold;

	/**
	* 威胁人口数量
	*/
    @Schema(description="威胁人口数量")
    private Integer threateningPopulation;

	/**
	* 首次灾害发生时间
	*/
    @Schema(description="首次灾害发生时间")
    private LocalDate occurrenceDate;

	/**
	* 变形活动特征
	*/
    @Schema(description="变形活动特征")
    private String characteristic;

	/**
	* 已开展防治工作
	*/
    @Schema(description="已开展防治工作")
    private String preventionWork;

	/**
	* 监测人姓名
	*/
    @Schema(description="监测人姓名")
    private String monitoringName;

	/**
	* 监测人电话
	*/
    @Schema(description="监测人电话")
    private String monitoringPhone;

	/**
	* 防治工作建议
	*/
    @Schema(description="防治工作建议")
    private String suggestions;

	/**
	* 备注
	*/
    @Schema(description="备注")
    private String remark;

	/**
	* 寻排查人员签名
	*/
    @Schema(description="寻排查人员签名")
    private String signatureImage;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 删除标志，0未删除，1已删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标志，0未删除，1已删除")
    private String delFlag;
}