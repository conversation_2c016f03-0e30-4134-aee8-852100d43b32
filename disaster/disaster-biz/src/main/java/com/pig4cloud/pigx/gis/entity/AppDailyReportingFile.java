package com.pig4cloud.pigx.gis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 日常巡排查附件
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@TableName("app_daily_reporting_file")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "日常巡排查附件")
public class AppDailyReportingFile extends Model<AppDailyReportingFile> {


	/**
	* 主键
	*/
	@JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 日常巡排查数据ID
	*/
    @Schema(description="日常巡排查数据ID")
    private Long dailyId;

	/**
	* 附件ID
	*/
    @Schema(description="附件ID")
    private Long fileId;
}