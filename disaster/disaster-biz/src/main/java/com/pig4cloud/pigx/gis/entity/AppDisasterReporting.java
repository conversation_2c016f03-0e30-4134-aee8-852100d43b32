package com.pig4cloud.pigx.gis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 一键报灾
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@TableName("app_disaster_reporting")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "一键报灾")
public class AppDisasterReporting extends Model<AppDisasterReporting> {

	/**
	* 主键
	*/
	@JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 经度
	*/
    @Schema(description="经度")
    private Double longitude;

	/**
	* 纬度
	*/
    @Schema(description="纬度")
    private Double latitude;

	/**
	* 所属行政区ID
	*/
    @Schema(description="所属行政区ID")
    private Integer regionId;

	/**
	* 地址描述
	*/
    @Schema(description="地址描述")
    private String address;

	/**
	* 灾害类型
	*/
    @Schema(description="灾害类型")
    private Integer disasterType;

	/**
	* 灾情描述
	*/
    @Schema(description="灾情描述")
    private String disasterDetails;

	/**
	* 受灾人员姓名
	*/
    @Schema(description="受灾人员姓名")
    private String peopleAffectedName;

	/**
	* 受灾人员电话
	*/
    @Schema(description="受灾人员电话")
    private String peopleAffectedPhone;

	/**
	* 紧急程度，1一般，2加急，3特急
	*/
    @Schema(description="紧急程度，1一般，2加急，3特急")
    private Integer urgency;

	/**
	* 查阅状态，1待处理，2已处理
	*/
    @Schema(description="查阅状态，1待处理，2已处理")
    private Integer status;

	/**
	* 查阅人ID
	*/
    @Schema(description="查阅人ID")
    private Long reviewer;

	/**
	* 查阅时间
	*/
    @Schema(description="查阅时间")
    private LocalDateTime reviewTime;

	/**
	 * 删除标志，0未删除，1已删除
	 */
	@TableLogic
	@TableField(fill = FieldFill.INSERT)
	@Schema(description = "删除标记,1:已删除,0:正常")
	private String delFlag;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;
}