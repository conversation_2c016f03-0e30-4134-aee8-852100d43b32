package com.pig4cloud.pigx.gis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 一键报灾审阅人员
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@TableName("app_disaster_reporting_reviewer")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "一键报灾审阅人员")
public class AppDisasterReportingReviewer extends Model<AppDisasterReportingReviewer> {


	/**
	* 主键
	*/
	@JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 一键报灾ID
	*/
    @Schema(description="一键报灾ID")
    private Long disasterReportingId;

	/**
	* 审阅人ID
	*/
    @Schema(description="审阅人ID")
    private Long userId;
}