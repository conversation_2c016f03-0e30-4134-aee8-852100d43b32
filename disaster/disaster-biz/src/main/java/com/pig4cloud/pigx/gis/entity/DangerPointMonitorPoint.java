package com.pig4cloud.pigx.gis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 监测点关联隐患点
 *
 * <AUTHOR>
 * @date 2025-08-25 09:45:19
 */
@Data
@TableName("t_danger_point_monitor_point")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "监测点关联隐患点")
public class DangerPointMonitorPoint extends Model<DangerPointMonitorPoint> {


	/**
	* 主键
	*/
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Integer id;

	/**
	* 隐患点编号
	*/
    @Schema(description="隐患点编号")
    private String dangerPointCode;

	/**
	* 监测点编号
	*/
    @Schema(description="监测点编号")
    private String monitorPointCode;

	/**
	* 监测点名称
	*/
    @Schema(description="监测点名称")
    private String dangerPointName;

	/**
	* 市
	*/
    @Schema(description="市")
    private String city;

	/**
	* 县
	*/
    @Schema(description="县")
    private String county;
}