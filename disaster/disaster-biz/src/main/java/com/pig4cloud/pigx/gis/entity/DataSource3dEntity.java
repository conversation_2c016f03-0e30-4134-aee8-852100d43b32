package com.pig4cloud.pigx.gis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 三维模型数据-区域模型
 *
 * <AUTHOR>
 * @date 2025-09-02 13:35:41
 */
@Data
@TableName("t_data_source_3d")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "三维模型数据-区域模型")
@AllArgsConstructor
public class DataSource3dEntity extends Model<DataSource3dEntity> {


	/**
	* 资源ID
	*/
    @TableId(type = IdType.ASSIGN_UUID)
    @Schema(description="资源ID")
    private String id;

	/**
	* 资源名称
	*/
    @Schema(description="资源名称")
    private String name;

	/**
	* 文件名称
	*/
    @Schema(description="文件名称")
    private String fileName;

	/**
	* 文件大小
	*/
    @Schema(description="文件大小")
    private Long totalSize;

	/**
	* 路径
	*/
    @Schema(description="路径")
    private String parentFile;

	/**
	* url
	*/
    @Schema(description="url")
    private String url;

	/**
	* 数据时项
	*/
    @Schema(description="数据时项")
    private LocalDate timeTrem;

	/**
	* 公开状态:1公开,0不公开
	*/
    @Schema(description="公开状态:1公开,0不公开")
    private Integer publicStatus;

	/**
	* 所属部门ID
	*/
    @Schema(description="所属部门ID")
    private Long deptId;

	/**
	 * 1:处理中 / 2:完成 / 3:失败
	 */
	@Schema(description="1:处理中 / 2:完成 / 3:失败")
	private String status;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 删除标记，0未删除，1已删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记，0未删除，1已删除")
    private String delFlag;

	public DataSource3dEntity() {
	}

	public DataSource3dEntity(String id, String status) {
		this.id = id;
		this.status = status;
	}

	public DataSource3dEntity(String id, String parentFile, String url, String status) {
		this.id = id;
		this.parentFile = parentFile;
		this.url = url;
		this.status = status;
	}
}