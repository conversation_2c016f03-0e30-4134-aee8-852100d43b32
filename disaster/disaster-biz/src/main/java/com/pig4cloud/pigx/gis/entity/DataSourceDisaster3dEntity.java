package com.pig4cloud.pigx.gis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 三维模型数据-隐患点，风险区模型
 *
 * <AUTHOR>
 * @date 2025-09-03 10:35:47
 */
@Data
@TableName("t_data_source_disaster_3d")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "三维模型数据-隐患点，风险区模型")
public class DataSourceDisaster3dEntity extends Model<DataSourceDisaster3dEntity> {

 
	/**
	* id
	*/
    @TableId(type = IdType.ASSIGN_UUID)
    @Schema(description="id")
    private String id;
 
	/**
	* 监测类型，1隐患点，2风险区
	*/
    @Schema(description="monitorDataType")
    private String monitorDataType;
 
	/**
	* 省级平台统一编号
	*/
    @Schema(description="provincialCode")
    private String provincialCode;
 
	/**
	* 数据名称
	*/
    @Schema(description="name")
    private String name;
 
	/**
	* 文件名称
	*/
    @Schema(description="fileName")
    private String fileName;
 
	/**
	* 文件大小
	*/
    @Schema(description="totalSize")
    private Long totalSize;
 
	/**
	* 路径
	*/
    @Schema(description="parentFile")
    private String parentFile;
 
	/**
	* url
	*/
    @Schema(description="url")
    private String url;
 
	/**
	* 数据时项
	*/
    @Schema(description="timeTrem")
    private LocalDate timeTrem;
 
	/**
	* 公开状态:1公开,0不公开
	*/
    @Schema(description="publicStatus")
    private Integer publicStatus;
 
	/**
	* 所属部门ID
	*/
    @Schema(description="deptId")
    private Long deptId;

	/**
	 * 1:处理中 / 2:完成 / 3:失败
	 */
	@Schema(description="status")
	private String status;
 
	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="createBy")
    private String createBy;
 
	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="createTime")
    private LocalDateTime createTime;
 
	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="updateBy")
    private String updateBy;
 
	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="updateTime")
    private LocalDateTime updateTime;
 
	/**
	* 删除标记，0未删除，1已删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="delFlag")
    private String delFlag;

	public DataSourceDisaster3dEntity() {
	}

	public DataSourceDisaster3dEntity(String id, String status) {
		this.id = id;
		this.status = status;
	}

	public DataSourceDisaster3dEntity(String id, String parentFile ,String url, String status) {
		this.id = id;
		this.url = url;
		this.parentFile = parentFile;
		this.status = status;
	}
}