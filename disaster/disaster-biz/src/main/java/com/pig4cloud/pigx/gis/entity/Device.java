package com.pig4cloud.pigx.gis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 监测设备
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@TableName("t_device")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "监测设备")
public class Device extends Model<Device> {


	/**
	* 主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="主键")
    private Long id;

	/**
	* 空间字段
	*/
    @Schema(description="空间字段")
    private String geom;

	/**
	* 监测设备名称
	*/
    @Schema(description="监测设备名称")
    private String deviceName;

	/**
	* 隐患点省平台统一编号
	*/
    @Schema(description="隐患点省平台统一编号")
    private String dangerPointsCode;

	/**
	 * 风险区统一编号
	 */
	@Schema(description="风险区统一编号")
	private String riskZoneCode;

	/**
	* 传感器类型
	*/
    @Schema(description="传感器类型")
    private String sensorType;

	/**
	* 是否同步到部平台
	*/
    @Schema(description="是否同步到部平台")
    private Boolean syncFlag;

	/**
	* 市
	*/
    @Schema(description="市")
    private String city;

	/**
	* 区县
	*/
    @Schema(description="区县")
    private String county;

	/**
	* 经度
	*/
    @Schema(description="经度")
    private Double longitude;

	/**
	* 纬度
	*/
    @Schema(description="纬度")
    private Double latitude;

	/**
	* 监测点名称
	*/
    @Schema(description="监测点名称")
    private String monitorPointName;

	/**
	* 监测点统一编号
	*/
    @Schema(description="监测点统一编号")
    private String monitorPointCode;

	/**
	* 设备状态
	*/
    @Schema(description="设备状态")
    private String deviceStatus;

	/**
	* 设备ID(clientID)
	*/
    @Schema(description="设备ID(clientID)")
    private String clientId;

	/**
	* 设备key
	*/
    @Schema(description="设备key")
    private String deviceKey;

	/**
	* 设备sn
	*/
    @Schema(description="设备sn")
    private String deviceSn;

	/**
	* 设备型号
	*/
    @Schema(description="设备型号")
    private String deviceModel;

	/**
	* 通讯方式，0: GPRS/3G/4G  1: NB-Iot
	*/
    @Schema(description="通讯方式，0: GPRS/3G/4G  1: NB-Iot")
    private String network;

	/**
	* 接入协议，0: MQTT  1: HTTP  2: COAP
	*/
    @Schema(description="接入协议，0: MQTT  1: HTTP  2: COAP")
    private String protocol;

	/**
	* 设备类型，0: 单参数  1: 多参数  2: 本地组网
	*/
    @Schema(description="设备类型，0: 单参数  1: 多参数  2: 本地组网")
    private String deviceType;

	/**
	* 设备厂商
	*/
    @Schema(description="设备厂商")
    private String deviceCompany;

	/**
	* 所属部门ID
	*/
    @Schema(description="所属部门ID")
    private Long deptId;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 删除标记，0未删除，1已删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记，0未删除，1已删除")
    private String delFlag;



	/**
	 * 乡镇id
	 */
	@TableField(exist = false)
	private Long quId;

	/**
	 * 乡镇id
	 */
	@TableField(exist = false)
	private Long townId;

	/**
	 * 村id
	 */
	@TableField(exist = false)
	private Long villageId;

	/**
	 * 1 区  2镇  3村
	 */
	@TableField(exist = false)
	private Integer level;

	/**
	 * 地灾点名称
	 */
	@TableField(exist = false)
	@JsonProperty("disaster_name")
	private String disasterName;

	/**
	 * 各种传感器类型
	 */
	@TableField(exist = false)
	//@JsonProperty("station_type_arr")
	private List<String> sensorTypeList;





}