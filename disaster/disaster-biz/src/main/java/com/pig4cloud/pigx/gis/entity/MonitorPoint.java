package com.pig4cloud.pigx.gis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;

/**
 * 监测点数据
 *
 * <AUTHOR>
 * @date 2025-08-25 09:44:40
 */
@Data
@TableName("t_monitor_point")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "监测点数据")
public class MonitorPoint extends Model<MonitorPoint> {


	/**
	* 自增主键
	*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description="自增主键")
    private Long id;

	/**
	* 空间字段
	*/
    @Schema(description="空间字段")
    private String geom;

	/**
	* 监测点编号
	*/
    @Schema(description="监测点编号")
    private String monitorPointCode;

	/**
	* 监测点名称
	*/
    @Schema(description="监测点名称")
    private String monitorPointName;

	/**
	* 灾害类型
	*/
    @Schema(description="灾害类型")
    private String disasterType;

	/**
	* 经度
	*/
    @Schema(description="经度")
    private Double longitude;

	/**
	* 纬度
	*/
    @Schema(description="纬度")
    private Double latitude;

	/**
	* 体积（10000×m³）
	*/
    @Schema(description="体积（10000×m³）")
    private Double volume;

	/**
	* 地理位置
	*/
    @Schema(description="地理位置")
    private String address;

	/**
	* 威胁人口
	*/
    @Schema(description="威胁人口")
    private Integer threateningPopulation;

	/**
	* 威胁财产，万
	*/
    @Schema(description="威胁财产，万")
    private Double threateningProperty;

	/**
	* 规模等级
	*/
    @Schema(description="规模等级")
    private String scaleLevel;

	/**
	* 险情等级
	*/
    @Schema(description="险情等级")
    private String dangerLevel;

	/**
	* 关联设备数量
	*/
    @Schema(description="关联设备数量")
    private Integer deviceNumber;

	/**
	* 区域代码
	*/
    @Schema(description="区域代码")
    private String areaCode;

	/**
	* 创建人
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

	/**
	* 创建时间
	*/
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

	/**
	* 修改人
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

	/**
	* 修改时间
	*/
	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

	/**
	* 删除标记，0未删除，1已删除
	*/
    @TableLogic
	@TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记，0未删除，1已删除")
    private String delFlag;
}