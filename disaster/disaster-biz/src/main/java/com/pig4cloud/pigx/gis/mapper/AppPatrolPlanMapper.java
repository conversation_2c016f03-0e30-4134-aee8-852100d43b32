package com.pig4cloud.pigx.gis.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.gis.domain.AppPatrolPlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;


@Mapper
public interface AppPatrolPlanMapper extends BaseMapper<AppPatrolPlan> {


	String selectPatrolPeople(@Param("regionId")Long regionId);

	Page<AppPatrolPlan> getpatrolList(@Param("page")Page<AppPatrolPlan> page,
									  @Param("taskStatus")Integer taskStatus,
									  @Param("taskType")Integer taskType,
									  @Param("patrolType")Integer patrolType,
									  @Param("startTime")Date startTime,
									  @Param("endTime")Date endTime);
}
