package com.pig4cloud.pigx.gis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pig4cloud.pigx.gis.domain.AppPatrolPlanPoint;
import com.pig4cloud.pigx.gis.dto.AppPatrolPlanPointDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface AppPatrolPlanPointMapper extends BaseMapper<AppPatrolPlanPoint> {


	List<AppPatrolPlanPointDTO> selectListByPlainId(@Param("planId") Long planId);
}
