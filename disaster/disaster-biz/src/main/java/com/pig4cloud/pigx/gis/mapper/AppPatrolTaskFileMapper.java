package com.pig4cloud.pigx.gis.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pig4cloud.pigx.gis.domain.AppPatrolTaskFile;
import com.pig4cloud.pigx.gis.vo.AppPatrolTaskPhotoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AppPatrolTaskFileMapper extends BaseMapper<AppPatrolTaskFile> {

    List<AppPatrolTaskPhotoVO> selectFileListByTaskId(@Param("taskId") Long taskId);
}
