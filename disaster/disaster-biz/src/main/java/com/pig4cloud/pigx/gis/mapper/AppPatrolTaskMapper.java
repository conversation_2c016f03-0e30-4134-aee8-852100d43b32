package com.pig4cloud.pigx.gis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.gis.domain.AppPatrolTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡查任务表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Mapper
public interface AppPatrolTaskMapper extends BaseMapper<AppPatrolTask> {

    /**
     * 分页查询巡查任务列表
     *
     * @param page 分页参数
     * @param appPatrolTask 查询条件
     * @return 巡查任务列表
     */
    IPage<AppPatrolTask> selectAppPatrolTaskPage(@Param("page") Page<AppPatrolTask> page, 
                                                 @Param("appPatrolTask") AppPatrolTask appPatrolTask);

    /**
     * 根据巡查计划ID查询任务列表
     *
     * @param planId 巡查计划ID
     * @return 任务列表
     */
    List<AppPatrolTask> selectByPlanId(@Param("planId") Long planId);

    /**
     * 根据点位ID查询任务列表
     *
     * @param pointId 点位ID
     * @return 任务列表
     */
    List<AppPatrolTask> selectByPointId(@Param("pointId") Long pointId);

    /**
     * 根据点位类型查询任务列表
     *
     * @param pointType 点位类型
     * @return 任务列表
     */
    List<AppPatrolTask> selectByPointType(@Param("pointType") Integer pointType);
}
