package com.pig4cloud.pigx.gis.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.gis.domain.DangerPoints;
import com.pig4cloud.pigx.gis.domain.EchartsProperty;
import com.pig4cloud.pigx.gis.domain.HgAddresses;
import com.pig4cloud.pigx.gis.dto.DangerPointsGridMembersMismatchDTO;
import com.pig4cloud.pigx.gis.param.DangerPointsPictureA;
import com.pig4cloud.pigx.gis.param.DangerPointsPictureB;
import com.pig4cloud.pigx.gis.vo.DangerPointsCountVO;
import com.pig4cloud.pigx.gis.vo.DangerPointsFileVO;
import com.pig4cloud.pigx.gis.vo.DangerPointsReqVO;
import com.pig4cloud.pigx.gis.vo.TaskLocationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 隐患点Mapper接口
 *
 * <AUTHOR>
 * @date 2025年5月27日15:28:55
 */

@Mapper
public interface DangerPointsMapper {


    /**
     * 修改隐患点
     *
     * @param dangerPoints 隐患点
     * @return 结果
     */
    void updateHgDangerPoints(DangerPoints dangerPoints);


    /**
     * 新增隐患点
     *
     * @param dangerPoints 隐患点
     * @return 结果
     */
    void insertHgDangerPoints(DangerPoints dangerPoints);


    /**
     * 批量删除隐患点
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteHgDangerPointsByIds(String[] ids);

    /**
     * 查询隐患点
     *
     * @param id 隐患点主键
     * @return 隐患点
     */
    DangerPoints selectHgDangerPointsById(String id);

    /**
     * 获取稳定性现状名称分组（基本稳定，稳定，不稳定）
     *
     * @return
     */
    List<String> selectGroupByStabilityStatus(@Param("countyId") Long countyId, @Param("townId") Long townId, @Param("villageId") Long villageId);

    /**
     * 获取隐患点类型名称分组（不稳定斜坡，滑坡，崩塌，泥石流）
     *
     * @return
     */
    List<String> selectGroupByDangerPointsType(@Param("countyId") Long countyId, @Param("townId") Long townId, @Param("villageId") Long villageId);

    /**
     * 根据指定字段统计
     *
     * @param dangerPoints
     * @return
     */
    List<EchartsProperty> selectStatistics(DangerPointsReqVO dangerPoints);


    /**
     * 查询隐患点列表
     *
     * @param reqVO 隐患点
     * @return 隐患点集合
     */
    Page<DangerPoints> selectHgDangerPointsList(@Param("pointPage") IPage<DangerPoints> pointPage, @Param("entity") DangerPointsReqVO reqVO);

    int checkDangerPoint(@Param("dangerPointsNumber") String dangerPointsNumber);

    Integer saveDangerPoint(@Param("list") List<Map<String, Object>> list);

    List<HgAddresses> listAllAddresses(@Param("level") Integer level,
                                       @Param("quId") Long quId,
                                       @Param("townId") Long townId,
                                       @Param("villageId") Long villageId);

    Integer selectDangerPointsTotal(@Param("level") Integer level,
                                    @Param("quId") Long quId,
                                    @Param("townId") Long townId,
                                    @Param("villageId") Long villageId);

    Integer selectDangerWarningNum(@Param("level") Integer level,
                                   @Param("quId") Long quId,
                                   @Param("townId") Long townId,
                                   @Param("villageId") Long villageId);

    List<DangerPoints> seletAllDangerPoints(@Param("level") Integer level,
                                            @Param("quId") Long quId,
                                            @Param("townId") Long townId,
                                            @Param("villageId") Long villageId);

    Integer selectHasDangerWarningNum();

    /**
     * 根据行政区（区县级）聚合统计
     * @param reqAO
     * @return
     */
    List<DangerPointsCountVO> getCountyDangerPointCount(DangerPointsReqVO reqAO);

    /**
     * 根据行政区（乡镇级）聚合统计
     * @param reqAO
     * @return
     */
    List<DangerPointsCountVO> getTownDangerPointCount(DangerPointsReqVO reqAO);

    List<DangerPointsCountVO> getVillageDangerPointCount(@Param("quId") Long quId,
                                                         @Param("townId") Long townId);

    List<DangerPointsCountVO> getSingleVillageDangerPointCount(@Param("quId") Long quId,
                                                               @Param("townId") Long townId,
                                                               @Param("villageId") Long villageId);

    List<TaskLocationVO> selectTaskLocation(@Param("villageName") String villageName);

    List<String> seletctByGeom(@Param("geom") String geom);

    void updateByCountryAndVillage(
            @Param("country") String country,
            @Param("village") String village,
            @Param("personLiable") String personLiable,
            @Param("personLiableTelephone") String personLiableTelephone,
            @Param("administrators") String administrators,
            @Param("administratorsTelephone") String administratorsTelephone,
            @Param("trafficAssistant") String trafficAssistant,
            @Param("trafficAssistantTelephone") String trafficAssistantTelephone,
            @Param("specialManager") String specialManager,
            @Param("specialManagerTelephone") String specialManagerTelephone
    );

    List<DangerPointsGridMembersMismatchDTO> selectMismatchByArea(@Param("area") String area);


    DangerPointsFileVO getDangerPointsFile(@Param("id")String id);

    void updateDangerPointsPictureA(DangerPointsPictureA dangerPointsPictureA);

    void updateDangerPointsPictureB(DangerPointsPictureB dangerPointsPictureB);
}
