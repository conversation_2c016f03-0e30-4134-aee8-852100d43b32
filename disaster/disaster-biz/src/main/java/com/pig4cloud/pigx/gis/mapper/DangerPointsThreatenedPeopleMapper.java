package com.pig4cloud.pigx.gis.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.gis.domain.DangerPointsThreatenedPeople;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DangerPointsThreatenedPeopleMapper extends BaseMapper<DangerPointsThreatenedPeople> {
	Page<DangerPointsThreatenedPeople> getList(@Param("page") Page<DangerPointsThreatenedPeople> page,
                                               @Param("dangerPointsId") String dangerPointsId);
}
