package com.pig4cloud.pigx.gis.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.data.datascope.PigxBaseMapper;
import com.pig4cloud.pigx.gis.entity.DataSourceDisaster3dEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DataSourceDisaster3dMapper extends PigxBaseMapper<DataSourceDisaster3dEntity> {


    List<DataSourceDisaster3dEntity> getDataSourceDisaster3dPage(@Param("page") Page<DataSourceDisaster3dEntity> page,
                                                                 @Param("name") String name);

    List<DataSourceDisaster3dEntity> getDataSourceDisaster3dPageVersion(@Param("page") Page<DataSourceDisaster3dEntity> page,
                                                                        @Param("monitorDataType") String monitorDataType,
                                                                        @Param("provincialCode") String provincialCode);
}