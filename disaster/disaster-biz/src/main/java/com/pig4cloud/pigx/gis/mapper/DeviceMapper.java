package com.pig4cloud.pigx.gis.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.common.data.datascope.PigxBaseMapper;
import com.pig4cloud.pigx.gis.domain.Devices;
import com.pig4cloud.pigx.gis.domain.EchartsProperty;
import com.pig4cloud.pigx.gis.entity.Device;
import com.pig4cloud.pigx.gis.vo.DeviceVO;
import com.pig4cloud.pigx.gis.vo.DevicesCompanyFenZuVO;
import com.pig4cloud.pigx.gis.vo.DevicesDangerOrRiskFenZuVO;
import com.pig4cloud.pigx.gis.vo.DevicesVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DeviceMapper extends PigxBaseMapper<Device> {

    /**
     * 新增监测设备
     *
     * @param devices 监测设备
     * @return 结果
     */
    int insertHgDevices(Devices devices);

    /**
     * 修改监测设备
     *
     * @param devices 监测设备
     * @return 结果
     */
    int updateHgDevices(Devices devices);


    /**
     * 批量删除监测设备
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteHgDevicesByIds(Integer[] ids);

    /**
     * 查询监测设备
     *
     * @param id 监测设备主键
     * @return 监测设备
     */
    DeviceVO selectHgDevicesById(@Param("id") Long id);

    /**
     * 获取测站类型分组
     *
     * @return
     */
    List<String> selectGroupByStationType(@Param("level") Integer level, @Param("quId") Long quId, @Param("townId") Long townId, @Param("villageId") Long villageId);

    /**
     * 查询监测设备列表
     *
     * @param devices 监测设备
     * @return 监测设备集合
     */
    Page<DevicesVO> selectHgDevicesList(@Param("devicesPage") Page<Devices> page, @Param("entity") Devices devices);

    List<DevicesDangerOrRiskFenZuVO> selectGroupByDangerOrRisk(@Param("level") Integer level, @Param("quId") Long quId, @Param("townId") Long townId, @Param("villageId") Long villageId);

    List<DevicesCompanyFenZuVO> selectGroupByCompany(@Param("level") Integer level, @Param("quId") Long quId, @Param("townId") Long townId, @Param("villageId") Long villageId);

    Integer selectDeviceTotal(@Param("level") Integer level, @Param("quId") Long quId, @Param("townId") Long townId, @Param("villageId") Long villageId);

    Integer selectOnlineDeviceTotal(@Param("level") Integer level, @Param("quId") Long quId, @Param("townId") Long townId, @Param("villageId") Long villageId);


    List<EchartsProperty> getDeviceTypeCount(@Param("monitorType") String monitorType);

    Integer getTotalDeviceCount();




    /**
     * 查询监测设备列表
     *
     * @param device 监测设备
     * @return 监测设备集合
     */
    Page<DeviceVO> selectHgDeviceList(@Param("devicesPage") Page<Device> page, @Param("entity") Device device);

}