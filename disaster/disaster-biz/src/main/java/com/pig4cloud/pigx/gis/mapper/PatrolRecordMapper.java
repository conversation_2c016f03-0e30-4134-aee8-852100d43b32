package com.pig4cloud.pigx.gis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.gis.domain.PatrolRecord;
import com.pig4cloud.pigx.gis.vo.PatrolRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 巡查记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface PatrolRecordMapper extends BaseMapper<PatrolRecord> {

    /**
     * 分页查询巡查记录
     *
     * @param page 分页对象
     * @param patrolRecordVO 查询条件
     * @return 分页结果
     */
    Page<PatrolRecord> selectPatrolRecordPage(@Param("page") Page<PatrolRecord> page,
                                              @Param("patrolRecordVO") PatrolRecordVO patrolRecordVO);

    /**
     * 查询巡查记录列表
     *
     * @param patrolRecordVO 查询条件
     * @return 巡查记录列表
     */
    List<PatrolRecord> selectPatrolRecordList(@Param("patrolRecordVO") PatrolRecordVO patrolRecordVO);

    /**
     * 根据巡查任务ID查询巡查记录
     *
     * @param patrolTaskId 巡查任务ID
     * @return 巡查记录列表
     */
    List<PatrolRecord> selectByPatrolTaskId(@Param("patrolTaskId") Long patrolTaskId);

}
