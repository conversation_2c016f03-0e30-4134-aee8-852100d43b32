package com.pig4cloud.pigx.gis.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.gis.domain.EchartsProperty;
import com.pig4cloud.pigx.gis.domain.RiskZone;
import com.pig4cloud.pigx.gis.param.RiskZonePictureParam;
import com.pig4cloud.pigx.gis.vo.RiskZoneQrCodeVO;
import com.pig4cloud.pigx.gis.vo.RiskZoneVO;
import com.pig4cloud.pigx.gis.vo.RiskZonesCountVO;
import com.pig4cloud.pigx.gis.vo.TaskLocationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 风险区Mapper接口
 *
 * <AUTHOR>
 * @date 2025年6月2日15:14:58
 */


@Mapper
public interface RiskZoneMapper extends BaseMapper<RiskZone> {


    /**
     * 获取行政村名称分组
     *
     * @param xz
     * @return
     */
    List<String> selectGroupByXzc(@Param("level") Integer level,
                                  @Param("xz") String xz,
                                  @Param("villageId") Long villageId);


    /**
     * 获取乡镇名称分组
     *
     * @return
     */
    List<String> selectGroupByXz(@Param("townId") Long townId, @Param("quId") Long quId);

    /**
     * 获取分级名称分组
     *
     * @return
     */
    List<String> selectGroupByFj(@Param("quId") Long quId, @Param("townId") Long townId, @Param("villageId") Long villageId);


    /**
     * 根据指定字段统计
     *
     * @param riskZoneVO
     * @return
     */
    List<EchartsProperty> selectStatistics(RiskZoneVO riskZoneVO);


    /**
     * 分页查询风险区列表
     *
     * @param page       分页信息
     * @param riskZoneVO 风险区信息
     * @return 风险区集合
     */
    IPage<RiskZone> selectRiskZonePage(@Param("riskZonePage") Page<RiskZone> page, @Param("entity") RiskZoneVO riskZoneVO);

    List<String> selectGroupByQx(@Param("quId") Long quId);


    List<RiskZonesCountVO> getQxRiskZoneCount();

    List<RiskZonesCountVO> getXzRiskZoneCount(@Param("quId") Long quId);

    List<RiskZonesCountVO> getXzcRiskZoneCount(@Param("quId") Long quId, @Param("townId") Long townId);

    List<RiskZonesCountVO> getSingleXzcRiskZoneCount(@Param("quId") Long quId, @Param("townId") Long townId, @Param("villageId") Long villageId);

    List<TaskLocationVO> selectTaskLocation(@Param("villageName") String villageName);

    RiskZone selectByTybh(@Param("tybh") String pointId);

    Long selectCountByFj(@Param("entity") RiskZoneVO riskZoneVO);

    void editPicture(@Param("param") RiskZonePictureParam riskZonePictureParam);

    RiskZoneQrCodeVO getInfo(@Param("id")Integer id);
}
