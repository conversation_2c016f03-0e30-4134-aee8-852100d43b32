package com.pig4cloud.pigx.gis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.gis.domain.RiskZoneThreatenedPeople;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface RiskZoneThreatenedPeopleMapper extends BaseMapper<RiskZoneThreatenedPeople> {


	Page<RiskZoneThreatenedPeople> getList(@Param("page") Page<RiskZoneThreatenedPeople> page,
                                           @Param("riskZoneId") Integer riskZoneId);
}
