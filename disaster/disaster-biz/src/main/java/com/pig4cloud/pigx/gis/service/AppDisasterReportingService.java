package com.pig4cloud.pigx.gis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.gis.dto.AppDisasterReportingAddDTO;
import com.pig4cloud.pigx.gis.dto.AppDisasterReportingUpdateDTO;
import com.pig4cloud.pigx.gis.entity.AppDisasterReporting;

public interface AppDisasterReportingService extends IService<AppDisasterReporting> {

    boolean addDisasterReporting(AppDisasterReportingAddDTO addDTO);

    boolean updateDisasterReporting(AppDisasterReportingUpdateDTO updateDTO);

}