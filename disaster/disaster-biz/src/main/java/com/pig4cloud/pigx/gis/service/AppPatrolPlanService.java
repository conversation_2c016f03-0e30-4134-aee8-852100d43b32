package com.pig4cloud.pigx.gis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.gis.domain.AppPatrolPlan;
import com.pig4cloud.pigx.gis.vo.AppPatrolPlanVO;

import java.util.List;

public interface AppPatrolPlanService extends IService<AppPatrolPlan> {
	void add(List<String> pointIdList, AppPatrolPlan patrolPlan);

	Boolean selectPatrolPeople(Long regionId);

	List<AppPatrolPlanVO> getpatrolList(Page<AppPatrolPlan> appPatrolPlanPage, Integer taskStatus, Integer taskType, Integer patrolType, String startTime, String endTime);
}
