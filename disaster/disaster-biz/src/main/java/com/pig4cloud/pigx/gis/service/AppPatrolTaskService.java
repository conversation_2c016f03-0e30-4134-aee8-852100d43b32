package com.pig4cloud.pigx.gis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.gis.domain.AppPatrolTask;
import com.pig4cloud.pigx.gis.vo.AppPatrolTaskVO;

import java.util.List;

/**
 * 巡查任务表 Service 接口
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface AppPatrolTaskService extends IService<AppPatrolTask> {

    /**
     * 分页查询巡查任务列表
     *
     * @param page 分页参数
     * @param appPatrolTask 查询条件
     * @return 巡查任务列表
     */
    List<AppPatrolTaskVO> selectAppPatrolTaskPage(Page<AppPatrolTask> page, AppPatrolTask appPatrolTask);

    /**
     * 新增巡查任务
     *
     * @param photoLongIds 照片id
     * @param appPatrolTask 巡查任务
     * @return 结果
     */
    boolean insertAppPatrolTask(List<Long> photoLongIds,AppPatrolTask appPatrolTask);

    /**
     * 修改巡查任务
     *
     * @param appPatrolTask 巡查任务
     * @return 结果
     */
    boolean updateAppPatrolTask(AppPatrolTask appPatrolTask);

    /**
     * 批量删除巡查任务
     *
     * @param ids 需要删除的巡查任务主键集合
     * @return 结果
     */
    boolean deleteAppPatrolTaskByIds(Long[] ids);

    /**
     * 删除巡查任务信息
     *
     * @param id 巡查任务主键
     * @return 结果
     */
    boolean deleteAppPatrolTaskById(Long id);

    /**
     * 根据巡查计划ID查询任务列表
     *
     * @param planId 巡查计划ID
     * @return 任务列表
     */
    List<AppPatrolTask> selectByPlanId(Long planId);

    /**
     * 根据点位ID查询任务列表
     *
     * @param pointId 点位ID
     * @return 任务列表
     */
    List<AppPatrolTask> selectByPointId(Long pointId);

    /**
     * 根据点位类型查询任务列表
     *
     * @param pointType 点位类型
     * @return 任务列表
     */
    List<AppPatrolTask> selectByPointType(Integer pointType);
}
