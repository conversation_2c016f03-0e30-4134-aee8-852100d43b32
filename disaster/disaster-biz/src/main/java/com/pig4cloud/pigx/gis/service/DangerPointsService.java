package com.pig4cloud.pigx.gis.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pig4cloud.pigx.gis.domain.DangerPoints;
import com.pig4cloud.pigx.gis.domain.EchartsProperty;
import com.pig4cloud.pigx.gis.dto.DangerPointStaffExcelDTO;
import com.pig4cloud.pigx.gis.dto.DangerPointsGridMembersMismatchDTO;
import com.pig4cloud.pigx.gis.param.DangerPointsPictureA;
import com.pig4cloud.pigx.gis.param.DangerPointsPictureB;
import com.pig4cloud.pigx.gis.vo.*;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface DangerPointsService {

    /**
     * 修改隐患点
     *
     * @param dangerPoints 隐患点
     * @return 结果
     */
    void updateHgDangerPoints(DangerPoints dangerPoints);


    /**
     * 新增隐患点
     *
     * @param dangerPoints 隐患点
     * @return 结果
     */
    void insertHgDangerPoints(DangerPoints dangerPoints);


    /**
     * 批量删除隐患点
     *
     * @param ids 需要删除的隐患点主键集合
     * @return 结果
     */
    int deleteHgDangerPointsByIds(String[] ids);

    /**
     * 查询隐患点
     *
     * @param id 隐患点主键
     * @return 隐患点
     */
    DangerPoints selectHgDangerPointsById(String id);

    List<String> selectGroupByStabilityStatus();

    List<String> selectGroupByDangerPointsType();

    List<EchartsProperty> selectStatisticsByDangerPointsType(DangerPointsReqVO dangerPoints);

    DangerPointsStatisticsVO selectDangerWarningNum();

    /**
     * 查询隐患点列表
     *
     * @param hgDangerPoints 隐患点
     * @return 隐患点集合
     */
    Page<DangerPoints> selectHgDangerPointsList(IPage<DangerPoints> pointPage, DangerPointsReqVO hgDangerPoints);

    Integer dangerPointImport(MultipartFile file) throws IOException;

    List<EchartsProperty> getStatisticsByStabilityStatus(DangerPointsReqVO dangerPoints);

    List<DangerPointsCountVO> getAreaDangerPointCount();

    /**
     * 地图上，根据行政区（区县级，乡镇级）聚合统计
     * @param reqAO
     * @return
     */
    DangerPointsMapAdministrativeStatisticsVO getAdministrativeStatistics(DangerPointsReqVO reqAO);

    List<TaskLocationVO> selectTaskLocation(String villageName);

    List<DangerPointsGridMembersMismatchDTO> batchUpdateGridMembers(List<DangerPointStaffExcelDTO> list, String area);

    void changeVerificationFlag(String[] ids,Boolean verificationFlag);


    DangerPointsFileVO getDangerPointsFile(String id);

    void updateDangerPointsPictureA(DangerPointsPictureA dangerPointsPictureA);

    void updateDangerPointsPictureB(DangerPointsPictureB dangerPointsPictureB);

    DangerPointsQrCodeVO getInfo(String id);

    void exportWorkFile(String id, HttpServletResponse response);

    void exportAvoidRiskFile(String id, HttpServletResponse response);
}
