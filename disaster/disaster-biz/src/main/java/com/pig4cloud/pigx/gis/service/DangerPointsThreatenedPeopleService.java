package com.pig4cloud.pigx.gis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.gis.domain.DangerPointsThreatenedPeople;
import com.pig4cloud.pigx.gis.vo.DangerPointsThreatenedPeopleVO;


public interface DangerPointsThreatenedPeopleService extends IService<DangerPointsThreatenedPeople> {
	DangerPointsThreatenedPeopleVO getList(Page<DangerPointsThreatenedPeople> hgDangerPointsThreatenedPeoplePage, String dangerPointsId);

	void add(DangerPointsThreatenedPeople dangerPointsThreatenedPeople);

	void edit(DangerPointsThreatenedPeople dangerPointsThreatenedPeople);
}
