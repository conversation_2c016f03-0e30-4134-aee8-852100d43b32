package com.pig4cloud.pigx.gis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.gis.entity.DataSource3dEntity;
import org.springframework.web.multipart.MultipartFile;


import java.time.LocalDate;

public interface DataSource3dService extends IService<DataSource3dEntity> {

    String saveUploadTask(MultipartFile file, String name, LocalDate date);

    void processAsync(String sourceId, MultipartFile file);

    DataSource3dEntity selectById(String sourceId);

}