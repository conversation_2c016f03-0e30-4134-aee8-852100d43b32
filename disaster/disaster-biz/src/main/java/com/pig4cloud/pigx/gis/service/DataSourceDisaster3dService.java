package com.pig4cloud.pigx.gis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.gis.entity.DataSourceDisaster3dEntity;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDate;
import java.util.List;

public interface DataSourceDisaster3dService extends IService<DataSourceDisaster3dEntity> {


    String saveUploadTask(MultipartFile file, String monitorDataType, String provincialCode, String name, LocalDate date);

    void processDataSourceDisaster3dAsync(String sourceId, MultipartFile file);

    DataSourceDisaster3dEntity selectById(String sourceId);

    List<DataSourceDisaster3dEntity> getDataSourceDisaster3dPage(Page<DataSourceDisaster3dEntity> page, String name);

    List<DataSourceDisaster3dEntity> getDataSourceDisaster3dPageVersion(Page<DataSourceDisaster3dEntity> dataSourceDisaster3dEntityPage, String monitorDataType, String provincialCode);
}