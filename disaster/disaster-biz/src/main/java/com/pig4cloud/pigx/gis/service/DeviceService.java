package com.pig4cloud.pigx.gis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.gis.domain.Devices;
import com.pig4cloud.pigx.gis.domain.EchartsProperty;
import com.pig4cloud.pigx.gis.entity.Device;
import com.pig4cloud.pigx.gis.vo.DeviceNumberCountVO;
import com.pig4cloud.pigx.gis.vo.DeviceVO;
import com.pig4cloud.pigx.gis.vo.DevicesAllGroupVO;
import com.pig4cloud.pigx.gis.vo.DevicesVO;

import java.util.List;

public interface DeviceService extends IService<Device> {

    /**
     * 新增监测设备
     *
     * @param devices 监测设备
     * @return 结果
     */

    int insertHgDevices(Devices devices);


    /**
     * 修改监测设备
     *
     * @param devices 监测设备
     * @return 结果
     */
    int updateHgDevices(Devices devices);

    /**
     * 批量删除监测设备
     *
     * @param ids 需要删除的监测设备主键集合
     * @return 结果
     */
    int deleteHgDevicesByIds(Integer[] ids);

    /**
     * 查询监测设备
     *
     * @param id 监测设备主键
     * @return 监测设备
     */
    DeviceVO selectHgDevicesById(Long id);


    /**
     * 获取测站类型分组
     *
     * @return
     */
    List<String> selectGroupByStationType();

    /**
     * 查询监测设备列表
     *
     * @param devices 监测设备
     * @return 监测设备集合
     */
    Page<DevicesVO> selectHgDevicesList(Page<Devices> devicePage, Devices devices);

    /**
     * 获取所有分组  隐患点和风险区的分组    设备制造商的分组
     *
     * @return
     */
    DevicesAllGroupVO allGroup();

    /**
     * 根据监测业务类型（1隐患点，2风险区，null全部)值，获取监测设备分类统计结果
     * @param monitorType
     * @return
     */
    List<EchartsProperty> getSensorTypeCount(String monitorType);

    DeviceNumberCountVO deviceNumberCount();

    List<EchartsProperty> selectStatisticsByWarningLevelType(Integer monitorType);

    Page<DeviceVO> selectDeviceList(Page<Device> devicePage, Device reqVo);
}