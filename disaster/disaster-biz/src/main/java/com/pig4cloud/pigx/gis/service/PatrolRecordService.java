package com.pig4cloud.pigx.gis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.gis.domain.PatrolRecord;
import com.pig4cloud.pigx.gis.vo.PatrolRecordVO;

import java.util.List;

/**
 * 巡查记录服务接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface PatrolRecordService extends IService<PatrolRecord> {

    /**
     * 分页查询巡查记录
     *
     * @param page 分页对象
     * @param patrolRecordVO 查询条件
     * @return 分页结果
     */
    Page<PatrolRecord> selectPatrolRecordPage(Page<PatrolRecord> page, PatrolRecordVO patrolRecordVO);

    /**
     * 查询巡查记录列表
     *
     * @param patrolRecordVO 查询条件
     * @return 巡查记录列表
     */
    List<PatrolRecord> selectPatrolRecordList(PatrolRecordVO patrolRecordVO);

    /**
     * 新增巡查记录
     *
     * @param patrolRecord 巡查记录
     */
    void insertPatrolRecord(PatrolRecord patrolRecord);

    /**
     * 修改巡查记录
     *
     * @param patrolRecord 巡查记录
     */
    void updatePatrolRecord(PatrolRecord patrolRecord);

    /**
     * 根据巡查任务ID查询巡查记录
     *
     * @param patrolTaskId 巡查任务ID
     * @return 巡查记录列表
     */
    List<PatrolRecord> selectByPatrolTaskId(Long patrolTaskId);

}
