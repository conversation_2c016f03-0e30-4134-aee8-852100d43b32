package com.pig4cloud.pigx.gis.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.gis.domain.EchartsProperty;
import com.pig4cloud.pigx.gis.domain.RiskZone;
import com.pig4cloud.pigx.gis.param.RiskZonePictureParam;
import com.pig4cloud.pigx.gis.vo.*;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;


/**
 * 风险区Service接口
 *
 * <AUTHOR>
 * @date 2025年6月2日15:02:52
 */
public interface RiskZoneService extends IService<RiskZone> {




	/**
	 * 新增风险区
	 *
	 * @param riskZone 风险区
	 * @return 结果
	 */
	int insert(RiskZone riskZone);


	/**
	 * 修改风险区
	 *
	 * @param riskZone 风险区
	 * @return 结果
	 */
	int update(RiskZone riskZone);


	/**
	 * 删除风险区信息
	 *
	 * @param ids 风险区ID
	 * @return 结果
	 */
	int deleteByIds(Integer[] ids);



	/**
	 * 查询风险区
	 *
	 * @param id 风险区ID
	 * @return 风险区
	 */
	RiskZone selectById(Integer id);


	/**
	 * 获取行政村名称分组
	 *
	 * @param xz
	 * @return
	 */
	List<String> selectGroupByXzc(String xz);


	/**
	 * 获取乡镇名称分组
	 *
	 * @return
	 */
	List<String>  selectGroupByXz();

	/**
	 * 获取分级名称分组
	 *
	 * @return
	 */
	List<String> selectGroupByFj();

	List<EchartsProperty> selectStatisticsByXz(RiskZoneVO riskZoneVO);



	/**
	 * 查询风险区列表
	 *
	 * @param riskZoneVO 风险区
	 * @return 风险区集合
	 */
	IPage<RiskZone> selectList(Page<RiskZone> page, RiskZoneVO riskZoneVO);

	/**
	 * 获取乡镇名称分组
	 *
	 * @return
	 */
	List<String> selectGroupByQx();

    List<RiskZonesCountVO> getRiskZoneCount();

    List<TaskLocationVO> selectTaskLocation(String villageName);

    RiskZoneStatisticsVO selectStatisticsByFj(RiskZoneVO riskZoneVO);

	void editPicture(RiskZonePictureParam riskZonePictureParam);

	RiskZoneQrCodeVO getInfo(Integer id);

    void exportControlListFile(Integer id, HttpServletResponse response);

	void exportAvoidRiskFile(Integer id, HttpServletResponse response);
}

