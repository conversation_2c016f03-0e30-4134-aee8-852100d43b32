package com.pig4cloud.pigx.gis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.gis.domain.RiskZoneThreatenedPeople;
import com.pig4cloud.pigx.gis.vo.RiskZoneThreatenedPeopleVO;

public interface RiskZoneThreatenedPeopleService extends IService<RiskZoneThreatenedPeople> {
	RiskZoneThreatenedPeopleVO getList(Page<RiskZoneThreatenedPeople> page, Integer riskZoneId);

	void add(RiskZoneThreatenedPeople riskZoneThreatenedPeople);

	void edit(RiskZoneThreatenedPeople riskZoneThreatenedPeople);
}
