package com.pig4cloud.pigx.gis.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.pig4cloud.pigx.gis.domain.WarningRuleTemplate;
import com.pig4cloud.pigx.gis.vo.WarningRuleTemplateRequestVO;
import com.pig4cloud.pigx.gis.vo.WarningRuleTemplateResponseVO;
import lombok.NonNull;

public interface WarningRuleTemplateService extends IService<WarningRuleTemplate> {


	IPage<WarningRuleTemplateResponseVO> getWaringRuleTemplate(String ruleName, Long pageNo, Long pageSize);

	Integer addWaringRuleTemplate(WarningRuleTemplateRequestVO hgWarningRuleTemplateRequestVO);

	Integer updateWaringRuleTemplate(WarningRuleTemplateRequestVO hgWarningRuleTemplateRequestVO);

	Integer deleteWaringRuleTemplate(@NonNull String code);
}
