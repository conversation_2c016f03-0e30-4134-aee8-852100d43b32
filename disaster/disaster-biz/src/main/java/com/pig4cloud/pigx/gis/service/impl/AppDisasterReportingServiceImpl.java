package com.pig4cloud.pigx.gis.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.admin.api.entity.SysDictItem;
import com.pig4cloud.pigx.common.core.constant.CommonConstants;
import com.pig4cloud.pigx.gis.constant.DisasterConstants;
import com.pig4cloud.pigx.gis.dto.AppDisasterReportingAddDTO;
import com.pig4cloud.pigx.gis.dto.AppDisasterReportingUpdateDTO;
import com.pig4cloud.pigx.gis.entity.AppDisasterReporting;
import com.pig4cloud.pigx.gis.entity.AppDisasterReportingFile;
import com.pig4cloud.pigx.gis.entity.AppDisasterReportingReviewer;
import com.pig4cloud.pigx.gis.mapper.AppDisasterReportingFileMapper;
import com.pig4cloud.pigx.gis.mapper.AppDisasterReportingMapper;
import com.pig4cloud.pigx.gis.mapper.AppDisasterReportingReviewerMapper;
import com.pig4cloud.pigx.gis.service.AppDisasterReportingService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 一键报灾
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Service
@RequiredArgsConstructor
public class AppDisasterReportingServiceImpl extends ServiceImpl<AppDisasterReportingMapper, AppDisasterReporting> implements AppDisasterReportingService {

    private final AppDisasterReportingFileMapper reportingFileMapper;
    private final AppDisasterReportingReviewerMapper reportingReviewerMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addDisasterReporting(AppDisasterReportingAddDTO addDTO) {
        // 数据转换
        AppDisasterReporting entity = new AppDisasterReporting();
        BeanUtils.copyProperties(addDTO, entity);
        entity.setStatus(DisasterConstants.DISASTER_REPORTING_STATUS_UNREAD);
        int num = baseMapper.insert(entity);
        if (num == 0) {
            throw new RuntimeException("保存一键报灾数据失败，请联系管理员处理");
        }

        Long disasterId = entity.getId();
        // 保存关联的现场照片
        if (addDTO.getFileIdList() != null && !addDTO.getFileIdList().isEmpty()) {
            List<AppDisasterReportingFile> collect = addDTO.getFileIdList().stream().map(x -> {
                AppDisasterReportingFile rFile = new AppDisasterReportingFile();
                rFile.setDisasterId(disasterId);
                rFile.setFileId(Long.valueOf(x));
                return rFile;
            }).collect(Collectors.toList());
            for (AppDisasterReportingFile reviewer : collect) {
                reportingFileMapper.insert(reviewer);
            }
        }

        // 保存通知人员
        if (addDTO.getReviewerIdList() != null && !addDTO.getReviewerIdList().isEmpty()) {
            List<AppDisasterReportingReviewer> collect = addDTO.getReviewerIdList().stream().map(x -> {
                AppDisasterReportingReviewer reviewer = new AppDisasterReportingReviewer();
                reviewer.setDisasterReportingId(disasterId);
                reviewer.setUserId(x);
                return reviewer;
            }).collect(Collectors.toList());
            // 批量新增数据
            for (AppDisasterReportingReviewer reviewer : collect) {
                reportingReviewerMapper.insert(reviewer);
            }
        }
        return true;
    }

    @Override
    public boolean updateDisasterReporting(AppDisasterReportingUpdateDTO updateDTO) {
        // 数据转换
        AppDisasterReporting entity = new AppDisasterReporting();
        BeanUtils.copyProperties(updateDTO, entity);
        int num = baseMapper.updateById(entity);
        return num > 0;
    }
}