package com.pig4cloud.pigx.gis.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.gis.domain.AppPatrolPlan;
import com.pig4cloud.pigx.gis.domain.AppPatrolPlanPoint;
import com.pig4cloud.pigx.gis.domain.AppPatrolPlanReviewer;
import com.pig4cloud.pigx.gis.domain.RiskZone;
import com.pig4cloud.pigx.gis.dto.AppPatrolPlanPointDTO;
import com.pig4cloud.pigx.gis.mapper.*;
import com.pig4cloud.pigx.gis.service.AppPatrolPlanService;
import com.pig4cloud.pigx.gis.utils.DateUtils;
import com.pig4cloud.pigx.gis.utils.UserDeptLevelUtils;
import com.pig4cloud.pigx.gis.vo.AppPatrolPlanVO;
import com.pig4cloud.pigx.admin.api.entity.SysDept;
import com.pig4cloud.pigx.admin.api.entity.SysUser;
import com.pig4cloud.pigx.admin.mapper.SysUserMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class AppPatrolPlanServiceImpl extends ServiceImpl<AppPatrolPlanMapper, AppPatrolPlan> implements AppPatrolPlanService {

	@Resource
	private AppPatrolPlanMapper appPatrolPlanMapper;

	@Resource
	private AppPatrolPlanPointMapper appPatrolPlanPointMapper;

	@Resource
	private RiskZoneMapper riskZoneMapper;

	@Resource
	private DangerPointsMapper dangerPointsMapper;

	@Resource
	private AppPatrolPlanReviewerMapper appPatrolPlanReviewerMapper;



	@Resource
	private SysUserMapper sysUserMapper;



	@Override
	@Transactional
	public void add(List<String> pointIdList, AppPatrolPlan patrolPlan) {

		//1:是隐患点省编号 2:是风险区省编号
		if (patrolPlan.getPatrolType() == 1) {
			//通过所属政区村id获取所属政区
			String region = UserDeptLevelUtils.getDeptPathWithoutRoot(Long.valueOf(patrolPlan.getRegionId())); //所属政区 县镇村
			patrolPlan.setRegion(region);
			String patrolPeople =appPatrolPlanMapper.selectPatrolPeople(patrolPlan.getRegionId());  //巡查人姓名
			patrolPlan.setPatrolPeopleName(patrolPeople);
			//字符串形式的雪花算法ID
			patrolPlan.setGroupId(String.valueOf(IdWorker.getId()));
			//插入数据至巡查计划表
			appPatrolPlanMapper.insert(patrolPlan);


			// 巡查任务审阅人员（抄送人员)
			informPlanReviewer(patrolPlan);
			int order = 1;
			//插入数据至 巡查计划关联的隐患点或风险区范围内的隐患点表
			for (String pointId : pointIdList) {

				AppPatrolPlanPoint appPatrolPlanPoint = new AppPatrolPlanPoint();
				appPatrolPlanPoint.setPlanId(patrolPlan.getId());
				appPatrolPlanPoint.setPointId(pointId);
				appPatrolPlanPoint.setSortOrder(order++);
				appPatrolPlanPointMapper.insert(appPatrolPlanPoint);
			}
		} else if (patrolPlan.getPatrolType() == 2) {
			//通过多个风险区id循环插入数据至巡查计划表
			for (String pointId : pointIdList) {
				//通过风险区id查询对应风险区所有属性信息,在属性信息geon属性去查找隐患点表查找具体隐患点
				RiskZone riskZone = riskZoneMapper.selectByTybh(pointId);
				List<String> dangerPointsProvinceIds = dangerPointsMapper.seletctByGeom(riskZone.getGeom());
				if(dangerPointsProvinceIds.isEmpty()){
					//没有找到对应的隐患点 跳过当前风险区不添加巡查计划
					continue;
				}
				//插入数据至巡查计划表
				patrolPlan.setRiskZoneId(pointId);
				patrolPlan.setRegion(UserDeptLevelUtils.getDeptPathWithoutRoot(Long.valueOf(patrolPlan.getRegionId())));  //所属政区 县镇村
				patrolPlan.setPatrolPeopleName(appPatrolPlanMapper.selectPatrolPeople(patrolPlan.getRegionId()));  //巡查人姓名
				appPatrolPlanMapper.insert(patrolPlan);


				// 巡查任务审阅人员（抄送人员)
				informPlanReviewer(patrolPlan);
				int order = 1;
				//插入数据至 巡查计划关联的隐患点或风险区范围内的隐患点表
				for (String dangerPointsProvinceId : dangerPointsProvinceIds) {
					AppPatrolPlanPoint appPatrolPlanPoint = new AppPatrolPlanPoint();
					appPatrolPlanPoint.setPlanId(patrolPlan.getId());
					appPatrolPlanPoint.setPointId(dangerPointsProvinceId);
					appPatrolPlanPoint.setSortOrder(order++);
					appPatrolPlanPointMapper.insert(appPatrolPlanPoint);
				}
			}
		} else {
			throw new RuntimeException("请选择正确的巡查类型");
		}

	}


	/**
	 * // 巡查任务审阅人员（抄送人员)  入库
	 * @param patrolPlan
	 */
	private void informPlanReviewer(AppPatrolPlan patrolPlan) {
		//通过所属政区，村ID获取其上级政区信息
		SysDept parentRegionInfo = UserDeptLevelUtils.getParentDeptInfo(patrolPlan.getRegionId());

		//通过其上级部门信息查询上级政区内有哪些用户
		LambdaQueryWrapper<SysUser> sysUserQueryWrapper = new LambdaQueryWrapper<>();
		sysUserQueryWrapper.eq(SysUser::getDeptId, parentRegionInfo.getDeptId());
		List<SysUser> patrolReviewerList = sysUserMapper.selectList(sysUserQueryWrapper);
		//根据用户数量循环插入数据至巡查任务审阅人员表
		for (SysUser patrolReviewer : patrolReviewerList) {
			AppPatrolPlanReviewer appPatrolPlanReviewer = new AppPatrolPlanReviewer();
			appPatrolPlanReviewer.setPlanId(patrolPlan.getId());
			appPatrolPlanReviewer.setUserId(patrolReviewer.getUserId());
			appPatrolPlanReviewerMapper.insert(appPatrolPlanReviewer);
		}
	}

	@Override
	public Boolean selectPatrolPeople(Long regionId) {
		String patrolPeople =appPatrolPlanMapper.selectPatrolPeople(regionId);
		return patrolPeople != null;
	}

	@Override
	public List<AppPatrolPlanVO> getpatrolList(Page<AppPatrolPlan> page, Integer taskStatus, Integer taskType, Integer patrolType, String startTime, String endTime) {
		Page<AppPatrolPlan> patrolList=appPatrolPlanMapper.getpatrolList(page, taskStatus, taskType, patrolType, DateUtils.parseDate(startTime),DateUtils.parseDate(endTime));

		List<AppPatrolPlan> modifiedList = new ArrayList<>(patrolList.getRecords());
		if(modifiedList.isEmpty()){
			return null;
		}
		List<AppPatrolPlanVO> appPatrolPlanVOS = new ArrayList<>();
		for (AppPatrolPlan patrolPlan : modifiedList) {
			AppPatrolPlanVO appPatrolPlanVO = new AppPatrolPlanVO();
			appPatrolPlanVO.setPatrolPlan(patrolPlan);
			List<AppPatrolPlanPointDTO> appPatrolPlanPointDTOList = appPatrolPlanPointMapper.selectListByPlainId(patrolPlan.getId());
			appPatrolPlanVO.setPatrolPlanPointList(appPatrolPlanPointDTOList);
			appPatrolPlanVOS.add(appPatrolPlanVO);
		}
		return appPatrolPlanVOS;
	}
}