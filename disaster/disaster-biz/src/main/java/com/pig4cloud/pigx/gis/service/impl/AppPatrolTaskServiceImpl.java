package com.pig4cloud.pigx.gis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.gis.domain.AppPatrolTask;
import com.pig4cloud.pigx.gis.domain.AppPatrolTaskFile;
import com.pig4cloud.pigx.gis.mapper.AppPatrolTaskFileMapper;
import com.pig4cloud.pigx.gis.mapper.AppPatrolTaskMapper;
import com.pig4cloud.pigx.gis.service.AppPatrolTaskService;
import com.pig4cloud.pigx.gis.vo.AppPatrolTaskPhotoVO;
import com.pig4cloud.pigx.gis.vo.AppPatrolTaskVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 巡查任务表 Service 实现类
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Slf4j
@Service
public class AppPatrolTaskServiceImpl extends ServiceImpl<AppPatrolTaskMapper, AppPatrolTask> implements AppPatrolTaskService {


    @Resource
    private AppPatrolTaskFileMapper appPatrolTaskFileMapper;



    @Override
    public List<AppPatrolTaskVO> selectAppPatrolTaskPage(Page<AppPatrolTask> page, AppPatrolTask appPatrolTask) {
        LambdaQueryWrapper<AppPatrolTask> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据巡查计划ID查询
        if (appPatrolTask.getPlanId() != null) {
            queryWrapper.eq(AppPatrolTask::getPlanId, appPatrolTask.getPlanId());
        }
        
        // 根据点位ID查询
        if (appPatrolTask.getPointId() != null) {
            queryWrapper.eq(AppPatrolTask::getPointId, appPatrolTask.getPointId());
        }
        
        // 根据点位类型查询
        if (appPatrolTask.getPointType() != null) {
            queryWrapper.eq(AppPatrolTask::getPointType, appPatrolTask.getPointType());
        }
        
        // 根据是否存在风险查询
        if (appPatrolTask.getRiskFlag() != null) {
            queryWrapper.eq(AppPatrolTask::getRiskFlag, appPatrolTask.getRiskFlag());
        }
        
        // 根据是否需要技术支撑查询
        if (appPatrolTask.getTechnicalSupportFlag() != null) {
            queryWrapper.eq(AppPatrolTask::getTechnicalSupportFlag, appPatrolTask.getTechnicalSupportFlag());
        }
        
        // 根据现场情况描述模糊查询
        if (StringUtils.hasText(appPatrolTask.getDescription())) {
            queryWrapper.like(AppPatrolTask::getDescription, appPatrolTask.getDescription());
        }
        
        // 排除已删除的记录
        queryWrapper.eq(AppPatrolTask::getDelFlag, "0");
        
        // 按排序值和创建时间排序
        queryWrapper.orderByAsc(AppPatrolTask::getSortOrder)
                   .orderByDesc(AppPatrolTask::getCreateTime);


        List<AppPatrolTaskVO> appPatrolTaskVOS = new ArrayList<>();
        List<AppPatrolTask> appPatrolTaskList = this.page(page, queryWrapper).getRecords();
        // 遍历巡查任务表,为其添加照片信息返回给前端
        for (AppPatrolTask patrolTask : appPatrolTaskList) {
            AppPatrolTaskVO appPatrolTaskVO = new AppPatrolTaskVO();
            appPatrolTaskVO.setAppPatrolTask(patrolTask);
            //通过任务id去查询当前任务的多个照片信息
            List<AppPatrolTaskPhotoVO> AppPatrolTaskPhotoVO =appPatrolTaskFileMapper.selectFileListByTaskId(patrolTask.getId());
            if(AppPatrolTaskPhotoVO != null){
                //设置其照信息url
                for (AppPatrolTaskPhotoVO appPatrolTaskPhotoVO : AppPatrolTaskPhotoVO) {
                    appPatrolTaskPhotoVO.setFileUrl(String.format("/admin/sys-file/oss/file?fileName=%s", appPatrolTaskPhotoVO.getFileName()));
                }
            }
            appPatrolTaskVO.setAppPatrolTaskPhotoVO(AppPatrolTaskPhotoVO);

            appPatrolTaskVOS.add(appPatrolTaskVO);

        }

        return appPatrolTaskVOS;
    }

    @Override
    public boolean insertAppPatrolTask(List<Long> photoLongIds,AppPatrolTask appPatrolTask) {
        // 设置创建时间
        appPatrolTask.setCreateTime(new Date());
        // 设置删除标志  0未删除 1已删除
        appPatrolTask.setDelFlag("0");

        boolean save = this.save(appPatrolTask);

        for (Long photoLongId : photoLongIds) {
            AppPatrolTaskFile appPatrolTaskFile = new AppPatrolTaskFile();
            appPatrolTaskFile.setTaskId(appPatrolTask.getId());
            appPatrolTaskFile.setFileId(photoLongId);
            appPatrolTaskFileMapper.insert(appPatrolTaskFile);
        }

        return save;
    }

    @Override
    public boolean updateAppPatrolTask(AppPatrolTask appPatrolTask) {
        // 设置修改时间
        appPatrolTask.setUpdateTime(new Date());
        
        return this.updateById(appPatrolTask);
    }

    @Override
    public boolean deleteAppPatrolTaskByIds(Long[] ids) {
        if (ids == null || ids.length == 0) {
            return false;
        }
        
        // 逻辑删除：设置删除标志为1
        List<AppPatrolTask> updateList = Arrays.stream(ids)
                .map(id -> {
                    AppPatrolTask task = new AppPatrolTask();
                    task.setId(id);
                    task.setDelFlag("1");
                    task.setUpdateTime(new Date());
                    return task;
                })
                .toList();
        
        return this.updateBatchById(updateList);
    }

    @Override
    public boolean deleteAppPatrolTaskById(Long id) {
        if (id == null) {
            return false;
        }
        
        // 逻辑删除：设置删除标志为1
        AppPatrolTask task = new AppPatrolTask();
        task.setId(id);
        task.setDelFlag("1");
        task.setUpdateTime(new Date());
        
        return this.updateById(task);
    }

    @Override
    public List<AppPatrolTask> selectByPlanId(Long planId) {
        if (planId == null) {
            return List.of();
        }
        
        LambdaQueryWrapper<AppPatrolTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppPatrolTask::getPlanId, planId)
                   .eq(AppPatrolTask::getDelFlag, "0")
                   .orderByAsc(AppPatrolTask::getSortOrder);
        
        return this.list(queryWrapper);
    }

    @Override
    public List<AppPatrolTask> selectByPointId(Long pointId) {
        if (pointId == null) {
            return List.of();
        }
        
        LambdaQueryWrapper<AppPatrolTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppPatrolTask::getPointId, pointId)
                   .eq(AppPatrolTask::getDelFlag, "0")
                   .orderByAsc(AppPatrolTask::getSortOrder);
        
        return this.list(queryWrapper);
    }

    @Override
    public List<AppPatrolTask> selectByPointType(Integer pointType) {
        if (pointType == null) {
            return List.of();
        }
        
        LambdaQueryWrapper<AppPatrolTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppPatrolTask::getPointType, pointType)
                   .eq(AppPatrolTask::getDelFlag, "0")
                   .orderByAsc(AppPatrolTask::getSortOrder);
        
        return this.list(queryWrapper);
    }
}
