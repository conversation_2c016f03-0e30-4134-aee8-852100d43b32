package com.pig4cloud.pigx.gis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.gis.entity.DangerPointMonitorPoint;
import com.pig4cloud.pigx.gis.mapper.DangerPointMonitorPointMapper;
import com.pig4cloud.pigx.gis.service.DangerPointMonitorPointService;
import org.springframework.stereotype.Service;
/**
 * 监测点关联隐患点
 *
 * <AUTHOR>
 * @date 2025-08-25 09:45:19
 */
@Service
public class DangerPointMonitorPointServiceImpl extends ServiceImpl<DangerPointMonitorPointMapper, DangerPointMonitorPoint> implements DangerPointMonitorPointService {
}