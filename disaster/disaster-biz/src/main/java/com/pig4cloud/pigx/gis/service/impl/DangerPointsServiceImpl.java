package com.pig4cloud.pigx.gis.service.impl;


import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.XWPFTemplate;
import com.pig4cloud.pigx.admin.mapper.SysDeptMapper;
import com.pig4cloud.pigx.common.security.util.SecurityUtils;
import com.pig4cloud.pigx.gis.domain.DangerPoints;
import com.pig4cloud.pigx.gis.domain.EchartsProperty;
import com.pig4cloud.pigx.gis.domain.HgAddresses;
import com.pig4cloud.pigx.gis.dto.DangerPointStaffExcelDTO;
import com.pig4cloud.pigx.gis.dto.DangerPointsGridMembersMismatchDTO;
import com.pig4cloud.pigx.gis.mapper.DangerPointsMapper;
import com.pig4cloud.pigx.gis.param.DangerPointsPictureA;
import com.pig4cloud.pigx.gis.param.DangerPointsPictureB;
import com.pig4cloud.pigx.gis.service.DangerPointsService;
import com.pig4cloud.pigx.gis.utils.UserDeptLevelUtils;
import com.pig4cloud.pigx.gis.vo.*;
import com.pig4cloud.pigx.gis.dto.UserDeptLevelDTO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.CellValue;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
public class DangerPointsServiceImpl implements DangerPointsService {


    @Resource
    private DangerPointsMapper dangerPointsMapper;

    @Resource
    private SysDeptMapper sysDeptMapper;


    @Override
    public void updateHgDangerPoints(DangerPoints dangerPoints) {
        dangerPointsMapper.updateHgDangerPoints(dangerPoints);
    }

    @Override
    public void insertHgDangerPoints(DangerPoints dangerPoints) {
        dangerPoints.setId(IdUtil.getSnowflakeNextIdStr());
        dangerPointsMapper.insertHgDangerPoints(dangerPoints);
    }

    @Override
    public int deleteHgDangerPointsByIds(String[] ids) {
        return dangerPointsMapper.deleteHgDangerPointsByIds(ids);
    }

    /**
     * 查询隐患点
     *
     * @param id 隐患点主键
     * @return 隐患点
     */

    @Override
    public DangerPoints selectHgDangerPointsById(String id) {
        return dangerPointsMapper.selectHgDangerPointsById(id);
    }

    @Override
    public List<String> selectGroupByStabilityStatus() {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        return dangerPointsMapper.selectGroupByStabilityStatus(levelDto.getQuId(), levelDto.getTownId(), levelDto.getVillageId());
    }

    @Override
    public List<String> selectGroupByDangerPointsType() {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        return dangerPointsMapper.selectGroupByDangerPointsType(levelDto.getQuId(), levelDto.getTownId(), levelDto.getVillageId());
    }

    @Override
    public List<EchartsProperty> selectStatisticsByDangerPointsType(DangerPointsReqVO dangerPoints) {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        // 乡镇用户与村用户查看的数据范围是一致的
        dangerPoints.setCountyId(levelDto.getQuId());
        dangerPoints.setTownId(levelDto.getTownId());
        dangerPoints.setVillageId(levelDto.getVillageId());
        dangerPoints.setStatisticsField("danger_points_type");
        return dangerPointsMapper.selectStatistics(dangerPoints);
    }

    @Override
    public List<EchartsProperty> getStatisticsByStabilityStatus(DangerPointsReqVO dangerPoints) {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        // 乡镇用户与村用户查看的数据范围是一致的
        dangerPoints.setCountyId(levelDto.getQuId());
        dangerPoints.setTownId(levelDto.getTownId());
        dangerPoints.setVillageId(levelDto.getVillageId());
        dangerPoints.setStatisticsField("stability_status");
        return dangerPointsMapper.selectStatistics(dangerPoints);
    }

    @Override
    public List<DangerPointsCountVO> getAreaDangerPointCount() {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        DangerPointsReqVO reqVO = new DangerPointsReqVO();
        if (levelDto.getLevel() == 0) {
            //市看区的
            return dangerPointsMapper.getCountyDangerPointCount(null);
        } else if (levelDto.getLevel() == 1) {
            //区看乡镇的
            reqVO.setCountyId(levelDto.getQuId());
            return dangerPointsMapper.getTownDangerPointCount(reqVO);
        } else if (levelDto.getLevel() == 2) {
            //乡镇看村子的
            return dangerPointsMapper.getVillageDangerPointCount(levelDto.getQuId(), levelDto.getTownId());
        } else if (levelDto.getLevel() == 3) {
            //村看自己的
            return dangerPointsMapper.getSingleVillageDangerPointCount(levelDto.getQuId(), levelDto.getTownId(), levelDto.getVillageId());
        }
        return null;
    }

    @Override
    public DangerPointsMapAdministrativeStatisticsVO getAdministrativeStatistics(DangerPointsReqVO reqAO) {
        Long deptId = SecurityUtils.getUser().getDeptId();
        // 所属政区ID为空，则返回空
        if (deptId == null) {
            return null;
        }
        List<DangerPointsCountVO> countyList = dangerPointsMapper.getCountyDangerPointCount(reqAO);
        List<DangerPointsCountVO> townList = dangerPointsMapper.getTownDangerPointCount(reqAO);
        DangerPointsMapAdministrativeStatisticsVO result = new DangerPointsMapAdministrativeStatisticsVO();
        result.setCountyList(countyList);
        result.setTownList(townList);
        return result;
    }


    @Override
    public List<TaskLocationVO> selectTaskLocation(String villageName) {
        return dangerPointsMapper.selectTaskLocation(villageName);
    }

    @Override
    public DangerPointsStatisticsVO selectDangerWarningNum() {
        DangerPointsStatisticsVO dangerPointsStatisticsVO = new DangerPointsStatisticsVO();
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }

        //计算隐患点总数
        Integer total = dangerPointsMapper.selectDangerPointsTotal(levelDto.getLevel(), levelDto.getQuId(), levelDto.getTownId(), levelDto.getVillageId());
        dangerPointsStatisticsVO.setDangerPointsTotal(total == null ? 0 : total);

        //求有预警的数量
        Integer num = dangerPointsMapper.selectHasDangerWarningNum();
        dangerPointsStatisticsVO.setHasWarningNum(num == null ? 0 : num);
        dangerPointsStatisticsVO.setNoWarningNum(dangerPointsStatisticsVO.getDangerPointsTotal() - dangerPointsStatisticsVO.getHasWarningNum());


        return dangerPointsStatisticsVO;
    }


    //----------------
    @Override
    public Page<DangerPoints> selectHgDangerPointsList(IPage<DangerPoints> pointPage, DangerPointsReqVO dangerPoints) {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        // 乡镇用户与村用户查看的数据范围是一致的
        dangerPoints.setCountyId(levelDto.getQuId());
        dangerPoints.setTownId(levelDto.getTownId());
        dangerPoints.setVillageId(levelDto.getVillageId());
        return dangerPointsMapper.selectHgDangerPointsList(pointPage, dangerPoints);
    }


    public List<HgAddresses> listAllAddresses() {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        List<HgAddresses> addresses = dangerPointsMapper.listAllAddresses(levelDto.getLevel(), levelDto.getQuId(), levelDto.getTownId(), levelDto.getVillageId());

        return null;
    }


    /**
     * 隐患点批量导入
     *
     * @param file
     * @return
     */
    @Override
    public Integer dangerPointImport(MultipartFile file) throws IOException {
        XSSFWorkbook wb = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = wb.getSheetAt(0);
        XSSFRow row;

        // 批量插入到隐患点表
        List<Map<String, Object>> list = new ArrayList<>();
        String dangerPointsNumber = null;
        for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
            // 获取每一行数据
            row = sheet.getRow(i);
            // 核对隐患点是否已导入，根据隐患点编号查询
            dangerPointsNumber = String.valueOf(row.getCell(5));
            if (dangerPointsMapper.checkDangerPoint(dangerPointsNumber) > 0) {
                // 已存在，则跳过
                continue;
            }

            Map<String, Object> map = new HashMap<>();
            // 主键id
            map.put("id", IdUtil.getSnowflakeNextIdStr());
            // 名称
            String name = String.valueOf(row.getCell(1));
            map.put("name", name);
            // 隐患点类型（1：不稳定斜坡，2：滑坡，3：崩塌，4：泥石流）
            String danger_points_type = String.valueOf(row.getCell(2));
            map.put("danger_points_type", danger_points_type);
            // 网格编号
            String grid_number = String.valueOf(row.getCell(3));
            map.put("grid_number", grid_number);
            // 数据库编号
            String data_base_number = String.valueOf(row.getCell(4));
            map.put("data_base_number", data_base_number);
            // 隐患点编号
            map.put("danger_points_number", dangerPointsNumber);
            // 省
            String province = String.valueOf(row.getCell(6));
            map.put("province", province);
            // 市
            String city = String.valueOf(row.getCell(7));
            map.put("city", city);
            // 区（县）
            String area = String.valueOf(row.getCell(8));
            map.put("area", area);
            // 乡
            String country = String.valueOf(row.getCell(9));
            map.put("country", country);
            // 村
            String village = String.valueOf(row.getCell(10));
            map.put("village", village);
            // 组（小地方）
            String small_place = String.valueOf(row.getCell(11));
            map.put("small_place", small_place);
            // x
            map.put("x", getIntegerString(row.getCell(12)));
            // y
            map.put("y", getIntegerString(row.getCell(13)));
            // 经度
            map.put("lon", getDoubleValue(row.getCell(14)));
            // 纬度
            map.put("lat", getDoubleValue(row.getCell(15)));
            // 长
            map.put("long", getDoubleValue(row.getCell(16)));
            // 宽
            map.put("wide", getDoubleValue(row.getCell(17)));
            // 高
            map.put("height", getDoubleValue(row.getCell(18)));
            // 面积
            map.put("measure", getDoubleValue(row.getCell(19)));
            // 体积
            map.put("volume", getDoubleValue(row.getCell(20)));
            // 规模（1：小型，2：中型）
            map.put("scale", String.valueOf(row.getCell(21)));
            // 管理层级（1：县级，2：村级，3：乡级）
            map.put("management_level", String.valueOf(row.getCell(22)));
            // 威胁人口
            Double populationNumber = getDoubleValue(row.getCell(23));
            map.put("threatening_population", populationNumber == null ? null : populationNumber.intValue());
            // 威胁财产
            map.put("threatening_property", getDoubleValue(row.getCell(24)));
            // 险情等级（1：小型，2：中型，3：大型）
            map.put("danger_level", String.valueOf(row.getCell(25)));
            // 次生灾害时间
            map.put("secondary_disaster_time", String.valueOf(row.getCell(26)));
            // 地质环境条件
            map.put("address_environmental_conditions", String.valueOf(row.getCell(27)));
            // 变形特征及活动历时
            map.put("deformation_characteristics_activity_duration", String.valueOf(row.getCell(28)));
            // 稳定性分析
            map.put("stability_analysis", String.valueOf(row.getCell(29)));
            // 稳定性现状（1：基本稳定，2：稳定，3：不稳定）
            map.put("stability_status", String.valueOf(row.getCell(30)));
            // 稳定性趋势（1：基本稳定，2：稳定，3：不稳定）
            map.put("stability_trend", String.valueOf(row.getCell(31)));
            // 引发因素
            map.put("causing_factors", String.valueOf(row.getCell(32)));
            // 潜在危害
            map.put("potential_hazards", String.valueOf(row.getCell(33)));
            // 临灾状态预测
            map.put("prediction_impending_disaster_state", String.valueOf(row.getCell(34)));
            // 监测方法
            map.put("monitoring_methods", String.valueOf(row.getCell(35)));
            // 监测人
            map.put("monitor", String.valueOf(row.getCell(36)));
            // 监测人职务(1：村干部，2：其它）
            map.put("position_supervisor", String.valueOf(row.getCell(37)));
            // 监测人电话
            map.put("monitor_telephone", getPhoneString(row.getCell(38)));
            // 警示牌文字内容
            map.put("text_warning_sign", String.valueOf(row.getCell(39)));
            // 责任人
            map.put("person_liable", String.valueOf(row.getCell(40)));
            // 责任人电话
            map.put("person_liable_telephone", getPhoneString(row.getCell(41)));
            // 管理员
            map.put("administrators", String.valueOf(row.getCell(42)));
            // 管理员电话
            map.put("administrators_telephone", getPhoneString(row.getCell(43)));
            // 协管员
            map.put("traffic_assistant", String.valueOf(row.getCell(44)));
            // 协管员电话
            map.put("traffic_assistant_telephone", getPhoneString(row.getCell(45)));
            // 专管员
            map.put("special_manager", String.valueOf(row.getCell(46)));
            // 专管员电话
            map.put("special_manager_telephone", getPhoneString(row.getCell(47)));

            list.add(map);
        }

        // 关闭流
        wb.close();

        // 插入
        if (!list.isEmpty()) {
            return dangerPointsMapper.saveDangerPoint(list);
        }

        return 0;
    }


    private static String getPhoneString(Cell cell) {
        String str;
        if (cell != null) {
            switch (cell.getCellType()) {
                case NUMERIC:
                    // 处理数值型单元格（科学计数法）
                    double numericValue = cell.getNumericCellValue();
                    // 去除小数点，转换为普通字符串格式
                    str = new BigDecimal(numericValue).setScale(0, RoundingMode.HALF_UP).toPlainString();
                    break;
                case STRING:
                    // 直接读取文本型内容
                    str = cell.getStringCellValue().trim();
                    break;
                default:
                    str = "";
            }
        } else {
            str = "";
        }
        // 移除可能的空格和特殊符号（如Excel自动添加的逗号）
        return str.replaceAll("[^0-9]", "");
    }


    private String getIntegerString(Cell cell) {
        String str;
        if (cell != null && cell.getCellType() == CellType.NUMERIC) {
            // 使用 DecimalFormat 去除小数点
            DecimalFormat df = new DecimalFormat("0");
            str = df.format(cell.getNumericCellValue());
        } else {
            // 处理非数值型单元格
            str = cell != null ? cell.toString() : "";
        }
        return str;
    }

    private Double getDoubleValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        switch (cell.getCellType()) {
            case NUMERIC:
                return cell.getNumericCellValue();
            case BLANK:
                return null;
            case FORMULA:
                FormulaEvaluator evaluator = cell.getSheet().getWorkbook()
                        .getCreationHelper().createFormulaEvaluator();
                CellValue formulaValue = evaluator.evaluate(cell);
                return (formulaValue.getCellType() == CellType.NUMERIC) ?
                        formulaValue.getNumberValue() : null;
            default:
                // 其他类型按需处理（如日志警告）
                return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<DangerPointsGridMembersMismatchDTO> batchUpdateGridMembers(List<DangerPointStaffExcelDTO> list, String area) {


        ArrayList<DangerPointsGridMembersMismatchDTO> mismatchList = new ArrayList<>();
        List<DangerPointsGridMembersMismatchDTO> waitMatchList = dangerPointsMapper.selectMismatchByArea(area);
        // 将List<DangerPointStaffExcelDTO> list中的数据country和village字段与mismatchList中的数据country和village字段进行匹配,如果都不相同,则将不匹配的waitMatchList的数据存入一个mismatchList集合,并去重
        for (DangerPointsGridMembersMismatchDTO mismatchData : waitMatchList) {
            for (DangerPointStaffExcelDTO excelData : list) {
                if (mismatchData.getCountry().equals(excelData.getCountry()) &&
                        mismatchData.getVillage().equals(excelData.getVillage())) {
                    mismatchList.add(mismatchData);
                    break;
                }
            }
        }
        //去除waitMatchList与mismatchList重复数据
        waitMatchList.removeAll(mismatchList);


        for (DangerPointStaffExcelDTO excelData : list) {
            // 过滤掉乡镇或村名为空的行
            if (StringUtils.isBlank(excelData.getCountry()) ||
                    StringUtils.isBlank(excelData.getVillage())) {
                continue;
            }

            // 处理电话号码中的空格和横线
            excelData.setPersonLiableTelephone(cleanPhoneNumber(excelData.getPersonLiableTelephone()));
            excelData.setAdministratorsTelephone(cleanPhoneNumber(excelData.getAdministratorsTelephone()));
            excelData.setTrafficAssistantTelephone(cleanPhoneNumber(excelData.getTrafficAssistantTelephone()));
            excelData.setSpecialManagerTelephone(cleanPhoneNumber(excelData.getSpecialManagerTelephone()));
            //去除乡镇和村名中的空格
            excelData.setCountry(excelData.getCountry().trim());
            excelData.setVillage(excelData.getVillage().trim());

            dangerPointsMapper.updateByCountryAndVillage(
                    excelData.getCountry(),
                    excelData.getVillage(),
                    excelData.getPersonLiable(),
                    excelData.getPersonLiableTelephone(),
                    excelData.getAdministrators(),
                    excelData.getAdministratorsTelephone(),
                    excelData.getTrafficAssistant(),
                    excelData.getTrafficAssistantTelephone(),
                    excelData.getSpecialManager(),
                    excelData.getSpecialManagerTelephone()
            );
        }
        return mismatchList;
    }

    @Override
    @Transactional
    public void changeVerificationFlag(String[] ids, Boolean verificationFlag) {
        if (ids == null) {
            return;
        }
        for (String id : ids) {
            DangerPoints idDangerPoints = new DangerPoints();
            idDangerPoints.setId(id);
            idDangerPoints.setVerificationFlag(verificationFlag);
            dangerPointsMapper.updateHgDangerPoints(idDangerPoints);
        }

    }

    @Override
    public DangerPointsFileVO getDangerPointsFile(String id) {
        DangerPointsFileVO dangerPointsFile = dangerPointsMapper.getDangerPointsFile(id);
        if (dangerPointsFile == null) {
            return null;
        }

        if (dangerPointsFile.getPictureLocale() != null) {
            dangerPointsFile.setPictureLocaleUrl(String.format("/admin/sys-file/oss/file?fileName=%s", dangerPointsFile.getPictureLocale()));
        }
        if (dangerPointsFile.getPictureDisasterPlan() != null) {
            dangerPointsFile.setPictureDisasterPlanUrl(String.format("/admin/sys-file/oss/file?fileName=%s", dangerPointsFile.getPictureDisasterPlan()));
        }
        if (dangerPointsFile.getPictureFlat() != null) {
            dangerPointsFile.setPictureFlatUrl(String.format("/admin/sys-file/oss/file?fileName=%s", dangerPointsFile.getPictureFlat()));
        }
        if (dangerPointsFile.getPictureSection() != null) {
            dangerPointsFile.setPictureSectionUrl(String.format("/admin/sys-file/oss/file?fileName=%s", dangerPointsFile.getPictureSection()));
        }
        return dangerPointsFile;
    }

    @Override
    public void updateDangerPointsPictureA(DangerPointsPictureA dangerPointsPictureA) {
        dangerPointsMapper.updateDangerPointsPictureA(dangerPointsPictureA);
    }

    @Override
    public void updateDangerPointsPictureB(DangerPointsPictureB dangerPointsPictureB) {
        dangerPointsMapper.updateDangerPointsPictureB(dangerPointsPictureB);
    }

    @Override
    public DangerPointsQrCodeVO getInfo(String id) {
        DangerPointsQrCodeVO dangerPointsQrCodeVO = new DangerPointsQrCodeVO();
        dangerPointsQrCodeVO.setDangerPoints(dangerPointsMapper.selectHgDangerPointsById(id));
        dangerPointsQrCodeVO.setDangerPointsFileVO(getDangerPointsFile(id));
        return dangerPointsQrCodeVO;
    }

    @Override
    public void exportWorkFile(String id, HttpServletResponse response) {
        // 1. 查询数据库数据
        DangerPoints dangerPoints = selectHgDangerPointsById(id);
        if (dangerPoints == null) {
            throw new IllegalArgumentException("未找到对应的危险点信息，id=" + id);
        }

        //选择模板1
        String wordModelName = "workModel.docx";

        //导出数据
        exportToWord(response, dangerPoints, wordModelName);
    }

    @Override
    public void exportAvoidRiskFile(String id, HttpServletResponse response) {
        // 1. 查询数据库数据
        DangerPoints dangerPoints = selectHgDangerPointsById(id);
        if (dangerPoints == null) {
            throw new IllegalArgumentException("未找到对应的危险点信息，id=" + id);
        }

        //选择模板1
        String wordModelName = "dangerPointsAvoidRiskModel.docx";

        //导出数据
        exportToWord(response, dangerPoints, wordModelName);

    }

    private static void exportToWord(HttpServletResponse response, DangerPoints dangerPoints, String wordModelName) {
        // 2. 文件名更直观（时间戳 + 名称）
        String fileName = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
                + "-" + dangerPoints.getName() + ".docx";

        try {
            // 3. 加载模板
            String templatePath = "file/" + wordModelName;    // 使用正斜杠
            ClassPathResource resource = new ClassPathResource(templatePath);

            try (InputStream in = resource.getInputStream();
                 XWPFTemplate template = XWPFTemplate.compile(in).render(dangerPoints);
                 ByteArrayOutputStream bos = new ByteArrayOutputStream()) {

                // 渲染到内存流，避免写一半失败
                template.write(bos);

                // 4. 设置响应头
                response.setCharacterEncoding("UTF-8");
                response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
                response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
                response.setHeader("Pragma", "no-cache");
                response.setDateHeader("Expires", 0);

                // 解决文件名中文乱码（兼容各种浏览器）
                String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-Disposition", "attachment;filename*=UTF-8''" + encodedFileName);

                // 写入响应流
                try (OutputStream out = response.getOutputStream()) {
                    bos.writeTo(out);
                    out.flush();
                }
            }
        } catch (IOException e) {
            throw new RuntimeException("导出 Word IO 异常", e);
        } catch (Exception e) {
            throw new RuntimeException("导出 Word 失败", e);
        }
    }


    private String cleanPhoneNumber(String phone) {
        if (phone == null) return null;
        // 移除电话号码中的空格、横线等字符
        return phone.replaceAll("[\\s-]+", "");
    }


}
