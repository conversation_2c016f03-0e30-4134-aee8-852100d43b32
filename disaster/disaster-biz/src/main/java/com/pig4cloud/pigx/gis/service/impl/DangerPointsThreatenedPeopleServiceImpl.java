package com.pig4cloud.pigx.gis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.common.security.service.PigxUser;
import com.pig4cloud.pigx.common.security.util.SecurityUtils;
import com.pig4cloud.pigx.gis.domain.DangerPointsThreatenedPeople;
import com.pig4cloud.pigx.gis.mapper.DangerPointsThreatenedPeopleMapper;
import com.pig4cloud.pigx.gis.service.DangerPointsThreatenedPeopleService;
import com.pig4cloud.pigx.gis.vo.DangerPointsThreatenedPeopleVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Service
public class DangerPointsThreatenedPeopleServiceImpl extends ServiceImpl<DangerPointsThreatenedPeopleMapper, DangerPointsThreatenedPeople> implements DangerPointsThreatenedPeopleService {

	@Resource
	private DangerPointsThreatenedPeopleMapper dangerPointsThreatenedPeopleMapper;


	@Override
	public DangerPointsThreatenedPeopleVO getList(Page<DangerPointsThreatenedPeople> page, String dangerPointsId) {
		List<DangerPointsThreatenedPeople> list = dangerPointsThreatenedPeopleMapper.getList(page,dangerPointsId).getRecords();
		DangerPointsThreatenedPeopleVO threatenedPeopleVO = new DangerPointsThreatenedPeopleVO();
		threatenedPeopleVO.setPointsThreatenedPeopleList(list);
		QueryWrapper<DangerPointsThreatenedPeople> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("danger_points_id",dangerPointsId);
		threatenedPeopleVO.setTotalSize(baseMapper.selectCount(queryWrapper));
		return threatenedPeopleVO;
	}

	@Override
	public void add(DangerPointsThreatenedPeople dangerPointsThreatenedPeople) {
		PigxUser loginUser = SecurityUtils.getUser();
		dangerPointsThreatenedPeople.setCreateUser(loginUser.getId());
		dangerPointsThreatenedPeople.setCreateTime(new Date());
		baseMapper.insert(dangerPointsThreatenedPeople);
	}

	@Override
	public void edit(DangerPointsThreatenedPeople dangerPointsThreatenedPeople) {
		PigxUser loginUser = SecurityUtils.getUser();
		dangerPointsThreatenedPeople.setUpdateUser(loginUser.getId());
		dangerPointsThreatenedPeople.setUpdateTime(new Date());
		baseMapper.updateById(dangerPointsThreatenedPeople);
	}
}
