package com.pig4cloud.pigx.gis.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.common.file.core.FileProperties;
import com.pig4cloud.pigx.gis.entity.DataSource3dEntity;
import com.pig4cloud.pigx.gis.mapper.DataSource3dMapper;
import com.pig4cloud.pigx.gis.service.DataSource3dService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.time.LocalDate;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 三维模型数据-区域模型
 *
 * <AUTHOR>
 * @date 2025-09-02 13:35:41
 */
@Service
public class DataSource3dServiceImpl extends ServiceImpl<DataSource3dMapper, DataSource3dEntity> implements DataSource3dService {
    @Value("${server.url.prefix}")
    private String urlPrefix;

    @Resource
    private DataSource3dMapper dataSource3dMapper;

    @Resource
    private FileProperties properties;


    @Override
    public String saveUploadTask(MultipartFile file, String name, LocalDate date) {

        DataSource3dEntity dataSource3dEntity = new DataSource3dEntity();
        dataSource3dEntity.setName(name);
        dataSource3dEntity.setFileName(file.getOriginalFilename());
        dataSource3dEntity.setTimeTrem(date);
        dataSource3dEntity.setTotalSize(file.getSize());
        dataSource3dEntity.setStatus("1");
        dataSource3dMapper.insert(dataSource3dEntity);
        return dataSource3dEntity.getId();
    }


    @Async
    @Override
    public void processAsync(String sourceId, MultipartFile multipartFile) {
        try {
            String basePath = properties.getLocal().getBasePath();
            // 1. 保存 zip 文件
            String zipFileName = multipartFile.getOriginalFilename();
            String uploadDir = basePath + File.separator + "3dFiles" + File.separator + IdUtil.simpleUUID() + "_" + zipFileName.substring(0, zipFileName.lastIndexOf("."));
            File zipFile = new File(uploadDir, zipFileName);
            // 确保父目录存在
            File parentDir = zipFile.getParentFile();
            if (!parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                if (!created) {
                    throw new IOException("目录创建失败: " + parentDir.getAbsolutePath());
                }
            }

            try (InputStream in = multipartFile.getInputStream();
                 FileOutputStream out = new FileOutputStream(zipFile)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }

            // 2. 解压
            File unzipDir = new File(uploadDir);
            if (!unzipDir.exists()) unzipDir.mkdirs();
            unzip(zipFile, unzipDir);

            // 3. 找 tileset.json
            File tilesetFile = findTilesetJson(unzipDir);
            if (tilesetFile == null) {
                dataSource3dMapper.updateById(new DataSource3dEntity(sourceId, "3"));

                return;
            }

            // 4. 更新任务信息
            String relativePath = tilesetFile.getAbsolutePath()
                    .replace("\\", "/")
                    .replace(basePath, "");
            String url = urlPrefix + relativePath;

            dataSource3dMapper.updateById(new DataSource3dEntity(sourceId, uploadDir, url, "2"));

        } catch (Exception e) {
            e.printStackTrace();
            dataSource3dMapper.updateById(new DataSource3dEntity(sourceId, "3"));
        }
    }

    @Override
    public DataSource3dEntity selectById(String sourceId) {
        return dataSource3dMapper.selectById(sourceId);
    }


    private File findTilesetJson(File dir) {
        File[] files = dir.listFiles();
        if (files == null) return null;
        for (File file : files) {
            if (file.isDirectory()) {
                File result = findTilesetJson(file);
                if (result != null) return result;
            } else if ("tileset.json".equalsIgnoreCase(file.getName())) {
                return file;
            }
        }
        return null;
    }


    private void unzip(File zipFile, File destDir) throws IOException {
        byte[] buffer = new byte[1024];
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                File newFile = new File(destDir, entry.getName());
                if (entry.isDirectory()) {
                    newFile.mkdirs();
                } else {
                    newFile.getParentFile().mkdirs();
                    try (FileOutputStream fos = new FileOutputStream(newFile)) {
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
            }
        }
    }
}