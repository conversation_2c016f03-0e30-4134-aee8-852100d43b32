package com.pig4cloud.pigx.gis.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.common.file.core.FileProperties;
import com.pig4cloud.pigx.gis.entity.DataSource3dEntity;
import com.pig4cloud.pigx.gis.entity.DataSourceDisaster3dEntity;
import com.pig4cloud.pigx.gis.mapper.DataSourceDisaster3dMapper;
import com.pig4cloud.pigx.gis.service.DataSourceDisaster3dService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.time.LocalDate;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 三维模型数据-隐患点，风险区模型
 *
 * <AUTHOR>
 * @date 2025-09-03 10:35:47
 */
@Service
public class DataSourceDisaster3dServiceImpl extends ServiceImpl<DataSourceDisaster3dMapper, DataSourceDisaster3dEntity> implements DataSourceDisaster3dService {

    @Value("${server.url.prefix}")
    private String urlPrefix;


    @Resource
    private FileProperties properties;

    @Resource
    private DataSourceDisaster3dMapper dataSourceDisaster3dMapper;


    @Override
    public String saveUploadTask(MultipartFile file, String monitorDataType, String provincialCode, String name, LocalDate date) {

        DataSourceDisaster3dEntity dataSourceDisaster3dEntity = new DataSourceDisaster3dEntity();
        dataSourceDisaster3dEntity.setMonitorDataType(monitorDataType);
        dataSourceDisaster3dEntity.setProvincialCode(provincialCode);
        dataSourceDisaster3dEntity.setName(name);
        dataSourceDisaster3dEntity.setTimeTrem(date);
        dataSourceDisaster3dEntity.setFileName(file.getOriginalFilename());
        dataSourceDisaster3dEntity.setStatus("1");
        dataSourceDisaster3dEntity.setTotalSize(file.getSize());
        dataSourceDisaster3dMapper.insert(dataSourceDisaster3dEntity);
        return dataSourceDisaster3dEntity.getId();
    }


    @Async
    @Override
    public void processDataSourceDisaster3dAsync(String sourceId, MultipartFile multipartFile) {
        try {
            String basePath = properties.getLocal().getBasePath();
            // 1. 保存 zip 文件
            String zipFileName = multipartFile.getOriginalFilename();
            String uploadDir = basePath + File.separator + "3dFiles" + File.separator + IdUtil.simpleUUID() + "_" + zipFileName.substring(0, zipFileName.lastIndexOf("."));
            File zipFile = new File(uploadDir, zipFileName);
            // 确保父目录存在
            File parentDir = zipFile.getParentFile();
            if (!parentDir.exists()) {
                boolean created = parentDir.mkdirs();
                if (!created) {
                    throw new IOException("目录创建失败: " + parentDir.getAbsolutePath());
                }
            }

            try (InputStream in = multipartFile.getInputStream();
                 FileOutputStream out = new FileOutputStream(zipFile)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }

            // 2. 解压
            File unzipDir = new File(uploadDir);
            if (!unzipDir.exists()) unzipDir.mkdirs();
            unzip(zipFile, unzipDir);

            // 3. 找 tileset.json
            File tilesetFile = findTilesetJson(unzipDir);
            if (tilesetFile == null) {
                dataSourceDisaster3dMapper.updateById(new DataSourceDisaster3dEntity(sourceId, "3"));
                return;
            }

            // 4. 更新任务信息
            String relativePath = tilesetFile.getAbsolutePath()
                    .replace("\\", "/")
                    .replace(basePath, "");
            String url = urlPrefix + relativePath;

            dataSourceDisaster3dMapper.updateById(new DataSourceDisaster3dEntity(sourceId, uploadDir, url, "2"));

        } catch (Exception e) {
            e.printStackTrace();
            dataSourceDisaster3dMapper.updateById(new DataSourceDisaster3dEntity(sourceId, "3"));
        }
    }

    @Override
    public DataSourceDisaster3dEntity selectById(String sourceId) {
        return dataSourceDisaster3dMapper.selectById(sourceId);
    }

    @Override
    public List<DataSourceDisaster3dEntity> getDataSourceDisaster3dPage(Page<DataSourceDisaster3dEntity> page, String name) {
        return dataSourceDisaster3dMapper.getDataSourceDisaster3dPage(page,name);
    }

    @Override
    public List<DataSourceDisaster3dEntity> getDataSourceDisaster3dPageVersion(Page<DataSourceDisaster3dEntity> page, String monitorDataType, String provincialCode) {
        return dataSourceDisaster3dMapper.getDataSourceDisaster3dPageVersion(page,monitorDataType,provincialCode);
    }


    private File findTilesetJson(File dir) {
        File[] files = dir.listFiles();
        if (files == null) return null;
        for (File file : files) {
            if (file.isDirectory()) {
                File result = findTilesetJson(file);
                if (result != null) return result;
            } else if ("tileset.json".equalsIgnoreCase(file.getName())) {
                return file;
            }
        }
        return null;
    }


    private void unzip(File zipFile, File destDir) throws IOException {
        byte[] buffer = new byte[1024];
        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFile))) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                File newFile = new File(destDir, entry.getName());
                if (entry.isDirectory()) {
                    newFile.mkdirs();
                } else {
                    newFile.getParentFile().mkdirs();
                    try (FileOutputStream fos = new FileOutputStream(newFile)) {
                        int len;
                        while ((len = zis.read(buffer)) > 0) {
                            fos.write(buffer, 0, len);
                        }
                    }
                }
            }
        }
    }
}