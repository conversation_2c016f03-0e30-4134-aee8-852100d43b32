package com.pig4cloud.pigx.gis.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.admin.api.entity.SysDictItem;
import com.pig4cloud.pigx.admin.api.feign.RemoteDictService;
import com.pig4cloud.pigx.common.core.util.R;
import com.pig4cloud.pigx.gis.domain.Devices;
import com.pig4cloud.pigx.gis.domain.EchartsProperty;
import com.pig4cloud.pigx.gis.dto.UserDeptLevelDTO;
import com.pig4cloud.pigx.gis.entity.Device;
import com.pig4cloud.pigx.gis.mapper.DeviceMapper;
import com.pig4cloud.pigx.gis.service.DeviceService;
import com.pig4cloud.pigx.gis.utils.UserDeptLevelUtils;
import com.pig4cloud.pigx.gis.vo.DeviceNumberCountVO;
import com.pig4cloud.pigx.gis.vo.DeviceVO;
import com.pig4cloud.pigx.gis.vo.DevicesAllGroupVO;
import com.pig4cloud.pigx.gis.vo.DevicesVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 监测设备
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements DeviceService {

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private RemoteDictService remoteDictService;


    /**
     * 新增监测设备
     *
     * @param devices 监测设备
     * @return 结果
     */
    @Override
    public int insertHgDevices(Devices devices) {
        return deviceMapper.insertHgDevices(devices);
    }


    /**
     * 修改监测设备
     *
     * @param devices 监测设备
     * @return 结果
     */
    @Override
    public int updateHgDevices(Devices devices) {
        return deviceMapper.updateHgDevices(devices);
    }

    /**
     * 批量删除监测设备
     *
     * @param ids 需要删除的监测设备主键
     * @return 结果
     */
    @Override
    public int deleteHgDevicesByIds(Integer[] ids) {
        return deviceMapper.deleteHgDevicesByIds(ids);
    }


    /**
     * 查询监测设备
     *
     * @param id 监测设备主键
     * @return 监测设备
     */
    @Override
    public DeviceVO selectHgDevicesById(Long id) {

        return deviceMapper.selectHgDevicesById(id);
    }

    @Override
    public List<String> selectGroupByStationType() {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        // 乡镇用户与村用户查看的数据范围
        List<String> strings = deviceMapper.selectGroupByStationType(levelDto.getLevel(), levelDto.getQuId(), levelDto.getTownId(), levelDto.getVillageId());
        List<String> deviceTypeList = new ArrayList<>();
        if (!strings.isEmpty()) {
            // 将字典值转为对应的中文名称
            R<List<SysDictItem>> res = remoteDictService.getDictByType("sensor_type");
            if (res.isOk() && res.getData() != null && !res.getData().isEmpty()) {
                Map<String, String> dictMap = res.getData().stream().collect(Collectors.toMap(SysDictItem::getItemValue, SysDictItem::getLabel));
                // 将字典值转为对应的中文名称
                deviceTypeList = strings.stream().map(item -> dictMap.containsKey(item) ? dictMap.get(item) : item).collect(Collectors.toList());
            }
        }
        return deviceTypeList;
    }




    @Override
    public DevicesAllGroupVO allGroup() {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        DevicesAllGroupVO groupVO = new DevicesAllGroupVO();
        //  隐患点或风险区的设备数量
        groupVO.setDangerOrRiskList(deviceMapper.selectGroupByDangerOrRisk(levelDto.getLevel(), levelDto.getQuId(), levelDto.getTownId(), levelDto.getVillageId()));
        groupVO.setDeviceCompanyList(deviceMapper.selectGroupByCompany(levelDto.getLevel(), levelDto.getQuId(), levelDto.getTownId(), levelDto.getVillageId()));
        groupVO.setDeviceTotal(deviceMapper.selectDeviceTotal(levelDto.getLevel(), levelDto.getQuId(), levelDto.getTownId(), levelDto.getVillageId()));
        groupVO.setOnlineDeviceTotal(deviceMapper.selectOnlineDeviceTotal(levelDto.getLevel(), levelDto.getQuId(), levelDto.getTownId(), levelDto.getVillageId()));
        return groupVO;
    }

    @Override
    public List<EchartsProperty> getSensorTypeCount(String monitorType) {
        List<EchartsProperty> list = deviceMapper.getDeviceTypeCount(monitorType);
        if (!list.isEmpty()) {
            // 将字典值转为对应的中文名称
            R<List<SysDictItem>> res = remoteDictService.getDictByType("sensor_type");
            if (res.isOk() && res.getData() != null && !res.getData().isEmpty()) {
                Map<String, String> dictMap = res.getData().stream().collect(Collectors.toMap(SysDictItem::getItemValue, SysDictItem::getLabel));
                list.forEach(item -> item.setName(dictMap.containsKey(item.getName()) ? dictMap.get(item.getName()) : item.getName()));
            }
        }
        return list;
    }

    @Override
    public DeviceNumberCountVO deviceNumberCount() {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        DeviceNumberCountVO deviceNumberCountVO = new DeviceNumberCountVO();
        deviceNumberCountVO.setDeviceTotal(deviceMapper.selectDeviceTotal(levelDto.getLevel(), levelDto.getQuId(), levelDto.getTownId(), levelDto.getVillageId()));
        deviceNumberCountVO.setOnlineDeviceTotal(deviceMapper.selectOnlineDeviceTotal(levelDto.getLevel(), levelDto.getQuId(), levelDto.getTownId(), levelDto.getVillageId()));
        //视频数 暂无数据--100内随机数
        deviceNumberCountVO.setVideoTotal(new Random().nextInt(100));
        return deviceNumberCountVO;
    }

    @Override
    public List<EchartsProperty> selectStatisticsByWarningLevelType(Integer monitorType) {
        ArrayList<EchartsProperty> echartsProperties = new ArrayList<>();
        EchartsProperty one = new EchartsProperty(monitorType != null ? monitorType == 1 ? 10 : 1 : 21, "红色");
        EchartsProperty two = new EchartsProperty(monitorType != null ? monitorType == 1 ? 12 : 1 : 13, "橙色");
        EchartsProperty three = new EchartsProperty(monitorType != null ? monitorType == 1 ? 0 : 1 : 1, "黄色");
        EchartsProperty four = new EchartsProperty(monitorType != null ? monitorType == 1 ? 34 : 1 : 35, "蓝色");
        echartsProperties.add(one);
        echartsProperties.add(two);
        echartsProperties.add(three);
        echartsProperties.add(four);
        return echartsProperties;
    }

    /**
     * 查询监测设备列表
     *
     * @param devices 监测设备
     * @return 监测设备
     */
    @Override
    public Page<DevicesVO> selectHgDevicesList(Page<Devices> devicePage, Devices devices) {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        // 乡镇用户与村用户查看的数据范围
        devices.setQuId(levelDto.getQuId());
        devices.setTownId(levelDto.getTownId());
        devices.setVillageId(levelDto.getVillageId());
        devices.setLevel(levelDto.getLevel());
        return deviceMapper.selectHgDevicesList(devicePage, devices);
    }

    @Override
    public Page<DeviceVO> selectDeviceList(Page<Device> devicePage, Device device) {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        // 乡镇用户与村用户查看的数据范围
        device.setQuId(levelDto.getQuId());
        device.setTownId(levelDto.getTownId());
        device.setVillageId(levelDto.getVillageId());
        device.setLevel(levelDto.getLevel());
        Page<DeviceVO> pageResult = deviceMapper.selectHgDeviceList(devicePage, device);
        if (pageResult != null && pageResult.getSize() > 0) {
            // 将字典值转为对应的中文名称
            R<List<SysDictItem>> res = remoteDictService.getDictByType("sensor_type");
            if (res.isOk() && res.getData() != null && !res.getData().isEmpty()) {
                Map<String, String> dictMap = res.getData().stream().collect(Collectors.toMap(SysDictItem::getItemValue, SysDictItem::getLabel));
                // 将字典值转为对应的中文名称
                pageResult.getRecords().forEach((x) -> {
                    x.setSensorTypeName(dictMap.containsKey(x.getSensorType()) ? dictMap.get(x.getSensorType()) : x.getSensorType());
                });
            }
        }
        return pageResult;
    }
}