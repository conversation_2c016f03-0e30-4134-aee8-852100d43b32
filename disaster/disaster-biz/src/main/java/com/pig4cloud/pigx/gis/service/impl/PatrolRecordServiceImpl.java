package com.pig4cloud.pigx.gis.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.common.security.service.PigxUser;
import com.pig4cloud.pigx.common.security.util.SecurityUtils;
import com.pig4cloud.pigx.gis.domain.PatrolRecord;
import com.pig4cloud.pigx.gis.mapper.PatrolRecordMapper;
import com.pig4cloud.pigx.gis.service.PatrolRecordService;
import com.pig4cloud.pigx.gis.vo.PatrolRecordVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 巡查记录服务实现类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Service
public class PatrolRecordServiceImpl extends ServiceImpl<PatrolRecordMapper, PatrolRecord> implements PatrolRecordService {

	@Resource
	private PatrolRecordMapper patrolRecordMapper;

	@Override
	public Page<PatrolRecord> selectPatrolRecordPage(Page<PatrolRecord> page, PatrolRecordVO patrolRecordVO) {
		return patrolRecordMapper.selectPatrolRecordPage(page, patrolRecordVO);
	}

	@Override
	public List<PatrolRecord> selectPatrolRecordList(PatrolRecordVO patrolRecordVO) {
		return patrolRecordMapper.selectPatrolRecordList(patrolRecordVO);
	}

	@Override
	public void insertPatrolRecord(PatrolRecord patrolRecord) {
		// 设置创建人和创建时间
		PigxUser loginUser = SecurityUtils.getUser();
		if (loginUser != null) {
			patrolRecord.setCreateUser(loginUser.getId());
		}
		patrolRecord.setCreateTime(new Date());
		patrolRecord.setUpdateTime(new Date());

		// 保存巡查记录
		patrolRecordMapper.insert(patrolRecord);
	}

	@Override
	public void updatePatrolRecord(PatrolRecord patrolRecord) {
		// 设置修改人和修改时间
		PigxUser loginUser = SecurityUtils.getUser();
		if (loginUser != null) {
			patrolRecord.setUpdateUser(loginUser.getId());
		}
		patrolRecord.setUpdateTime(new Date());

		// 更新巡查记录
		patrolRecordMapper.updateById(patrolRecord);
	}

	@Override
	public List<PatrolRecord> selectByPatrolTaskId(Long patrolTaskId) {
		return patrolRecordMapper.selectByPatrolTaskId(patrolTaskId);
	}

}
