package com.pig4cloud.pigx.gis.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.Pictures;
import com.pig4cloud.pigx.admin.api.entity.SysFile;
import com.pig4cloud.pigx.common.file.core.FileProperties;
import com.pig4cloud.pigx.gis.dto.UserDeptLevelDTO;
import com.pig4cloud.pigx.gis.domain.EchartsProperty;
import com.pig4cloud.pigx.gis.domain.RiskZone;
import com.pig4cloud.pigx.gis.mapper.RiskZoneMapper;
import com.pig4cloud.pigx.gis.param.RiskZonePictureParam;
import com.pig4cloud.pigx.gis.service.RiskZoneService;
import com.pig4cloud.pigx.gis.utils.UserDeptLevelUtils;
import com.pig4cloud.pigx.gis.vo.*;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.PropertyAccessorFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class RiskZoneServiceImpl extends ServiceImpl<RiskZoneMapper, RiskZone> implements RiskZoneService {


    @Resource
    private RiskZoneMapper riskZoneMapper;

    @Resource
    private FileProperties properties;


    @Override
    public int insert(RiskZone riskZone) {

        riskZone.setGeom(null);
        return riskZoneMapper.insert(riskZone);
    }

    @Override
    public int update(RiskZone riskZone) {

        riskZone.setGeom(null);
        return riskZoneMapper.updateById(riskZone);
    }

    @Override
    public int deleteByIds(Integer[] ids) {
        return riskZoneMapper.deleteBatchIds(Arrays.asList(ids));
    }

    @Override
    public RiskZone selectById(Integer id) {
        return riskZoneMapper.selectById(id);
    }


    @Override
    public List<String> selectGroupByQx() {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        //村用户和乡镇用户啥也看不到
        if (levelDto.getLevel() == 3 || levelDto.getLevel() == 2) {
            return null;
        }
        // 乡镇用户与村用户查看的数据范围是一致的
        return riskZoneMapper.selectGroupByQx(levelDto.getQuId());
    }

    @Override
    public List<RiskZonesCountVO> getRiskZoneCount() {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        if (levelDto.getLevel() == 0) {
            //市看区的   qu区
            return riskZoneMapper.getQxRiskZoneCount();
        } else if (levelDto.getLevel() == 1) {
            //区看乡镇的   xz乡镇
            return riskZoneMapper.getXzRiskZoneCount(levelDto.getQuId());
        } else if (levelDto.getLevel() == 2) {
            //乡镇看村子的  xzc行政村
            return riskZoneMapper.getXzcRiskZoneCount(levelDto.getQuId(), levelDto.getTownId());
        } else if (levelDto.getLevel() == 3) {
            //村看自己的
            return riskZoneMapper.getSingleXzcRiskZoneCount(levelDto.getQuId(), levelDto.getTownId(), levelDto.getVillageId());
        }


        return null;
    }

    @Override
    public List<TaskLocationVO> selectTaskLocation(String villageName) {
        return riskZoneMapper.selectTaskLocation(villageName);
    }

    @Override
    public RiskZoneStatisticsVO selectStatisticsByFj(RiskZoneVO riskZoneVO) {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        // 乡镇用户与村用户查看的数据范围是一致的
        riskZoneVO.setQuId(levelDto.getQuId());
        riskZoneVO.setTownId(levelDto.getTownId());
        riskZoneVO.setVillageId(levelDto.getVillageId());
        RiskZoneStatisticsVO riskZoneStatisticsVO = new RiskZoneStatisticsVO();
        riskZoneStatisticsVO.setTotalNumber(riskZoneMapper.selectCountByFj(riskZoneVO));
        riskZoneVO.setStatisticsField("fj");
        riskZoneStatisticsVO.setFjResult(riskZoneMapper.selectStatistics(riskZoneVO));

        return riskZoneStatisticsVO;
    }

    @Override
    public void editPicture(RiskZonePictureParam riskZonePictureParam) {
        riskZoneMapper.editPicture(riskZonePictureParam);
    }

    @Override
    public RiskZoneQrCodeVO getInfo(Integer id) {
        RiskZoneQrCodeVO riskZoneQrCodeVO = riskZoneMapper.getInfo(id);
        if (riskZoneQrCodeVO == null) {
            return null;
        }
        riskZoneQrCodeVO.setPictureDetailUrl(String.format("/admin/sys-file/oss/file?fileName=%s", riskZoneQrCodeVO.getFileName()));
        return riskZoneQrCodeVO;
    }

    @Override
    public List<String> selectGroupByXzc(String xz) {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        return riskZoneMapper.selectGroupByXzc(levelDto.getLevel(), xz, levelDto.getVillageId());
    }

    @Override
    public List<String> selectGroupByXz() {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        if (levelDto.getLevel() == 3) {
            return null;
        }
        return riskZoneMapper.selectGroupByXz(levelDto.getTownId(), levelDto.getQuId());
    }


    @Override
    public List<String> selectGroupByFj() {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        // 乡镇用户与村用户查看的数据范围是一致的
        return riskZoneMapper.selectGroupByFj(levelDto.getQuId(), levelDto.getTownId(), levelDto.getVillageId());
    }

    @Override
    public List<EchartsProperty> selectStatisticsByXz(RiskZoneVO riskZoneVO) {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        // 乡镇用户与村用户查看的数据范围是一致的
        riskZoneVO.setTownId(levelDto.getTownId());
        riskZoneVO.setVillageId(levelDto.getVillageId());
        //riskZoneVO.setStatisticsField("xz");
        if (levelDto.getLevel() == 1 || levelDto.getLevel() == 2) {
            riskZoneVO.setStatisticsField("xzc");
        }
        return riskZoneMapper.selectStatistics(riskZoneVO);
    }

    @Override
    public IPage<RiskZone> selectList(Page<RiskZone> page, RiskZoneVO riskZoneVO) {
        UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
        // 用户无部门信息，则返回空
        if (levelDto == null) {
            return null;
        }
        // 乡镇用户与村用户查看的数据范围是一致的
        riskZoneVO.setQuId(levelDto.getQuId());
        riskZoneVO.setTownId(levelDto.getTownId());
        riskZoneVO.setVillageId(levelDto.getVillageId());
        return riskZoneMapper.selectRiskZonePage(page, riskZoneVO);
    }


    @Override
    public void exportControlListFile(Integer id, HttpServletResponse response) {
        // 1. 查询数据库数据
        RiskZone riskZone = riskZoneMapper.selectById(id);
        if (riskZone == null) {
            throw new IllegalArgumentException("未找到对应的风险区信息，id=" + id);
        }
        //将对象转为map集合，并向内添加图片
        Map<String, Object> stringRiskZoneMap = convertToMap(riskZone);
        if (riskZone.getPictureDetail() != null) {
            SysFile sysFile = new SysFile().selectById(riskZone.getPictureDetail());
            String fileName = sysFile.getFileName();
            String basePath = properties.getLocal().getBasePath();
            riskZone.setPictureDetailUrl(basePath + File.separator + sysFile.getBucketName() + File.separator + fileName);
            stringRiskZoneMap.put("photo", Pictures.ofLocal(riskZone.getPictureDetailUrl()).size(200, 220).create());
        }

        String wordModelName = "controlListMode.docx";
        exportToWord(response, wordModelName, stringRiskZoneMap);

    }

    @Override
    public void exportAvoidRiskFile(Integer id, HttpServletResponse response) {
        // 1. 查询数据库数据
        RiskZone riskZone = riskZoneMapper.selectById(id);
        if (riskZone == null) {
            throw new IllegalArgumentException("未找到对应的风险区信息，id=" + id);
        }
        //将对象转为map集合
        Map<String, Object> stringRiskZoneMap = convertToMap(riskZone);
        String wordModelName = "riskZoneAvoidRiskModel.docx";
        exportToWord(response, wordModelName, stringRiskZoneMap);
    }


    private static void exportToWord(HttpServletResponse response, String wordModelName, Map<String, Object> objectMap) {
        // 2. 文件名更直观（时间戳 + 名称）
        String fileName = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
                + "-" + objectMap.get("fxqmc") + ".docx";

        try {
            // 3. 加载模板
            String templatePath = "file/" + wordModelName;    // 使用正斜杠
            ClassPathResource resource = new ClassPathResource(templatePath);

            try (InputStream in = resource.getInputStream();
                 XWPFTemplate template = XWPFTemplate.compile(in).render(objectMap);
                 ByteArrayOutputStream bos = new ByteArrayOutputStream()) {

                // 渲染到内存流，避免写一半失败
                template.write(bos);

                // 4. 设置响应头
                response.setCharacterEncoding("UTF-8");
                response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
                response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
                response.setHeader("Pragma", "no-cache");
                response.setDateHeader("Expires", 0);

                // 解决文件名中文乱码（兼容各种浏览器）
                String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-Disposition", "attachment;filename*=UTF-8''" + encodedFileName);

                // 写入响应流
                try (OutputStream out = response.getOutputStream()) {
                    bos.writeTo(out);
                    out.flush();
                }
            }
        } catch (IOException e) {
            throw new RuntimeException("导出 Word IO 异常", e);
        } catch (Exception e) {
            throw new RuntimeException("导出 Word 失败", e);
        }
    }


    /**
     * 用于将对象转为map集合
     *
     * @param obj 对象
     * @return map集合
     */
    private Map<String, Object> convertToMap(Object obj) {
        Map<String, Object> map = new HashMap<>();

        try {
            // 使用Spring的BeanWrapper获取所有属性
            BeanWrapper wrapper = PropertyAccessorFactory.forBeanPropertyAccess(obj);
            PropertyDescriptor[] descriptors = wrapper.getPropertyDescriptors();

            for (PropertyDescriptor descriptor : descriptors) {
                String name = descriptor.getName();
                // 跳过class属性
                if ("class".equals(name)) {
                    continue;
                }

                Method readMethod = descriptor.getReadMethod();
                if (readMethod != null) {
                    Object value = readMethod.invoke(obj);
                    map.put(name, value);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("转换对象到Map失败", e);
        }

        return map;
    }


}
