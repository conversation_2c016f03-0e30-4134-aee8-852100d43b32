package com.pig4cloud.pigx.gis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.common.security.service.PigxUser;
import com.pig4cloud.pigx.common.security.util.SecurityUtils;
import com.pig4cloud.pigx.gis.domain.RiskZoneThreatenedPeople;
import com.pig4cloud.pigx.gis.mapper.RiskZoneThreatenedPeopleMapper;
import com.pig4cloud.pigx.gis.service.RiskZoneThreatenedPeopleService;
import com.pig4cloud.pigx.gis.vo.RiskZoneThreatenedPeopleVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Service
public class RiskZoneThreatenedPeopleServiceImpl extends ServiceImpl<RiskZoneThreatenedPeopleMapper, RiskZoneThreatenedPeople> implements RiskZoneThreatenedPeopleService {

	@Resource
	private RiskZoneThreatenedPeopleMapper riskZoneThreatenedPeopleMapper;


	@Override
	public RiskZoneThreatenedPeopleVO getList(Page<RiskZoneThreatenedPeople> page, Integer riskZoneId) {
		List<RiskZoneThreatenedPeople> list = riskZoneThreatenedPeopleMapper.getList(page,riskZoneId).getRecords();

		RiskZoneThreatenedPeopleVO threatenedPeopleVO = new RiskZoneThreatenedPeopleVO();
		threatenedPeopleVO.setHgRiskZoneThreatenedPeopleList(list);
		QueryWrapper<RiskZoneThreatenedPeople> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("risk_zone_id",riskZoneId);
		threatenedPeopleVO.setTotalSize(riskZoneThreatenedPeopleMapper.selectCount(queryWrapper));
		return threatenedPeopleVO;
	}

	@Override
	public void add(RiskZoneThreatenedPeople riskZoneThreatenedPeople) {
		PigxUser loginUser = SecurityUtils.getUser();
		riskZoneThreatenedPeople.setCreateUser(loginUser.getId());
		riskZoneThreatenedPeople.setCreateTime(new Date());
		riskZoneThreatenedPeopleMapper.insert(riskZoneThreatenedPeople);
	}

	@Override
	public void edit(RiskZoneThreatenedPeople riskZoneThreatenedPeople) {
		PigxUser loginUser = SecurityUtils.getUser();
		riskZoneThreatenedPeople.setUpdateUser(loginUser.getId());
		riskZoneThreatenedPeople.setUpdateTime(new Date());
		riskZoneThreatenedPeopleMapper.updateById(riskZoneThreatenedPeople);
	}
}
