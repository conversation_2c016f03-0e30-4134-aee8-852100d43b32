package com.pig4cloud.pigx.gis.service.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.gis.dto.UserDeptLevelDTO;
import com.pig4cloud.pigx.gis.domain.ShpTown;
import com.pig4cloud.pigx.gis.mapper.ShpTownMapper;
import com.pig4cloud.pigx.gis.service.ShpTownService;
import com.pig4cloud.pigx.gis.utils.UserDeptLevelUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class ShpTownServiceImpl extends ServiceImpl<ShpTownMapper, ShpTown> implements ShpTownService{


	@Resource
	private ShpTownMapper shpTownMapper;

	@Override
	public Page<ShpTown> selectShpTownList(Page<ShpTown> shpTownPage, ShpTown shpTown) {

		UserDeptLevelDTO levelDto = UserDeptLevelUtils.getUserDeptLevel();
		// 用户无部门信息，则返回空
		if (levelDto == null) {
			return null;
		}

		return null;
	}
}
