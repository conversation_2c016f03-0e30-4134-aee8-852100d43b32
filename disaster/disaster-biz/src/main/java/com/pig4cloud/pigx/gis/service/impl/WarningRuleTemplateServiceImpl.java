package com.pig4cloud.pigx.gis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pig4cloud.pigx.common.security.util.SecurityUtils;
import com.pig4cloud.pigx.gis.domain.WarningRuleTemplate;
import com.pig4cloud.pigx.gis.mapper.warningRuleTemplateMapper;
import com.pig4cloud.pigx.gis.service.WarningRuleTemplateService;
import com.pig4cloud.pigx.gis.vo.WarningRuleTemplateRequestVO;
import com.pig4cloud.pigx.gis.vo.WarningRuleTemplateResponseVO;
import jakarta.annotation.Resource;
import lombok.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


@Service
public class WarningRuleTemplateServiceImpl extends ServiceImpl<warningRuleTemplateMapper, WarningRuleTemplate> implements WarningRuleTemplateService {

	@Resource
	private warningRuleTemplateMapper warningRuleTemplateMapper;


	@Override
	public IPage<WarningRuleTemplateResponseVO> getWaringRuleTemplate(String ruleName, Long pageNo, Long pageSize) {
		// 分页查询
		LambdaQueryWrapper<WarningRuleTemplate> wrapper = Wrappers.lambdaQuery();
		if (!StringUtils.isEmpty(ruleName)) {
			ruleName = "%" + ruleName + "%";
			wrapper.like(WarningRuleTemplate::getRuleName, ruleName);
		}

		List<WarningRuleTemplate> hgWaringRuleTemplates = warningRuleTemplateMapper.selectList(wrapper);
		List<WarningRuleTemplateResponseVO> waringRuleTemplateResponseVOS = new ArrayList<>();
		// 封装返回数据 hgWaringRuleTemplates 根据code 分组

		hgWaringRuleTemplates.stream().collect(Collectors.groupingBy(WarningRuleTemplate::getCode))
				.forEach((code, hgWarningRuleTemplateList) -> {
					WarningRuleTemplateResponseVO warningRuleTemplateResponseVO = new WarningRuleTemplateResponseVO();
					warningRuleTemplateResponseVO.setCode(code);
					warningRuleTemplateResponseVO.setRuleName(hgWarningRuleTemplateList.get(0).getRuleName());
					warningRuleTemplateResponseVO.setCreateTime(hgWarningRuleTemplateList.get(0).getCreateTime());
					warningRuleTemplateResponseVO.setCreateBy(hgWarningRuleTemplateList.get(0).getCreateBy());
					warningRuleTemplateResponseVO.setDefaultFlag(hgWarningRuleTemplateList.get(0).getDefaultFlag());
					warningRuleTemplateResponseVO.setApplyTime(hgWarningRuleTemplateList.get(0).getApplyTime());
					warningRuleTemplateResponseVO.setWarningRuleTemplate(hgWarningRuleTemplateList);
					warningRuleTemplateResponseVO.setRuleType(hgWarningRuleTemplateList.get(0).getRuleType());
					waringRuleTemplateResponseVOS.add(warningRuleTemplateResponseVO);
				});

		// 针对 ysWaringConfigTemplateResponseVOS 前端分页
		List<WarningRuleTemplateResponseVO> rtList = waringRuleTemplateResponseVOS.stream()
				.skip((pageNo - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
		Page<WarningRuleTemplateResponseVO> rtPage = new Page<>(pageNo, pageSize, waringRuleTemplateResponseVOS.size());
		rtPage.setRecords(rtList);
		return rtPage;
	}



	@Override
	public Integer addWaringRuleTemplate(WarningRuleTemplateRequestVO warningRuleTemplateRequestVO) {

		String code = UUID.randomUUID().toString();
		List<WarningRuleTemplate> hgWarningRuleTemplates = warningRuleTemplateRequestVO.getHgWarningRuleTemplate();
		hgWarningRuleTemplates.forEach(hgWarningRuleTemplate -> {
			hgWarningRuleTemplate.setCode(code);
			hgWarningRuleTemplate.setId(UUID.randomUUID().toString());
			hgWarningRuleTemplate.setCreateTime(new Date());
			hgWarningRuleTemplate.setCreateBy(SecurityUtils.getUser().getUsername());
			hgWarningRuleTemplate.setDefaultFlag(warningRuleTemplateRequestVO.getDefaultFlag());
			hgWarningRuleTemplate.setRuleName(warningRuleTemplateRequestVO.getRuleName());
			hgWarningRuleTemplate.setRuleType(warningRuleTemplateRequestVO.getRuleType());
		});

		this.saveBatch(hgWarningRuleTemplates);
		return 0;
	}

	@Override
	public Integer updateWaringRuleTemplate(WarningRuleTemplateRequestVO hgWarningRuleTemplateRequestVO) {
		// 根据code 删除数据
		warningRuleTemplateMapper.delete(Wrappers.<WarningRuleTemplate>lambdaQuery().eq(WarningRuleTemplate::getCode, hgWarningRuleTemplateRequestVO.getCode()));
		return addWaringRuleTemplate(hgWarningRuleTemplateRequestVO);
	}

	@Override
	public Integer deleteWaringRuleTemplate(@NonNull String code) {
		return warningRuleTemplateMapper.delete(Wrappers.<WarningRuleTemplate>lambdaQuery().eq(WarningRuleTemplate::getCode, code));
	}
}
