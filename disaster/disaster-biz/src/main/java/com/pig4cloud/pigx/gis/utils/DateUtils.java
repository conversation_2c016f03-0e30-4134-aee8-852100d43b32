package com.pig4cloud.pigx.gis.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

public class DateUtils extends org.apache.commons.lang3.time.DateUtils{
	public static String YYYY = "yyyy";

	public static String YYYY_MM = "yyyy-MM";

	public static String YYYY_MM_DD = "yyyy-MM-dd";

	public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

	public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

	private static String dateFilePrefix = "yyyyMM";

	private static String[] parsePatterns = {
			"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
			"yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
			"yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

	public static String getDateFilePrefix() {
		return dateTimeNow(dateFilePrefix);
	}

	/**
	 * 获取当前Date型日期
	 *
	 * @return Date() 当前日期
	 */
	public static Date getNowDate() {
		return new Date();
	}

	/**
	 * 获取当前Date型日期前30分钟的时间
	 *
	 * @return Date() 前30分钟的时间
	 */
	public static Date getThirtyMinutesAgo() {
		return Date.from(LocalDateTime.now().minusMinutes(30).atZone(ZoneId.systemDefault()).toInstant());
	}

	/**
	 * 获取当前日期, 默认格式为yyyy-MM-dd
	 *
	 * @return String
	 */
	public static String getDate() {
		return dateTimeNow(YYYY_MM_DD);
	}

	public static final String getTime() {
		return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
	}

	public static final String dateTimeNow() {
		return dateTimeNow(YYYYMMDDHHMMSS);
	}

	public static final String dateTimeNow(final String format) {
		return parseDateToStr(format, new Date());
	}

	public static final String dateTime(final Date date) {
		return parseDateToStr(YYYY_MM_DD, date);
	}

	public static final String parseDateToStr(final String format, final Date date) {
		return new SimpleDateFormat(format).format(date);
	}

	public static final Date dateTime(final String format, final String ts) {
		try {
			return new SimpleDateFormat(format).parse(ts);
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 日期路径 即年/月/日 如2018/08/08
	 */
	public static final String datePath() {
		Date now = new Date();
		return DateFormatUtils.format(now, "yyyy/MM/dd");
	}

	/**
	 * 日期路径 即年/月/日 如20180808
	 */
	public static final String dateTime() {
		Date now = new Date();
		return DateFormatUtils.format(now, "yyyyMMdd");
	}

	/**
	 * 日期型字符串转化为日期 格式
	 */
	public static Date parseDate(Object str) {
		if (str == null) {
			return null;
		}
		try {
			return parseDate(str.toString(), parsePatterns);
		} catch (ParseException e) {
			return null;
		}
	}

	/**
	 * 使用 UTC 时区和格式 YYYY-MM-DDThh:mm:ssZ（其中 Z 表示 UTC 时区）
	 * @return
	 */
	public static String getUTCDate() {
		// 获取当前时间的 UTC 时间戳
		Instant now = Instant.now(); // Instant 是基于 UTC 的时间戳
		// 将 Instant 转换为 UTC 时区的 ZonedDateTime
		ZonedDateTime utcTime = now.atZone(ZoneOffset.UTC);
		// 使用 ISO 8601 格式化日期，按照 'YYYY-MM-DD'T'HH:mm:ss'Z' 格式
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
		// 格式化日期
		return formatter.format(utcTime);
	}

	/**
	 * 获取服务器启动时间
	 */
	public static Date getServerStartDate() {
		long time = ManagementFactory.getRuntimeMXBean().getStartTime();
		return new Date(time);
	}

	/**
	 * 计算相差天数
	 */
	public static int differentDaysByMillisecond(Date date1, Date date2) {
		return Math.abs((int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24)));
	}

	/**
	 * 计算时间差
	 *
	 * @param endDate   最后时间
	 * @param startTime 开始时间
	 * @return 时间差（天/小时/分钟）
	 */
	public static String timeDistance(Date endDate, Date startTime) {
		long nd = 1000 * 24 * 60 * 60;
		long nh = 1000 * 60 * 60;
		long nm = 1000 * 60;
		// long ns = 1000;
		// 获得两个时间的毫秒时间差异
		long diff = endDate.getTime() - startTime.getTime();
		// 计算差多少天
		long day = diff / nd;
		// 计算差多少小时
		long hour = diff % nd / nh;
		// 计算差多少分钟
		long min = diff % nd % nh / nm;
		// 计算差多少秒//输出结果
		// long sec = diff % nd % nh % nm / ns;
		return day + "天" + hour + "小时" + min + "分钟";
	}

	/**
	 * 获取一年后时间
	 *
	 * @return
	 */
	public static String getAfterOneYear() {
		LocalDate today = LocalDate.now();
		LocalDate nextYear = today.plus(1, ChronoUnit.YEARS);
		return nextYear.toString();
	}

	/**
	 * 两个时间之间相差距离多少天
	 *
	 * @param startTime 时间参数 1：
	 * @param endTime   时间参数 2：
	 * @return 相差天数
	 */
	public static long getDistanceDays(String startTime, String endTime) throws Exception {
		return getDistanceDays(startTime, endTime, false);
	}

	/**
	 * 两个时间之间相差距离多少天
	 *
	 * @param startTime    时间参数 1：
	 * @param endTime      时间参数 2：
	 * @param negativeFlag 是否返回负值
	 * @return 相差天数
	 */
	public static long getDistanceDays(String startTime, String endTime, Boolean negativeFlag) throws Exception {
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		Date one;
		Date two;
		long days = 0;
		try {
			one = df.parse(startTime);
			two = df.parse(endTime);
			long time1 = one.getTime();
			long time2 = two.getTime();
			long diff;
			if (negativeFlag != null && negativeFlag) {
				diff = time2 - time1;
			} else {
				if (time1 < time2) {
					diff = time2 - time1;
				} else {
					diff = time1 - time2;
				}
			}
			days = diff / (1000 * 60 * 60 * 24);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		// 返回相差多少天
		return days;
	}

	/**
	 * 增加 LocalDateTime ==> Date
	 */
	public static Date toDate(LocalDateTime temporalAccessor) {
		ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
		return Date.from(zdt.toInstant());
	}

	/**
	 * 增加 LocalDate ==> Date
	 */
	public static Date toDate(LocalDate temporalAccessor) {
		LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
		ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
		return Date.from(zdt.toInstant());
	}

	public static boolean beforeNow(String time, String format){
		SimpleDateFormat df = new SimpleDateFormat(format);
		try {
			Date beginDate = df.parse(time);
			return beginDate.before(new Date());
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return false;
	}

	/**
	 * 获取今日日零点
	 *
	 * @return
	 */
	public static Date getTimesDayMorning() {
		Date time = new Date();
		Calendar calendar2 = Calendar.getInstance();
		calendar2.setTime(time);
		calendar2.set(calendar2.get(Calendar.YEAR), calendar2.get(Calendar.MONTH), calendar2.get(Calendar.DAY_OF_MONTH),
				0, 0, 0);
		calendar2.set(Calendar.MILLISECOND, 0);
		Date date = calendar2.getTime();
		return date;
	}

	/**
	 * 获取今日最晚
	 *
	 * @return
	 */
	public static Date getTimesDayNight() {
		Date time = new Date();
		Calendar calendar2 = Calendar.getInstance();
		calendar2.setTime(time);
		calendar2.set(calendar2.get(Calendar.YEAR), calendar2.get(Calendar.MONTH), calendar2.get(Calendar.DAY_OF_MONTH),
				23, 59, 59);
		calendar2.set(Calendar.MILLISECOND,0);
		Date date = calendar2.getTime();
		return date;
	}

	/**
	 * 获得本周一0点时间
	 */
	public static Date getTimesWeekMorning() {
		Calendar cal = Calendar.getInstance();
		cal.setFirstDayOfWeek(Calendar.MONDAY);
		cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
		cal.set(Calendar.HOUR_OF_DAY, cal.getActualMinimum(Calendar.HOUR_OF_DAY));
		cal.set(Calendar.MINUTE, cal.getActualMinimum(Calendar.MINUTE));
		cal.set(Calendar.SECOND, cal.getActualMinimum(Calendar.SECOND));
		cal.set(Calendar.MILLISECOND,0);
		return cal.getTime();
	}

	/**
	 * 获得本周日24点时间
	 */
	public static Date getTimesWeekNight() {
		Calendar cal = Calendar.getInstance();
		cal.setFirstDayOfWeek(Calendar.MONDAY);
		cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
		cal.set(Calendar.HOUR_OF_DAY, cal.getActualMaximum(Calendar.HOUR_OF_DAY));
		cal.set(Calendar.MINUTE, cal.getActualMaximum(Calendar.MINUTE));
		cal.set(Calendar.SECOND, cal.getActualMaximum(Calendar.SECOND));
		cal.set(Calendar.MILLISECOND,0);
		return cal.getTime();
	}

	/**
	 * 获得本月第一天0点时间
	 */
	public static Date getTimesMonthMorning() {
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
		cal.set(Calendar.HOUR_OF_DAY, cal.getActualMinimum(Calendar.HOUR_OF_DAY));
		cal.set(Calendar.MINUTE, cal.getActualMinimum(Calendar.MINUTE));
		cal.set(Calendar.SECOND, cal.getActualMinimum(Calendar.SECOND));
		cal.set(Calendar.MILLISECOND,0);
		return cal.getTime();
	}

	/**
	 * 获得本月最后一天24点时间
	 */
	public static Date getTimesMonthNight() {
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
		cal.set(Calendar.HOUR_OF_DAY, cal.getActualMaximum(Calendar.HOUR_OF_DAY));
		cal.set(Calendar.MINUTE, cal.getActualMaximum(Calendar.MINUTE));
		cal.set(Calendar.SECOND, cal.getActualMaximum(Calendar.SECOND));
		cal.set(Calendar.MILLISECOND,0);
		return cal.getTime();
	}

	/**
	 * 获得上个月第一天0点时间
	 */
	public static Date getTimesLastMonthMorning() {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为本月的第一天
		calendar.add(Calendar.MONTH, -1); // 减去一个月，即上个月
		calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置小时为0
		calendar.set(Calendar.MINUTE, 0); // 设置分钟为0
		calendar.set(Calendar.SECOND, 0); // 设置秒为0
		calendar.set(Calendar.MILLISECOND, 0); // 设置毫秒为0
		return calendar.getTime();
	}

	/**
	 * 获得上个月最后一天结束时间
	 */
	public static Date getTimesLastMonthNight() {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为本月的第一天
		calendar.add(Calendar.MONTH, -1); // 减去一个月，即上个月
		calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置小时为0
		calendar.set(Calendar.MINUTE, 0); // 设置分钟为0
		calendar.set(Calendar.SECOND, 0); // 设置秒为0
		calendar.set(Calendar.MILLISECOND, 0); // 设置毫秒为0

		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH)); // 设置为上个月的最后一天
		calendar.set(Calendar.HOUR_OF_DAY, 23); // 设置小时为23
		calendar.set(Calendar.MINUTE, 59); // 设置分钟为59
		calendar.set(Calendar.SECOND, 59); // 设置秒为59
		calendar.set(Calendar.MILLISECOND, 999); // 设置毫秒为999
		return calendar.getTime();
	}

	/**
	 * 获得本年第一天0点时间
	 */
	public static Date getTimesYearMorning() {
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.DAY_OF_YEAR, cal.getActualMinimum(Calendar.DAY_OF_YEAR));
		//cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
		cal.set(Calendar.HOUR_OF_DAY, cal.getActualMinimum(Calendar.HOUR_OF_DAY));
		cal.set(Calendar.MINUTE, cal.getActualMinimum(Calendar.MINUTE));
		cal.set(Calendar.SECOND, cal.getActualMinimum(Calendar.SECOND));
		cal.set(Calendar.MILLISECOND,0);
		return cal.getTime();
	}

	/**
	 * 获得本年最后一天24点时间
	 */
	public static Date getTimesYearNight() {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.MONTH, Calendar.DECEMBER); // 设置为12月
		calendar.set(Calendar.DAY_OF_MONTH, 31); // 设置为31日
		calendar.set(Calendar.HOUR_OF_DAY, 23); // 设置小时为23
		calendar.set(Calendar.MINUTE, 59); // 设置分钟为59
		calendar.set(Calendar.SECOND, 59); // 设置秒为59
		calendar.set(Calendar.MILLISECOND, 999); // 设置毫秒为999
		return calendar.getTime();
	}

	/**
	 * 输入一个开始时间和一个结束时间，获取这个区间内的所有时间列表
	 *
	 * @param startTime
	 * @param endTime
	 * @param format    输入和输出的格式需要保持一致
	 * @param type      1 月类型 2 天类型 3 小时类型 4 年类型
	 */
	public static List<String> getDateList(Date startTime, Date endTime, String format, int type) {

		int type2;
		if (type == 1) { //月
			type2 = Calendar.MONTH;
		} else if (type == 2) {//日
			type2 = Calendar.DAY_OF_MONTH;
		} else if (type == 3) {//小时
			type2 = Calendar.HOUR_OF_DAY;
		} else {
			type2 = Calendar.YEAR;
		}
		List<String> result = new ArrayList<>();
		try {
			GregorianCalendar[] dateArr = getDateStr(parseDateToStr(YYYY_MM_DD_HH_MM_SS,startTime), parseDateToStr(YYYY_MM_DD_HH_MM_SS,endTime), format, type2);
			SimpleDateFormat sdf = new SimpleDateFormat(format);
			for (GregorianCalendar e : dateArr) {
				result.add(sdf.format(e.getTime()));
			}
		} catch (ParseException e) {
			e.printStackTrace();
		}

		return result;
	}

	public static Date getDateBefore(Date date, int minutes){
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.MINUTE, -minutes);
		return cal.getTime();
	}


	/**
	 * 根据开始时间和结束时间获取这段时间范围内的时间列表
	 */
	private static GregorianCalendar[] getDateStr(String startTime, String endTime, String format, int type) throws ParseException {
		Vector<GregorianCalendar> v = new Vector<>();
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		GregorianCalendar gc1 = new GregorianCalendar(), gc2 = new GregorianCalendar();
		gc1.setTime(sdf.parse(startTime));
		gc2.setTime(sdf.parse(endTime));
		do {
			GregorianCalendar gc3 = (GregorianCalendar) gc1.clone();
			v.add(gc3);
			gc1.add(type, 1);
		} while (!gc1.after(gc2));
		return v.toArray(new GregorianCalendar[v.size()]);
	}

	public static void main(String[] args) {
		System.out.println(parseDateToStr(YYYY_MM_DD_HH_MM_SS,getTimesMonthMorning()));
	}
}
