package com.pig4cloud.pigx.gis.utils;

import com.pig4cloud.pigx.gis.dto.UserDeptLevelDTO;
import com.pig4cloud.pigx.admin.api.entity.SysDept;
import com.pig4cloud.pigx.common.security.util.SecurityUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class UserDeptLevelUtils {
	/**
	 * 获取当前登录用户所属的行政区划层级DTO，目前村用户和乡镇用户的数据权限范围一致
	 *
	 * @return
	 */
	public static UserDeptLevelDTO getUserDeptLevel() {
		Long deptId = SecurityUtils.getUser().getDeptId();
		SysDept dept = new SysDept().selectById(deptId);
		List<SysDept> sysDepts = new SysDept().selectAll();
		int level = getDeptLevel(deptId, sysDepts);
		// 用户无部门信息，或所属机构无层级信息，返回空对象
		if (dept == null) {
			return new UserDeptLevelDTO();
		}


		// 乡镇用户与村用户查看的数据范围是一致的
		Long townId = null, villageId = null,  quId = null;
		//0 市用户    扬州市
		//区用户  江都区
		if (level == 1) {
			quId=deptId;
		}else if (level == 2 ) {
			// 乡镇用户  大桥镇
			quId=dept.getParentId();
			townId = deptId;
		} else if (level >= 3) {
			// 村用户   六和村
			quId=new SysDept().selectById(dept.getParentId()).getParentId();
			townId = dept.getParentId();
			villageId = deptId;
			level = 3;
		}
		return UserDeptLevelDTO.builder().dept(dept).level(level).quId(quId).townId(townId).villageId(villageId).build();
	}

	public static int getDeptLevel(Long deptId, List<SysDept> sysDepts) {
		// 构建 deptId -> parentId 映射
		Map<Long, Long> parentMap = sysDepts.stream()
				.collect(Collectors.toMap(SysDept::getDeptId, SysDept::getParentId));

		int level = 1;
		Long current = deptId;

		// 向上查找直到没有父级（通常是 parentId 为 0 或 null）
		while (parentMap.containsKey(current)) {
			Long parentId = parentMap.get(current);
			if (parentId == null || parentId == 0 || parentId.equals(current)) {
				break;
			}
			level++;
			current = parentId;
		}

		return level-1;
	}


	/**
	 * 获取部门完整路径
	 * @param deptId
	 * @return
	 */
	public static String getFullDeptPath(Long deptId) {
		// 存储从根部门到当前部门的完整路径
		LinkedList<String> nameList = new LinkedList<>();
		Long currentId = deptId;

		// 循环向上查询直到根部门
		while (currentId != null && currentId != 0) {
			SysDept dept = new SysDept().selectById(currentId);
			if (dept == null) break;

			// 将当前部门名称添加到链表头部（实现顺序反转）
			nameList.addFirst(dept.getName());

			// 移动到上级部门
			currentId = dept.getParentId();
		}

		// 将列表转换为空格分隔的字符串
		return nameList.stream().collect(Collectors.joining(""));
	}


	/**
	 * 获取部门路径，不包含根部门名称
	 *
	 * @param deptId
	 * @return
	 */
	public static String getDeptPathWithoutRoot(Long deptId) {
		// 存储从根部门下一级到当前部门的路径
		LinkedList<String> nameList = new LinkedList<>();
		Long currentId = deptId;

		// 循环向上查询直到根部门
		while (currentId != null && currentId != 0) {
			SysDept dept = new SysDept().selectById(currentId);
			if (dept == null) break;

			// 将当前部门名称添加到链表头部
			nameList.addFirst(dept.getName());

			// 移动到上级部门
			currentId = dept.getParentId();
		}

		// 移除根部门名称（如果存在）
		if (!nameList.isEmpty()) {
			nameList.removeFirst();  // 移除根部门
		}

		// 将列表转换为空格分隔的字符串
		return nameList.stream().collect(Collectors.joining(""));
	}

	public static SysDept  getParentDeptInfo(Long deptId) {
		// 参数校验
		if (deptId == null) {
			return null;
		}
		try {
			// 查询部门信息
			SysDept dept = new SysDept().selectById(deptId);
			if (dept == null) {
				return null;
			}

			// 查询父部门信息
			Long parentId = dept.getParentId();
			if (parentId == null) {
				return null;
			}

			SysDept parentDept = new SysDept().selectById(parentId);
			return parentDept;
		} catch (Exception e) {
			// 异常处理可以根据实际需求进行调整
			return null;
		}
	}

}
