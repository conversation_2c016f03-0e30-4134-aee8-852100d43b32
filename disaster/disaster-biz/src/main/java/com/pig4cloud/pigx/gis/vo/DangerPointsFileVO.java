package com.pig4cloud.pigx.gis.vo;


import lombok.Data;

@Data
public class DangerPointsFileVO {


    /**
     * 隐患点省级编号id
     */
    private String id;

    /**
     * 现场照片文件id
     */
    private Long pictureLocaleId;

    /**
     * 现场照片
     */
    private String pictureLocale;

    /**
     * 现场照片路径
     */
    private String pictureLocaleUrl;

    /**
     * 防灾预案图id
     */
    private Long pictureDisasterPlanId;

    /**
     * 防灾预案图
     */
    private String pictureDisasterPlan;

    /**
     * 防灾预案图路径
     */
    private String pictureDisasterPlanUrl;


    /**
     * 平面图文件id
     */
    private Long pictureFlatId;

    /**
     * 平面图
     */
    private String pictureFlat;

    /**
     * 平面图路径
     */
    private String pictureFlatUrl;

    /**
     * 剖面图文件id
     */
    private Long pictureSectionId;

    /**
     * 剖面图
     */
    private String pictureSection;

    /**
     * 剖面图路径
     */
    private String pictureSectionUrl;


}
