package com.pig4cloud.pigx.gis.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 隐患点查询VO
 *
 * <AUTHOR>
 * @date 2025年5月28日13:59:02
 */
@Data
public class DangerPointsReqVO implements Serializable {

	private static final long serialVersionUID = 1L;


	/**
	 * 分页的查询参数，隐患点类型（不稳定斜坡，滑坡，崩塌，泥石流）
	 */
	private String type;

	/**
	 * 分页的查询参数，稳定性现状（基本稳定，稳定，不稳定）
	 */
	private String stability;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 隐患点编号
	 */
	private String dangerPointsNumber;

	/**
	 * 空间查询字段，WKT格式
	 */
	private String geom;

	/**
	 * 隐患点类型（1：不稳定斜坡，2：滑坡，3：崩塌，4：泥石流）
	 */
	private String dangerPointsType;

	/**
	 * 隐患点类型数组
	 */
	private List<String> dangerPointsTypeList;

	/**
	 * 规模（1：小型，2：中型）
	 */
	private String scale;

	/**
	 * 管理层级（1：县级，2：村级，3：乡级）
	 */
	private String managementLevel;

	/**
	 * 稳定性分析
	 */
	private String stabilityAnalysis;

	/**
	 * 稳定性现状（1：基本稳定，2：稳定，3：不稳定）
	 */
	private String stabilityStatus;

	/**
	 * 稳定性现状数组
	 */
	private List<String> stabilityStatusList;

	/**
	 * 稳定性趋势（1：基本稳定，2：稳定，3：不稳定）
	 */
	private String stabilityTrend;

	/**
	 * 区县名称
	 */
	private String county;

	/**
	 * 乡镇名称
	 */
	private String town;

	/**
	 * 村名称
	 */
	private String village;

	/**
	 * 组（小地方）名称
	 */
	private String smallPlace;

	/**
	 * 区县id
	 */
	private Long countyId;

	/**
	 * 乡镇id
	 */
	private Long townId;

	/**
	 * 行政村id
	 */
	private Long villageId;

	/**
	 * 区id，多选参数
	 */
	private List<Long> countyIds;

	/**
	 * 乡镇id，多选参数
	 */
	private List<Long> townIds;

	/**
	 * 行政村id，多选参数
	 */
	private List<Long> villageIds;

	/**
	 * 分类统计的字段
	 */
	private String statisticsField;

	/**
	 * 行政区划ID
	 */
	private Long deptId;

}

