package com.pig4cloud.pigx.gis.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DeviceVO {


    /**
     * 主键
     */
    @Schema(description="主键")
    private Long id;


    /**
     * 隐患点或风险区灾害名称
     */
    @Schema(description="隐患点或风险区灾害名称")
    private String disasterName;

    /**
     * 空间字段
     */
    @Schema(description="空间字段")
    private String geom;

//    /**
//     * 省级平台统一编号
//     */
//    @Schema(description="省级平台统一编号")
//    private String provincialCode;

    /**
     * 监测设备名称
     */
    @Schema(description="监测设备名称")
    private String deviceName;

    /**
     * 隐患点省平台统一编号
     */
    @Schema(description="隐患点省平台统一编号")
    private String dangerPointsCode;

    /**
     * 风险区统一编号
     */
    @Schema(description="风险区统一编号")
    private String riskZoneCode;

//    /**
//     * 监测类型，1隐患点，2风险区
//     */
//    @Schema(description="监测类型，1隐患点，2风险区")
//    private String monitorDataType;

    /**
     * 传感器类型
     */
    @Schema(description="传感器类型")
    private String sensorType;

    /**
     * 传感器类型名称
     */
    @Schema(description="传感器类型名称")
    private String sensorTypeName;

    /**
     * 是否同步到部平台
     */
    @Schema(description="是否同步到部平台")
    private Boolean syncFlag;

    /**
     * 市
     */
    @Schema(description="市")
    private String city;

    /**
     * 区县
     */
    @Schema(description="区县")
    private String county;

    /**
     * 经度
     */
    @Schema(description="经度")
    private Double longitude;

    /**
     * 纬度
     */
    @Schema(description="纬度")
    private Double latitude;

    /**
     * 监测点名称
     */
    @Schema(description="监测点名称")
    private String monitorPointName;

    /**
     * 监测点统一编号
     */
    @Schema(description="监测点统一编号")
    private String monitorPointCode;

    /**
     * 设备状态
     */
    @Schema(description="设备状态")
    private String deviceStatus;

    /**
     * 设备ID(clientID)
     */
    @Schema(description="设备ID(clientID)")
    private String clientId;

    /**
     * 设备key
     */
    @Schema(description="设备key")
    private String deviceKey;

    /**
     * 设备sn
     */
    @Schema(description="设备sn")
    private String deviceSn;

    /**
     * 设备型号
     */
    @Schema(description="设备型号")
    private String deviceModel;

    /**
     * 通讯方式，0: GPRS/3G/4G  1: NB-Iot
     */
    @Schema(description="通讯方式，0: GPRS/3G/4G  1: NB-Iot")
    private String network;

    /**
     * 接入协议，0: MQTT  1: HTTP  2: COAP
     */
    @Schema(description="接入协议，0: MQTT  1: HTTP  2: COAP")
    private String protocol;

    /**
     * 设备类型，0: 单参数  1: 多参数  2: 本地组网
     */
    @Schema(description="设备类型，0: 单参数  1: 多参数  2: 本地组网")
    private String deviceType;

    /**
     * 设备厂商
     */
    @Schema(description="设备厂商")
    private String deviceCompany;


}
