package com.pig4cloud.pigx.gis.vo;


import com.pig4cloud.pigx.gis.domain.WarningRuleTemplate;
import lombok.Data;

import java.util.List;

@Data
public class WarningRuleTemplateRequestVO {

	/**
	 * 预警类型 1 雨量 2 裂缝 3 GNSS 4倾角 5 加速度
	 */
	private Integer ruleType;

	/**
	 * 预警规则名称
	 */
	private String ruleName;



	/**
	 * 编码
	 */
	private String code;

	/**
	 *   应用到所有设备标识  1否  2是
	 */
	private Integer defaultFlag;


	/**
	 * 具体配置
	 */
	private List<WarningRuleTemplate> hgWarningRuleTemplate;

}
