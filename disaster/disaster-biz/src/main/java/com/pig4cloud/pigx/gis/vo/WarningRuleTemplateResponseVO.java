package com.pig4cloud.pigx.gis.vo;


import com.pig4cloud.pigx.gis.domain.WarningRuleTemplate;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class WarningRuleTemplateResponseVO {

	/**
	 * 预警规则名称
	 */
	private String ruleName;


	/**
	 * 编码
	 */
	private String code;


	/**
	 * 创建时间
	 */
	private Date createTime;


	/**
	 * 应用时间
	 */
	private Date applyTime;


	/**
	 *   应用到所有设备标识  1否  2是
	 */
	private Integer defaultFlag;


	/**
	 * 创建人姓名
	 */
	private String createBy;


	/**
	 * 预警类型 1 雨量 2 裂缝 3 GNSS 4倾角 5 加速度
	 */
	private Integer ruleType;


	/**
	 * 具体配置
	 */
	private List<WarningRuleTemplate> warningRuleTemplate;

}
