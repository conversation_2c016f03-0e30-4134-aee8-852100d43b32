<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.gis.mapper.AppDailyReportingMapper">

  <resultMap id="appDailyReportingMap" type="com.pig4cloud.pigx.gis.entity.AppDailyReporting">
        <id property="id" column="id"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="address" column="address"/>
        <result property="dangerPointsType" column="danger_points_type"/>
        <result property="dangerLevel" column="danger_level"/>
        <result property="threateningHousehold" column="threatening_household"/>
        <result property="threateningPopulation" column="threatening_population"/>
        <result property="occurrenceDate" column="occurrence_date"/>
        <result property="characteristic" column="characteristic"/>
        <result property="preventionWork" column="prevention_work"/>
        <result property="monitoringName" column="monitoring_name"/>
        <result property="monitoringPhone" column="monitoring_phone"/>
        <result property="suggestions" column="suggestions"/>
        <result property="remark" column="remark"/>
        <result property="signatureImage" column="signature_image"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
</mapper>