<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.gis.mapper.AppDisasterReportingMapper">

  <resultMap id="appDisasterReportingMap" type="com.pig4cloud.pigx.gis.entity.AppDisasterReporting">
        <id property="id" column="id"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="regionId" column="region_id"/>
        <result property="address" column="address"/>
        <result property="disasterType" column="disaster_type"/>
        <result property="disasterDetails" column="disaster_details"/>
        <result property="peopleAffectedName" column="people_affected_name"/>
        <result property="peopleAffectedPhone" column="people_affected_phone"/>
        <result property="urgency" column="urgency"/>
        <result property="status" column="status"/>
        <result property="reviewer" column="reviewer"/>
        <result property="reviewTime" column="review_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
  </resultMap>
</mapper>