<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.gis.mapper.AppPatrolPlanMapper">


	<select id="selectPatrolPeople" resultType="java.lang.String">
		SELECT
			u.username  as patrolPeopleName
		FROM
			sys_user u
		<where>
			u.del_flag = '0'
			<if test="regionId != null">
				AND u.dept_id = #{regionId}
			</if>
		</where>
		limit 1
	</select>

	<select id="getpatrolList" resultType="com.pig4cloud.pigx.gis.domain.AppPatrolPlan">
		SELECT id,
		       group_id,
		       patrol_type,
		       task_type,
		       region_id,
		       risk_zone_id,
		       urgency,
		       expected_completion_date,
		       task_cycle_num,
		       task_cycle_unit,
		       task_end_date,
		       description,
		       task_status,
		       reviewer_id,
		       approval_time,
		       del_flag,
		       create_by,
		       update_by,
		       create_time,
		       update_time,
		       region,
		       patrol_people_name
		FROM app_patrol_plan
		WHERE del_flag = '0'
		<if test="taskStatus != null">
			AND task_status = #{taskStatus}
		</if>
		<if test="taskType != null">
			AND task_type = #{taskType}
		</if>
		<if test="patrolType != null">
			AND patrol_type = #{patrolType}
		</if>
		<if test="startTime != null">
			AND create_time >= #{startTime}
		</if>
		<if test="endTime != null">
			AND create_time &lt;= #{endTime}
		</if>
		order by urgency desc , create_time desc
	</select>
</mapper>