<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.gis.mapper.AppPatrolPlanPointMapper">


	<select id="selectListByPlainId" resultType="com.pig4cloud.pigx.gis.dto.AppPatrolPlanPointDTO">
		SELECT
			appp.plan_id AS plainId,
			appp.point_id AS pointId,
			hdp.name AS pointName
		FROM
			app_patrol_plan_point  appp
		left join t_danger_points  hdp
		on appp.point_id = hdp.danger_points_number
		WHERE
			1=1
		  <if test="planId != null">
			AND
			  appp.plan_id = #{planId}
		</if>
		order by appp.sort_order
	</select>
</mapper>