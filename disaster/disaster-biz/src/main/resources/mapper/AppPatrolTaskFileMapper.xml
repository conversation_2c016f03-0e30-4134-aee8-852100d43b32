<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.gis.mapper.AppPatrolTaskFileMapper">


	<select id="selectFileListByTaskId" resultType="com.pig4cloud.pigx.gis.vo.AppPatrolTaskPhotoVO">
		select aptf.id as appPatrolTaskFileId,
		       sf.id as fileId,
		       file_name
		 from app_patrol_task_file aptf
		left join sys_file sf on aptf.file_id = sf.id
		where aptf.task_id = #{taskId}
	</select>
</mapper>