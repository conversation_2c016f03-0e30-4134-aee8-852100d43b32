<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.gis.mapper.AppPatrolTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.pig4cloud.pigx.gis.domain.AppPatrolTask">
        <id column="id" property="id" />
        <result column="plan_id" property="planId" />
        <result column="point_id" property="pointId" />
        <result column="point_type" property="pointType" />
        <result column="sort_order" property="sortOrder" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="risk_flag" property="riskFlag" />
        <result column="threatening_household" property="threateningHousehold" />
        <result column="threatening_population" property="threateningPopulation" />
        <result column="description" property="description" />
        <result column="technical_support_flag" property="technicalSupportFlag" />
        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, plan_id, point_id, point_type, sort_order, longitude, latitude, 
        risk_flag, threatening_household, threatening_population, description, 
        technical_support_flag, del_flag, create_by, update_by, create_time, update_time
    </sql>

    <!-- 分页查询巡查任务列表 -->
    <select id="selectAppPatrolTaskPage" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM app_patrol_task
        <where>
            del_flag = '0'
            <if test="appPatrolTask.planId != null">
                AND plan_id = #{appPatrolTask.planId}
            </if>
            <if test="appPatrolTask.pointId != null">
                AND point_id = #{appPatrolTask.pointId}
            </if>
            <if test="appPatrolTask.pointType != null">
                AND point_type = #{appPatrolTask.pointType}
            </if>
            <if test="appPatrolTask.riskFlag != null">
                AND risk_flag = #{appPatrolTask.riskFlag}
            </if>
            <if test="appPatrolTask.technicalSupportFlag != null">
                AND technical_support_flag = #{appPatrolTask.technicalSupportFlag}
            </if>
            <if test="appPatrolTask.description != null and appPatrolTask.description != ''">
                AND description LIKE CONCAT('%', #{appPatrolTask.description}, '%')
            </if>
        </where>
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 根据巡查计划ID查询任务列表 -->
    <select id="selectByPlanId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM app_patrol_task
        WHERE del_flag = '0' 
          AND plan_id = #{planId}
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 根据点位ID查询任务列表 -->
    <select id="selectByPointId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM app_patrol_task
        WHERE del_flag = '0' 
          AND point_id = #{pointId}
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 根据点位类型查询任务列表 -->
    <select id="selectByPointType" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM app_patrol_task
        WHERE del_flag = '0' 
          AND point_type = #{pointType}
        ORDER BY sort_order ASC, create_time DESC
    </select>

</mapper>
