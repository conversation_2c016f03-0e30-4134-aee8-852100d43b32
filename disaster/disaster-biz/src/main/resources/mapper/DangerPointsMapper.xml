<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.gis.mapper.DangerPointsMapper">

    <resultMap type="com.pig4cloud.pigx.gis.domain.DangerPoints" id="DangerPointsResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="dangerPointsType" column="danger_points_type"/>
        <result property="gridNumber" column="grid_number"/>
        <result property="dataBaseNumber" column="data_base_number"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="area" column="area"/>
        <result property="country" column="country"/>
        <result property="village" column="village"/>
        <result property="smallPlace" column="small_place"/>
        <result property="x" column="x"/>
        <result property="y" column="y"/>
        <result property="lon" column="lon"/>
        <result property="lat" column="lat"/>
        <result property="longValue" column="long"/>
        <result property="wide" column="wide"/>
        <result property="height" column="height"/>
        <result property="measure" column="measure"/>
        <result property="volume" column="volume"/>
        <result property="scale" column="scale"/>
        <result property="managementLevel" column="management_level"/>
        <result property="threateningPopulation" column="threatening_population"/>
        <result property="threateningProperty" column="threatening_property"/>
        <result property="dangerLevel" column="danger_level"/>
        <result property="secondaryDisasterTime" column="secondary_disaster_time"/>
        <result property="addressEnvironmentalConditions" column="address_environmental_conditions"/>
        <result property="deformationCharacteristicsActivityDuration"
                column="deformation_characteristics_activity_duration"/>
        <result property="stabilityAnalysis" column="stability_analysis"/>
        <result property="stabilityStatus" column="stability_status"/>
        <result property="stabilityTrend" column="stability_trend"/>
        <result property="causingFactors" column="causing_factors"/>
        <result property="potentialHazards" column="potential_hazards"/>
        <result property="predictionImpendingDisasterState" column="prediction_impending_disaster_state"/>
        <result property="monitoringMethods" column="monitoring_methods"/>
        <result property="monitor" column="monitor"/>
        <result property="positionSupervisor" column="position_supervisor"/>
        <result property="monitorTelephone" column="monitor_telephone"/>
        <result property="textWarningSign" column="text_warning_sign"/>
        <result property="personLiable" column="person_liable"/>
        <result property="personLiableTelephone" column="person_liable_telephone"/>
        <result property="administrators" column="administrators"/>
        <result property="administratorsTelephone" column="administrators_telephone"/>
        <result property="trafficAssistant" column="traffic_assistant"/>
        <result property="trafficAssistantTelephone" column="traffic_assistant_telephone"/>
        <result property="specialManager" column="special_manager"/>
        <result property="specialManagerTelephone" column="special_manager_telephone"/>
        <result property="dangerPointsNumber" column="danger_points_number"/>
        <result property="geom" column="geom"/>
    </resultMap>

    <sql id="selectHgDangerPointsVo">
        select id,
               name,
               danger_points_type,
               grid_number,
               data_base_number,
               province,
               city,
               area,
               country,
               village,
               small_place,
               x,
               y,
               lon,
               lat,
               long,
               wide,
               height,
               measure,
               volume,
               scale,
               management_level,
               threatening_population,
               threatening_property,
               danger_level,
               secondary_disaster_time,
               address_environmental_conditions,
               deformation_characteristics_activity_duration,
               stability_analysis,
               stability_status,
               stability_trend,
               causing_factors,
               potential_hazards,
               prediction_impending_disaster_state,
               monitoring_methods,
               monitor,
               position_supervisor,
               monitor_telephone,
               text_warning_sign,
               person_liable,
               person_liable_telephone,
               administrators,
               administrators_telephone,
               traffic_assistant,
               traffic_assistant_telephone,
               special_manager,
               special_manager_telephone,
               danger_points_number,
               ST_AsGeoJSON(geom) as geom,
               verification_flag,
               geographic_location,
               fill_in_data
        from t_danger_points
    </sql>

    <select id="selectHgDangerPointsList" resultMap="DangerPointsResult">
        <include refid="selectHgDangerPointsVo"/>
        <where>
            <if test="entity.geom != null  and entity.geom != ''">and ST_Intersects(ST_GeomFromText(#{entity.geom}, 4326), geom)</if>
            <if test="entity.type != null and entity.type != ''">and danger_points_type = #{entity.type}</if>
            <if test="entity.stability != null and entity.stability != ''">and (stability_status = #{entity.stability}
                or stability_status = '')
            </if>
            <if test="entity.dangerPointsNumber != null  and entity.dangerPointsNumber != ''">and danger_points_number =
                #{entity.dangerPointsNumber}
            </if>
            <if test="entity.name != null  and entity.name != ''">and name like concat('%', #{entity.name}, '%')</if>
            <if test="entity.dangerPointsType != null  and entity.dangerPointsType != ''">and danger_points_type =
                #{entity.dangerPointsType}
            </if>
            <if test="entity.dangerPointsTypeList != null and entity.dangerPointsTypeList.size > 0">
                and danger_points_type in
                <foreach item="item" index="index" collection="entity.dangerPointsTypeList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="entity.stabilityStatus != null and entity.stabilityStatus != ''">and stability_status =
                #{entity.stabilityStatus}
            </if>
            <if test="entity.stabilityStatusList != null and entity.stabilityStatusList.size > 0">
                and stability_status in
                <foreach item="item" index="index" collection="entity.stabilityStatusList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="entity.county != null  and entity.county != ''">and area = #{entity.county}</if>
            <if test="entity.town != null  and entity.town != ''">and country = #{entity.town}</if>
            <if test="entity.village != null  and entity.village != ''">and village = #{entity.village}</if>
            <if test="entity.smallPlace != null  and entity.smallPlace != ''">and small_place = #{entity.smallPlace}</if>
            <if test="entity.countyId != null">and area = (select name from sys_dept where dept_id =
                #{entity.countyId})
            </if>
            <if test="entity.townId != null">and country = (select name from sys_dept where dept_id =
                #{entity.townId})
            </if>
            <if test="entity.villageId != null">and village = (select name from sys_dept where dept_id =
                #{entity.villageId})
            </if>
            <if test="entity.countyIds != null and entity.countyIds.size > 0">
                and area in ( select name from sys_dept where dept_id in
                <foreach item="item" index="index" collection="entity.countyIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="entity.townIds != null and entity.townIds.size > 0">
                and country in ( select name from sys_dept where dept_id in
                <foreach item="item" index="index" collection="entity.townIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="entity.villageIds != null and entity.villageIds.size > 0">
                and village in ( select name from sys_dept where dept_id in
                <foreach item="item" index="index" collection="entity.villageIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="entity.scale != null  and entity.scale != ''">and scale = #{entity.scale}</if>
            <if test="entity.managementLevel != null  and entity.managementLevel != ''">and management_level =
                #{entity.managementLevel}
            </if>
        </where>
    </select>

    <select id="selectStatistics" resultType="com.pig4cloud.pigx.gis.domain.EchartsProperty">
        select count(1) as value, ${statisticsField} as name
        from t_danger_points
        <where>
            <if test="countyId != null">and area = (select name from sys_dept where dept_id = #{countyId})</if>
            <if test="townId != null">and country = (select name from sys_dept where dept_id = #{townId})</if>
            <if test="villageId != null">and village = (select name from sys_dept where dept_id = #{villageId})</if>
            <if test="geom != null and geom != ''"> and ST_Contains(ST_GeomFromText(#{geom}, 4326), geom)</if>
            <if test="dangerPointsTypeList != null and dangerPointsTypeList.size > 0">
                and danger_points_type in <foreach item="item" index="index" collection="dangerPointsTypeList" open="(" separator="," close=")"> #{item} </foreach>
            </if>
            <if test="stabilityStatusList != null and stabilityStatusList.size > 0">
                and stability_status in
                <foreach item="item" index="index" collection="stabilityStatusList" open="(" separator="," close=")"> #{item} </foreach>
            </if>
        </where>
        group by ${statisticsField}
        order by value desc
    </select>

    <select id="selectGroupByDangerPointsType" resultType="java.lang.String">
        select danger_points_type as name
        from t_danger_points
        <where>
            <if test="countyId != null">and area = (select name from sys_dept where dept_id = #{countyId})</if>
            <if test="townId != null">and country = (select name from sys_dept where dept_id = #{townId})</if>
            <if test="villageId != null">and village = (select name from sys_dept where dept_id = #{villageId})</if>
        </where>
        group by danger_points_type
        order by danger_points_type desc
    </select>

    <select id="selectGroupByStabilityStatus" resultType="java.lang.String">
        select stability_status as name
        from t_danger_points
        <where>
            stability_status is not null and stability_status != ''
            <if test="countyId != null">and area = (select name from sys_dept where dept_id = #{countyId})</if>
            <if test="townId != null">and country = (select name from sys_dept where dept_id = #{townId})</if>
            <if test="villageId != null">and village = (select name from sys_dept where dept_id = #{villageId})</if>
        </where>
        group by stability_status
        order by stability_status desc
    </select>

    <select id="selectDangerWarningNum" resultType="java.lang.Integer">
        select count(1)
        from t_danger_points d
        inner join t_early_warning w
        on d.danger_points_number = w.provincial_id
        <where>
            w.has_been_deleted = '0'
            <if test="level == 1">
                and d.area = (select name from sys_dept where dept_id = #{quId})
            </if>
            <if test="level == 2">
                and d.area = (select name from sys_dept where dept_id = #{quId})
                and d.country = (select name from sys_dept where dept_id = #{townId})
            </if>
            <if test="level == 3">
                and d.area = (select name from sys_dept where dept_id = #{quId})
                and d.country = (select name from sys_dept where dept_id = #{townId})
                and d.village = (select name from sys_dept where dept_id = #{villageId})
            </if>
        </where>
    </select>

    <select id="selectHgDangerPointsById" parameterType="String" resultMap="DangerPointsResult">
        <include refid="selectHgDangerPointsVo"/>
        where id = #{id}
    </select>

    <select id="checkDangerPoint" parameterType="string" resultType="java.lang.Integer">
        select count(id)
        from t_danger_points
        where danger_points_number = #{dangerPointsNumber}
    </select>

    <select id="listAllAddresses" resultType="com.pig4cloud.pigx.gis.domain.HgAddresses">
        select
        city,
        area,
        country,
        village
        from
        hg_addresses
        <if test="quId != null">and area = (select name from sys_dept where dept_id = #{quId})</if>
        <if test="townId != null">and country = (select name from sys_dept where dept_id = #{townId})</if>
        <if test="villageId != null">and village = (select name from sys_dept where dept_id = #{villageId})</if>
    </select>


    <select id="selectDangerPointsTotal" resultType="java.lang.Integer">
        select count(1)
        from t_danger_points d
        <where>
            <if test="level == 1">
                and d.area = (select name from sys_dept where dept_id = #{quId})
            </if>
            <if test="level == 2">
                and d.area = (select name from sys_dept where dept_id = #{quId})
                and d.country = (select name from sys_dept where dept_id = #{townId})
            </if>
            <if test="level == 3">
                and d.area = (select name from sys_dept where dept_id = #{quId})
                and d.country = (select name from sys_dept where dept_id = #{townId})
                and d.village = (select name from sys_dept where dept_id = #{villageId})
            </if>
        </where>
    </select>

    <select id="seletAllDangerPoints" resultType="com.pig4cloud.pigx.gis.domain.DangerPoints">
        <include refid="selectHgDangerPointsVo"/>
        <where>
            <if test="level == 1">
                and area = (select name from sys_dept where dept_id = #{quId})
            </if>
            <if test="level == 2">
                and area = (select name from sys_dept where dept_id = #{quId})
                and country = (select name from sys_dept where dept_id = #{townId})
            </if>
            <if test="level == 3">
                and area = (select name from sys_dept where dept_id = #{quId})
                and country = (select name from sys_dept where dept_id = #{townId})
                and village = (select name from sys_dept where dept_id = #{villageId})
            </if>
        </where>
    </select>


    <select id="selectHasDangerWarningNum" resultType="java.lang.Integer">
        select count(DISTINCT w.provincial_id)
        from t_early_warning w
    </select>


    <select id="getCountyDangerPointCount" resultType="com.pig4cloud.pigx.gis.vo.DangerPointsCountVO">
        select tt.*, d.longitude, d.latitude
        from (SELECT area AS areaName, COUNT(*) AS count
              FROM public.t_danger_points
              where area IS NOT NULL AND area != ''
                <if test="geom != null and geom != ''"> and ST_Contains(ST_GeomFromText(#{geom}, 4326), geom)</if>
                <if test="dangerPointsTypeList != null and dangerPointsTypeList.size > 0">
                    and danger_points_type in <foreach item="item" index="index" collection="dangerPointsTypeList" open="(" separator="," close=")"> #{item} </foreach>
                </if>
                <if test="stabilityStatusList != null and stabilityStatusList.size > 0">
                    and stability_status in
                    <foreach item="item" index="index" collection="stabilityStatusList" open="(" separator="," close=")"> #{item} </foreach>
                </if>
              GROUP BY area) tt
                 left join sys_dept d on tt.areaName = d.name and d.place_level = '3'
        ORDER BY tt.count DESC
    </select>

    <select id="getTownDangerPointCount" resultType="com.pig4cloud.pigx.gis.vo.DangerPointsCountVO">
        select tt.*, d.longitude, d.latitude
        from (SELECT country AS areaName, COUNT(*) AS count
              FROM public.t_danger_points
              where country IS NOT NULL AND country != ''
                <if test="countyId != null"> and area = (select name from sys_dept where dept_id = #{countyId}) </if>
                <if test="geom != null and geom != ''"> and ST_Contains(ST_GeomFromText(#{geom}, 4326), geom)</if>
                <if test="dangerPointsTypeList != null and dangerPointsTypeList.size > 0">
                    and danger_points_type in <foreach item="item" index="index" collection="dangerPointsTypeList" open="(" separator="," close=")"> #{item} </foreach>
                </if>
                <if test="stabilityStatusList != null and stabilityStatusList.size > 0">
                    and stability_status in
                    <foreach item="item" index="index" collection="stabilityStatusList" open="(" separator="," close=")"> #{item} </foreach>
                </if>
              GROUP BY country) tt
                 left join sys_dept d on tt.areaName = d.name and d.place_level = '4'
        ORDER BY tt.count DESC
    </select>

    <select id="getVillageDangerPointCount" resultType="com.pig4cloud.pigx.gis.vo.DangerPointsCountVO">
        SELECT
        village AS areaName,
        COUNT(*) AS count
        FROM
        public.t_danger_points
        <where>
            village IS NOT NULL
            AND village != ''
            and area = (select name from sys_dept where dept_id = #{quId})
            and country = (select name from sys_dept where dept_id = #{townId})
        </where>
        GROUP BY
        village
        ORDER BY
        count DESC
    </select>

    <select id="getSingleVillageDangerPointCount" resultType="com.pig4cloud.pigx.gis.vo.DangerPointsCountVO">
        SELECT
        village AS areaName,
        COUNT(*) AS count
        FROM
        public.t_danger_points
        <where>
            village IS NOT NULL
            AND village != ''
            and area = (select name from sys_dept where dept_id = #{quId})
            and country = (select name from sys_dept where dept_id = #{townId})
            and village = (select name from sys_dept where dept_id = #{villageId})
        </where>
        GROUP BY
        village
        ORDER BY
        count DESC
    </select>
    <select id="selectTaskLocation" resultType="com.pig4cloud.pigx.gis.vo.TaskLocationVO">
        select id as dangerOrRiskPrimaryKeyId,
        name as name,
        danger_points_number as provinceId
        from t_danger_points
        <where>
            <if test="villageName != null and villageName != ''">
                and village = #{villageName}
            </if>
        </where>
        order by name
    </select>

    <select id="seletctByGeom" resultType="java.lang.String">
        select danger_points_number as pointId
        from t_danger_points
        where ST_Contains(ST_GeomFromText(#{geom}, 4326), geom)
    </select>
    <select id="selectMismatchByArea"
            resultType="com.pig4cloud.pigx.gis.dto.DangerPointsGridMembersMismatchDTO">
        select d.name as name,
        d.area as area,
        d.country as country,
        d.village as village
        from t_danger_points d
        <where>
            <if test="area != null and area != ''">
                and d.area = #{area}
            </if>
        </where>
    </select>
    <select id="getDangerPointsFile" resultType="com.pig4cloud.pigx.gis.vo.DangerPointsFileVO">
        select tdp.id,
        sf.id as pictureLocaleId,
        sf.file_name as pictureLocale,
        sfa.id as pictureDisasterPlanId,
        sfa.file_name as pictureDisasterPlan,
        sfb.id as pictureFlatId,
        sfb.file_name as pictureFlat,
        sfc.id as pictureSectionId,
        sfc.file_name as pictureSection
        from t_danger_points tdp
        left join sys_file sf on tdp.picture_locale = sf.id and sf.del_flag = '0'
        left join sys_file sfa on tdp.picture_disaster_plan = sfa.id and sfa.del_flag = '0'
        left join sys_file sfb on tdp.picture_flat = sfb.id and sfb.del_flag = '0'
        left join sys_file sfc on tdp.picture_section = sfc.id and sfc.del_flag = '0'
        <where>
            <if test="id != null and id != ''">
                and tdp.id = #{id}
            </if>
        </where>
    </select>


    <insert id="insertHgDangerPoints" parameterType="com.pig4cloud.pigx.gis.domain.DangerPoints">
        insert into t_danger_points
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="name != null">name,</if>
            <if test="dangerPointsType != null">danger_points_type,</if>
            <if test="gridNumber != null">grid_number,</if>
            <if test="dataBaseNumber != null">data_base_number,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="area != null">area,</if>
            <if test="country != null">country,</if>
            <if test="village != null">village,</if>
            <if test="smallPlace != null">small_place,</if>
            <if test="x != null">x,</if>
            <if test="y != null">y,</if>
            <if test="lon != null">lon,</if>
            <if test="lat != null">lat,</if>
            <if test="longValue != null">long,</if>
            <if test="wide != null">wide,</if>
            <if test="height != null">height,</if>
            <if test="measure != null">measure,</if>
            <if test="volume != null">volume,</if>
            <if test="scale != null">scale,</if>
            <if test="managementLevel != null">management_level,</if>
            <if test="threateningPopulation != null">threatening_population,</if>
            <if test="threateningProperty != null">threatening_property,</if>
            <if test="dangerLevel != null">danger_level,</if>
            <if test="secondaryDisasterTime != null">secondary_disaster_time,</if>
            <if test="addressEnvironmentalConditions != null">address_environmental_conditions,</if>
            <if test="deformationCharacteristicsActivityDuration != null">
                deformation_characteristics_activity_duration,
            </if>
            <if test="stabilityAnalysis != null">stability_analysis,</if>
            <if test="stabilityStatus != null">stability_status,</if>
            <if test="stabilityTrend != null">stability_trend,</if>
            <if test="causingFactors != null">causing_factors,</if>
            <if test="potentialHazards != null">potential_hazards,</if>
            <if test="predictionImpendingDisasterState != null">prediction_impending_disaster_state,</if>
            <if test="monitoringMethods != null">monitoring_methods,</if>
            <if test="monitor != null">monitor,</if>
            <if test="positionSupervisor != null">position_supervisor,</if>
            <if test="monitorTelephone != null">monitor_telephone,</if>
            <if test="textWarningSign != null">text_warning_sign,</if>
            <if test="personLiable != null">person_liable,</if>
            <if test="personLiableTelephone != null">person_liable_telephone,</if>
            <if test="administrators != null">administrators,</if>
            <if test="administratorsTelephone != null">administrators_telephone,</if>
            <if test="trafficAssistant != null">traffic_assistant,</if>
            <if test="trafficAssistantTelephone != null">traffic_assistant_telephone,</if>
            <if test="specialManager != null">special_manager,</if>
            <if test="specialManagerTelephone != null">special_manager_telephone,</if>
            <if test="dangerPointsNumber != null">danger_points_number,</if>
            <if test="geom != null">geom,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="dangerPointsType != null">#{dangerPointsType},</if>
            <if test="gridNumber != null">#{gridNumber},</if>
            <if test="dataBaseNumber != null">#{dataBaseNumber},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="area != null">#{area},</if>
            <if test="country != null">#{country},</if>
            <if test="village != null">#{village},</if>
            <if test="smallPlace != null">#{smallPlace},</if>
            <if test="x != null">#{x},</if>
            <if test="y != null">#{y},</if>
            <if test="lon != null">#{lon},</if>
            <if test="lat != null">#{lat},</if>
            <if test="longValue != null">#{longValue},</if>
            <if test="wide != null">#{wide},</if>
            <if test="height != null">#{height},</if>
            <if test="measure != null">#{measure},</if>
            <if test="volume != null">#{volume},</if>
            <if test="scale != null">#{scale},</if>
            <if test="managementLevel != null">#{managementLevel},</if>
            <if test="threateningPopulation != null">#{threateningPopulation},</if>
            <if test="threateningProperty != null">#{threateningProperty},</if>
            <if test="dangerLevel != null">#{dangerLevel},</if>
            <if test="secondaryDisasterTime != null">#{secondaryDisasterTime},</if>
            <if test="addressEnvironmentalConditions != null">#{addressEnvironmentalConditions},</if>
            <if test="deformationCharacteristicsActivityDuration != null">
                #{deformationCharacteristicsActivityDuration},
            </if>
            <if test="stabilityAnalysis != null">#{stabilityAnalysis},</if>
            <if test="stabilityStatus != null">#{stabilityStatus},</if>
            <if test="stabilityTrend != null">#{stabilityTrend},</if>
            <if test="causingFactors != null">#{causingFactors},</if>
            <if test="potentialHazards != null">#{potentialHazards},</if>
            <if test="predictionImpendingDisasterState != null">#{predictionImpendingDisasterState},</if>
            <if test="monitoringMethods != null">#{monitoringMethods},</if>
            <if test="monitor != null">#{monitor},</if>
            <if test="positionSupervisor != null">#{positionSupervisor},</if>
            <if test="monitorTelephone != null">#{monitorTelephone},</if>
            <if test="textWarningSign != null">#{textWarningSign},</if>
            <if test="personLiable != null">#{personLiable},</if>
            <if test="personLiableTelephone != null">#{personLiableTelephone},</if>
            <if test="administrators != null">#{administrators},</if>
            <if test="administratorsTelephone != null">#{administratorsTelephone},</if>
            <if test="trafficAssistant != null">#{trafficAssistant},</if>
            <if test="trafficAssistantTelephone != null">#{trafficAssistantTelephone},</if>
            <if test="specialManager != null">#{specialManager},</if>
            <if test="specialManagerTelephone != null">#{specialManagerTelephone},</if>
            <if test="dangerPointsNumber != null">#{dangerPointsNumber},</if>
            <if test="geom != null">#{geom},</if>
        </trim>
    </insert>

    <insert id="saveDangerPoint" parameterType="java.util.List">
        insert into
        t_danger_points
        (
        id,
        name,
        danger_points_type,
        grid_number,
        data_base_number,
        danger_points_number,
        province,
        city,
        area,
        country,
        village,
        small_place,
        x,
        y,
        lon,
        lat,
        long,
        wide,
        height,
        measure,
        volume,
        scale,
        management_level,
        threatening_population,
        threatening_property,
        danger_level,
        secondary_disaster_time,
        address_environmental_conditions,
        deformation_characteristics_activity_duration,
        stability_analysis,
        stability_status,
        stability_trend,
        causing_factors,
        potential_hazards,
        prediction_impending_disaster_state,
        monitoring_methods,
        monitor,
        position_supervisor,
        monitor_telephone,
        text_warning_sign,
        person_liable,
        person_liable_telephone,
        administrators,
        administrators_telephone,
        traffic_assistant,
        traffic_assistant_telephone,
        special_manager,
        special_manager_telephone,
        geom
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.name},
            #{item.danger_points_type},
            #{item.grid_number},
            #{item.data_base_number},
            #{item.danger_points_number},
            #{item.province},
            #{item.city},
            #{item.area},
            #{item.country},
            #{item.village},
            #{item.small_place},
            #{item.x},
            #{item.y},
            #{item.lon},
            #{item.lat},
            #{item.long},
            #{item.wide},
            #{item.height},
            #{item.measure},
            #{item.volume},
            #{item.scale},
            #{item.management_level},
            #{item.threatening_population},
            #{item.threatening_property},
            #{item.danger_level},
            #{item.secondary_disaster_time},
            #{item.address_environmental_conditions},
            #{item.deformation_characteristics_activity_duration},
            #{item.stability_analysis},
            #{item.stability_status},
            #{item.stability_trend},
            #{item.causing_factors},
            #{item.potential_hazards},
            #{item.prediction_impending_disaster_state},
            #{item.monitoring_methods},
            #{item.monitor},
            #{item.position_supervisor},
            #{item.monitor_telephone},
            #{item.text_warning_sign},
            #{item.person_liable},
            #{item.person_liable_telephone},
            #{item.administrators},
            #{item.administrators_telephone},
            #{item.traffic_assistant},
            #{item.traffic_assistant_telephone},
            #{item.special_manager},
            #{item.special_manager_telephone},
            ST_GeomFromText(concat('POINT (', #{item.lon}, ' ', #{item.lat}, ')'),4326)
            )
        </foreach>
    </insert>

    <update id="updateHgDangerPoints" parameterType="com.pig4cloud.pigx.gis.domain.DangerPoints">
        update t_danger_points
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="dangerPointsType != null">danger_points_type = #{dangerPointsType},</if>
            <if test="gridNumber != null">grid_number = #{gridNumber},</if>
            <if test="dataBaseNumber != null">data_base_number = #{dataBaseNumber},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="country != null">country = #{country},</if>
            <if test="village != null">village = #{village},</if>
            <if test="smallPlace != null">small_place = #{smallPlace},</if>
            <if test="x != null">x = #{x},</if>
            <if test="y != null">y = #{y},</if>
            <if test="lon != null">lon = #{lon},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="longValue != null">long = #{longValue},</if>
            <if test="wide != null">wide = #{wide},</if>
            <if test="height != null">height = #{height},</if>
            <if test="measure != null">measure = #{measure},</if>
            <if test="volume != null">volume = #{volume},</if>
            <if test="scale != null">scale = #{scale},</if>
            <if test="managementLevel != null">management_level = #{managementLevel},</if>
            <if test="threateningPopulation != null">threatening_population = #{threateningPopulation},</if>
            <if test="threateningProperty != null">threatening_property = #{threateningProperty},</if>
            <if test="dangerLevel != null">danger_level = #{dangerLevel},</if>
            <if test="secondaryDisasterTime != null">secondary_disaster_time = #{secondaryDisasterTime},</if>
            <if test="addressEnvironmentalConditions != null">address_environmental_conditions =
                #{addressEnvironmentalConditions},
            </if>
            <if test="deformationCharacteristicsActivityDuration != null">deformation_characteristics_activity_duration
                = #{deformationCharacteristicsActivityDuration},
            </if>
            <if test="stabilityAnalysis != null">stability_analysis = #{stabilityAnalysis},</if>
            <if test="stabilityStatus != null">stability_status = #{stabilityStatus},</if>
            <if test="stabilityTrend != null">stability_trend = #{stabilityTrend},</if>
            <if test="causingFactors != null">causing_factors = #{causingFactors},</if>
            <if test="potentialHazards != null">potential_hazards = #{potentialHazards},</if>
            <if test="predictionImpendingDisasterState != null">prediction_impending_disaster_state =
                #{predictionImpendingDisasterState},
            </if>
            <if test="verificationFlag != null">verification_flag = #{verificationFlag},</if>
            <if test="monitoringMethods != null">monitoring_methods = #{monitoringMethods},</if>
            <if test="monitor != null">monitor = #{monitor},</if>
            <if test="positionSupervisor != null">position_supervisor = #{positionSupervisor},</if>
            <if test="monitorTelephone != null">monitor_telephone = #{monitorTelephone},</if>
            <if test="textWarningSign != null">text_warning_sign = #{textWarningSign},</if>
            <if test="personLiable != null">person_liable = #{personLiable},</if>
            <if test="personLiableTelephone != null">person_liable_telephone = #{personLiableTelephone},</if>
            <if test="administrators != null">administrators = #{administrators},</if>
            <if test="administratorsTelephone != null">administrators_telephone = #{administratorsTelephone},</if>
            <if test="trafficAssistant != null">traffic_assistant = #{trafficAssistant},</if>
            <if test="trafficAssistantTelephone != null">traffic_assistant_telephone = #{trafficAssistantTelephone},
            </if>
            <if test="specialManager != null">special_manager = #{specialManager},</if>
            <if test="specialManagerTelephone != null">special_manager_telephone = #{specialManagerTelephone},</if>
            <if test="dangerPointsNumber != null">danger_points_number = #{dangerPointsNumber},</if>
            <if test="geom != null">geom = #{geom},</if>
            <if test="pictureLocale != null">picture_locale = #{pictureLocale},</if>
            <if test="pictureDisasterPlan != null">picture_disaster_plan = #{pictureDisasterPlan},</if>
            <if test="pictureFlat != null">picture_flat = #{pictureFlat},</if>
            <if test="pictureSection != null">picture_section = #{pictureSection},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateByCountryAndVillage">
        UPDATE t_danger_points
        SET person_liable               = #{personLiable},
            person_liable_telephone     = #{personLiableTelephone},
            administrators              = #{administrators},
            administrators_telephone    = #{administratorsTelephone},
            traffic_assistant           = #{trafficAssistant},
            traffic_assistant_telephone = #{trafficAssistantTelephone},
            special_manager             = #{specialManager},
            special_manager_telephone   = #{specialManagerTelephone}
        WHERE country = #{country}
          AND village = #{village}
    </update>


    <update id="updateDangerPointsPictureA">
        UPDATE t_danger_points
        SET picture_locale    = #{pictureLocale},
            picture_disaster_plan = #{pictureDisasterPlan}
        WHERE id = #{id}
    </update>

    <update id="updateDangerPointsPictureB">
        UPDATE t_danger_points
        SET picture_flat    = #{pictureFlat},
            picture_section = #{pictureSection}
        WHERE id = #{id}
    </update>


    <delete id="deleteHgDangerPointsById" parameterType="String">
        delete
        from t_danger_points
        where id = #{id}
    </delete>

    <delete id="deleteHgDangerPointsByIds" parameterType="String">
        delete from t_danger_points where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!--<select id="selectDangerPointsAndUser" resultType="com.daspatial.gis.domain.YsDangerPointsUserDTO">
        select zz.id, zz.name, zz.danger_points_number, zz.country, zz.village, bb.user_id, bb.user_name, bb.parent_id as town_id, bb.dept_id as village_id
        from ys_danger_points zz
        left join(
            select aa.parent_id, aa.dept_id, aa.dept_name, aa.parent_dept_name, uu.user_id, uu.user_name from
            (
                select sd.parent_id, sd.dept_id, sd.dept_name,
                    (select dept_name from sys_dept pp where pp.dept_id = sd.parent_id) as parent_dept_name
                from sys_dept sd
            ) aa
            inner join sys_user uu  on uu.dept_id = aa.dept_id
        ) bb on zz.country = bb.parent_dept_name and zz.village = bb.dept_name
        where zz.danger_points_number in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>-->
</mapper>