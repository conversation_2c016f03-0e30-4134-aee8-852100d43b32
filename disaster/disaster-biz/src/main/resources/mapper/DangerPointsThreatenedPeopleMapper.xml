<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.gis.mapper.DangerPointsThreatenedPeopleMapper">


	<select id="getList" resultType="com.pig4cloud.pigx.gis.domain.DangerPointsThreatenedPeople">
		select * from t_danger_points_threatened_people where danger_points_id = #{dangerPointsId}
		order by create_time desc
	</select>
</mapper>