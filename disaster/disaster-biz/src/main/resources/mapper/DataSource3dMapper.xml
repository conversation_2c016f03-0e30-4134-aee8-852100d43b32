<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.gis.mapper.DataSource3dMapper">

  <resultMap id="dataSource3dMap" type="com.pig4cloud.pigx.gis.entity.DataSource3dEntity">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="fileName" column="file_name"/>
        <result property="totalSize" column="total_size"/>
        <result property="parentFile" column="parent_file"/>
        <result property="url" column="url"/>
        <result property="timeTrem" column="time_trem"/>
        <result property="publicStatus" column="public_status"/>
        <result property="deptId" column="dept_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
</mapper>