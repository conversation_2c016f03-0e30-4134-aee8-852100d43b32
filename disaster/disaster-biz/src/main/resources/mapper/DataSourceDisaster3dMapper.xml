<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.gis.mapper.DataSourceDisaster3dMapper">

    <resultMap id="dataSourceDisaster3dMap" type="com.pig4cloud.pigx.gis.entity.DataSourceDisaster3dEntity">
        <id property="id" column="id"/>
        <result property="monitorDataType" column="monitor_data_type"/>
        <result property="provincialCode" column="provincial_code"/>
        <result property="name" column="name"/>
        <result property="fileName" column="file_name"/>
        <result property="totalSize" column="total_size"/>
        <result property="parentFile" column="parent_file"/>
        <result property="url" column="url"/>
        <result property="timeTrem" column="time_trem"/>
        <result property="publicStatus" column="public_status"/>
        <result property="deptId" column="dept_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


    <select id="getDataSourceDisaster3dPage" resultType="com.pig4cloud.pigx.gis.entity.DataSourceDisaster3dEntity">
        select dd.id as id,
        dd.monitor_data_type as monitorDataType,
        dd.provincial_code as provincialCode,
        dd.name as name,
        dd.file_name as fileName,
        dd.total_size as totalSize,
        dd.parent_file as parentFile,
        dd.url as url,
        dd.time_trem as timeTrem,
        dd.public_status as publicStatus,
        dd.dept_id as deptId,
        dd.status as status,
        dd.create_by as createBy,
        dd.create_time as createTime,
        dd.update_by as updateBy,
        dd.update_time as updateTime
        from t_data_source_disaster_3d dd
        inner join
        (select monitor_data_type, provincial_code, max(time_trem) as time_trem
        from t_data_source_disaster_3d where del_flag ='0'
        group by monitor_data_type, provincial_code) bb
        on bb.monitor_data_type = dd.monitor_data_type and bb.provincial_code = dd.provincial_code and
        bb.time_trem = dd.time_trem
        where 1=1 and dd.del_flag ='0'
        <if test="name != null and name != ''">
            and dd.name like concat('%',#{name},'%')
        </if>
        order by dd.time_trem


    </select>
    <select id="getDataSourceDisaster3dPageVersion"
            resultType="com.pig4cloud.pigx.gis.entity.DataSourceDisaster3dEntity">
        select * from t_data_source_disaster_3d dd
        <where>
             and dd.del_flag ='0'
            <if test="monitorDataType != null and monitorDataType != ''">
                and dd.monitor_data_type = #{monitorDataType}
            </if>
            <if test="provincialCode != null and provincialCode != ''">
                and dd.provincial_code = #{provincialCode}
            </if>
        </where>
        order by dd.time_trem desc

    </select>

</mapper>