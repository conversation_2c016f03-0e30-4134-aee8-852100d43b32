<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.gis.mapper.DeviceMapper">

  <resultMap id="deviceMap" type="com.pig4cloud.pigx.gis.entity.Device">
        <id property="id" column="id"/>
        <result property="geom" column="geom"/>
        <result property="deviceName" column="device_name"/>
        <result property="dangerPointsCode" column="danger_points_code"/>
        <result property="riskZoneCode" column="risk_zone_code"/>
<!--        <result property="provincialCode" column="provincial_code"/>
        <result property="monitorDataType" column="monitor_data_type"/>-->
        <result property="sensorType" column="sensor_type"/>
        <result property="syncFlag" column="sync_flag"/>
        <result property="city" column="city"/>
        <result property="county" column="county"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="monitorPointName" column="monitor_point_name"/>
        <result property="monitorPointCode" column="monitor_point_code"/>
        <result property="deviceStatus" column="device_status"/>
        <result property="clientId" column="client_id"/>
        <result property="deviceKey" column="device_key"/>
        <result property="deviceSn" column="device_sn"/>
        <result property="deviceModel" column="device_model"/>
        <result property="network" column="network"/>
        <result property="protocol" column="protocol"/>
        <result property="deviceType" column="device_type"/>
        <result property="deviceCompany" column="device_company"/>
        <result property="deptId" column="dept_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>

      <select id="selectHgDeviceList" resultType="com.pig4cloud.pigx.gis.vo.DeviceVO">
            SELECT
                  d.id, d.device_name, d.danger_points_code, d.risk_zone_code, d.sensor_type, d.sync_flag, d.city, d.county,
                  d.longitude, d.latitude, d.monitor_point_name, d.monitor_point_code, d.device_status, d.client_id, d.device_key,
                  d.device_sn, d.device_model, d.network, d.protocol, d.device_type, d.device_company, CONCAT(dp.name, '/', rz.fxqmc) AS disasterName
            FROM t_device d
            LEFT JOIN t_danger_points dp ON d.danger_points_code = dp.danger_points_number
            LEFT JOIN t_risk_zone rz ON d.risk_zone_code = rz.tybh
            <where>
                  d.del_flag = '0'
                  <if test="entity.geom != null and entity.geom != ''">and ST_Intersects(d.geom, ST_GeomFromText(#{entity.geom}, 4326 )) </if>
                  <if test="entity.level == 1 "> and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='3' and tt.name = (select name from sys_dept where dept_id = #{entity.quId}))) </if>
                  <if test="entity.level == 2 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='4' and tt.name = (select name from sys_dept where dept_id = #{entity.townId})))
                  </if>
                  <if test="entity.level == 3 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='5' and tt.pzwh = (select name from sys_dept where dept_id = #{entity.townId})
                        and tt.name = (select name from sys_dept where dept_id = #{entity.villageId})))
                  </if>
                  <if test="entity.sensorType != null  and entity.sensorType != ''">and d.sensor_type =
                        #{entity.sensorType}
                  </if>
                  <if test="entity.monitorDataType != null  and entity.monitorDataType != ''">and d.monitor_data_type =
                        #{entity.monitorDataType}
                  </if>
                  <if test="entity.deviceModel != null  and entity.deviceModel != ''">and d.device_model =
                        #{entity.deviceModel}
                  </if>
                  <if test="entity.deviceName != null  and entity.deviceName != ''">and d.device_name like concat('%',
                        #{entity.deviceName}, '%')
                  </if>
                  <if test="entity.deviceStatus != null  and entity.deviceStatus != ''">and d.device_status =
                        #{entity.deviceStatus}
                  </if>
                  <if test="entity.sensorTypeList != null and entity.sensorTypeList.size > 0">
                        and d.sensor_type in
                        <foreach item="item" index="index" collection="entity.sensorTypeList" open="(" separator="," close=")">
                              #{item}
                        </foreach>
                  </if>
            </where>
      </select>


      <select id="selectHgDevicesList" parameterType="com.pig4cloud.pigx.gis.domain.Devices" resultType="com.pig4cloud.pigx.gis.vo.DevicesVO">
            select d.id,
            ST_AsGeoJSON(d.geom) AS geom,
            d.device_no AS deviceNo,
            d.device_name AS deviceName,
            d.device_type AS deviceType,
            d.lon,
            d.lat,
            d.device_status AS deviceStatus,
            d.device_company AS deviceCompany,
            d.station_type_number AS stationTypeNumber,
            COALESCE(dp.name, rz.fxqmc) AS disasterName
            from (select * from t_devices where 1=1
            <if test="entity.geom != null and entity.geom != ''">and ST_Intersects(geom, ST_GeomFromText(#{entity.geom},
                  4326 ))
            </if>
            ) d
            LEFT JOIN t_danger_points dp
            ON d.danger_or_risk = 1 AND d.danger_or_risk_id = dp.danger_points_number
            LEFT JOIN t_risk_zone rz
            ON d.danger_or_risk = 2 AND d.danger_or_risk_id = rz.tybh
            <where>
                  <if test="entity.level == 1 "> and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='3' and tt.name = (select name from sys_dept where dept_id = #{entity.quId}))) </if>
                  <if test="entity.level == 2 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='4' and tt.name = (select name from sys_dept where dept_id = #{entity.townId})))
                  </if>
                  <if test="entity.level == 3 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='5' and tt.pzwh = (select name from sys_dept where dept_id = #{entity.townId})
                        and tt.name = (select name from sys_dept where dept_id = #{entity.villageId})))
                  </if>
                  <if test="entity.dangerOrRiskId != null  and entity.dangerOrRiskId != ''">and d.danger_or_risk_id =
                        #{entity.dangerOrRiskId}
                  </if>
                  <if test="entity.deviceType != null  and entity.deviceType != ''">and d.device_type =
                        #{entity.deviceType}
                  </if>
                  <if test="entity.deviceName != null  and entity.deviceName != ''">and d.device_name like concat('%',
                        #{entity.deviceName}, '%')
                  </if>
                  <if test="entity.deviceStatus != null  and entity.deviceStatus != ''">and d.device_status =
                        #{entity.deviceStatus}
                  </if>
                  <if test="entity.stationTypeNumber != null  and entity.stationTypeNumber != ''">and d.station_type_number =
                        #{entity.stationTypeNumber}
                  </if>
                  <if test="entity.deviceTypeList != null and entity.deviceTypeList.size > 0">
                        and d.device_type in
                        <foreach item="item" index="index" collection="entity.deviceTypeList" open="(" separator="," close=")">
                              #{item}
                        </foreach>
                  </if>
            </where>
            order by id asc
      </select>

      <select id="selectGroupByStationType" resultType="java.lang.String">
            select sensor_type as name
            from t_device d
            <where>
                  <if test="level == 1 "> and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='3' and tt.name = (select name from sys_dept where dept_id = #{quId}))) </if>
                  <if test="level == 2 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='4' and tt.name = (select name from sys_dept where dept_id = #{townId})))
                  </if>
                  <if test="level == 3 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='5' and tt.pzwh = (select name from sys_dept where dept_id = #{townId})
                        and tt.name = (select name from sys_dept where dept_id = #{villageId})))
                  </if>
            </where>
            group by name
            order by name desc
      </select>

      <select id="selectHgDevicesById" parameterType="Long" resultType="com.pig4cloud.pigx.gis.vo.DeviceVO">
            SELECT
                  d.id, ST_AsGeoJSON(d.geom) AS geom, d.device_name, d.danger_points_code, d.risk_zone_code, d.sensor_type, d.sync_flag, d.city, d.county,
                  d.longitude, d.latitude, d.monitor_point_name, d.monitor_point_code, d.device_status, d.client_id, d.device_key,
                  d.device_sn, d.device_model, d.network, d.protocol, d.device_type, d.device_company, CONCAT(dp.name, '/', rz.fxqmc) AS disasterName
            FROM t_device d
            LEFT JOIN t_danger_points dp ON d.danger_points_code = dp.danger_points_number
            LEFT JOIN t_risk_zone rz ON d.risk_zone_code = rz.tybh
            WHERE d.del_flag = '0' and d.id = #{id}
      </select>


      <select id="selectGroupByDangerOrRisk" resultType="com.pig4cloud.pigx.gis.vo.DevicesDangerOrRiskFenZuVO">
            select
            '1' as dangerOrRisk,
            COUNT(*) AS total
            from t_device d
            <where>
                  danger_points_code is not null
                  <if test="level == 1 "> and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='3' and tt.name = (select name from sys_dept where dept_id = #{quId}))) </if>
                  <if test="level == 2 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='4' and tt.name = (select name from sys_dept where dept_id = #{townId})))
                  </if>
                  <if test="level == 3 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='5' and tt.pzwh = (select name from sys_dept where dept_id = #{townId})
                        and tt.name = (select name from sys_dept where dept_id = #{villageId})))
                  </if>
            </where>
            UNION ALL
            select
            '2' as dangerOrRisk,
            COUNT(*) AS total
            from t_device d
            <where>
                  risk_zone_code is not null
                  <if test="level == 1 "> and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='3' and tt.name = (select name from sys_dept where dept_id = #{quId}))) </if>
                  <if test="level == 2 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='4' and tt.name = (select name from sys_dept where dept_id = #{townId})))
                  </if>
                  <if test="level == 3 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='5' and tt.pzwh = (select name from sys_dept where dept_id = #{townId})
                        and tt.name = (select name from sys_dept where dept_id = #{villageId})))
                  </if>
            </where>
      </select>

      <select id="selectGroupByCompany" resultType="com.pig4cloud.pigx.gis.vo.DevicesCompanyFenZuVO">
            SELECT
            device_company as deviceCompany,
            COUNT(CASE WHEN device_status = '在线' THEN 1 END) AS onlineStatusNo,
            COUNT(CASE WHEN device_status = '离线' THEN 1 END) AS notOnlineStatusNo
            FROM
            t_device d
            <where>
                  <if test="level == 1 "> and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='3' and tt.name = (select name from sys_dept where dept_id = #{quId}))) </if>
                  <if test="level == 2 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='4' and tt.name = (select name from sys_dept where dept_id = #{townId})))
                  </if>
                  <if test="level == 3 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='5' and tt.pzwh = (select name from sys_dept where dept_id = #{townId})
                        and tt.name = (select name from sys_dept where dept_id = #{villageId})))
                  </if>
            </where>
            GROUP BY
            device_company
            ORDER BY
            device_company
      </select>

      <select id="selectDeviceTotal" resultType="java.lang.Integer">
            select count(*) from t_device d
            <where>
                  <if test="level == 1 "> and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='3' and tt.name = (select name from sys_dept where dept_id = #{quId}))) </if>
                  <if test="level == 2 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='4' and tt.name = (select name from sys_dept where dept_id = #{townId})))
                  </if>
                  <if test="level == 3 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='5' and tt.pzwh = (select name from sys_dept where dept_id = #{townId})
                        and tt.name = (select name from sys_dept where dept_id = #{villageId})))
                  </if>
            </where>

      </select>
      <select id="selectOnlineDeviceTotal" resultType="java.lang.Integer">
            select count(*) from t_device d
            <where>
                  device_status = '在线'
                  <if test="level == 1 "> and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='3' and tt.name = (select name from sys_dept where dept_id = #{quId}))) </if>
                  <if test="level == 2 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='4' and tt.name = (select name from sys_dept where dept_id = #{townId})))
                  </if>
                  <if test="level == 3 ">
                        and ST_Intersects(ST_SetSRID(d.geom, 4326), (select geom from t_shp_town tt where tt.type='5' and tt.pzwh = (select name from sys_dept where dept_id = #{townId})
                        and tt.name = (select name from sys_dept where dept_id = #{villageId})))
                  </if>
            </where>
      </select>


      <select id="getDeviceTypeCount" resultType="com.pig4cloud.pigx.gis.domain.EchartsProperty">
            SELECT
            sensor_type AS name,
            COUNT(*) AS value
            FROM t_device
            <where>
                  sensor_type IS NOT NULL AND sensor_type  != ''
                  <!--<if test="monitorType != null ">-->
                        <choose>
                              <when test="monitorType == '1'.toString()"> AND danger_points_code IS NOT NULL AND danger_points_code  != ''</when>
                              <when test="monitorType == '2'.toString()"> AND risk_zone_code IS NOT NULL AND risk_zone_code  != ''</when>
                        </choose>
                <!--</if>-->
            </where>
            GROUP BY
            sensor_type
            ORDER BY
            value DESC
      </select>

      <select id="getTotalDeviceCount" resultType="java.lang.Integer">
            select count(*) from t_devices
            <where>
                  device_type IS NOT NULL
                  AND device_type  != ''
            </where>
      </select>

      <insert id="insertHgDevices" parameterType="com.pig4cloud.pigx.gis.domain.Devices" useGeneratedKeys="true" keyProperty="id">
            insert into t_devices
            <trim prefix="(" suffix=")" suffixOverrides=",">
                  <if test="dangerOrRisk != null">danger_or_risk,</if>
                  <if test="dangerOrRiskId != null and dangerOrRiskId != ''">danger_or_risk_id,</if>
                  <if test="geom != null">geom,</if>
                  <if test="lon != null">lon,</if>
                  <if test="lat != null">lat,</if>
                  <if test="deviceNo != null">device_no,</if>
                  <if test="deviceType != null">device_type,</if>
                  <if test="deviceName != null">device_name,</if>
                  <if test="deviceStatus != null">device_status,</if>
                  <if test="deviceCompany != null">device_company,</if>
                  <if test="stationTypeNumber != null">station_type_number,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                  <if test="dangerOrRisk != null">#{dangerOrRisk},</if>
                  <if test="dangerOrRiskId != null and dangerOrRiskId != ''">#{dangerOrRiskId},</if>
                  <if test="geom != null">#{geom},</if>
                  <if test="lon != null">#{lon},</if>
                  <if test="lat != null">#{lat},</if>
                  <if test="deviceNo != null">#{deviceNo},</if>
                  <if test="deviceType != null">#{deviceType},</if>
                  <if test="deviceName != null">#{deviceName},</if>
                  <if test="deviceStatus != null">#{deviceStatus},</if>
                  <if test="deviceCompany != null">#{deviceCompany},</if>
                  <if test="stationTypeNumber != null">#{stationTypeNumber},</if>
            </trim>
      </insert>

      <update id="updateHgDevices" parameterType="com.pig4cloud.pigx.gis.domain.Devices">
            update t_devices
            <trim prefix="SET" suffixOverrides=",">
                  <if test="dangerOrRisk != null">danger_or_risk = #{dangerOrRisk},</if>
                  <if test="dangerOrRiskId != null and dangerOrRiskId != ''">danger_or_risk_id = #{dangerOrRiskId},</if>
                  <if test="geom != null">geom = #{geom},</if>
                  <if test="lon != null">lon = #{lon},</if>
                  <if test="lat != null">lat = #{lat},</if>
                  <if test="deviceNo != null">device_no = #{deviceNo},</if>
                  <if test="deviceType != null">device_type = #{deviceType},</if>
                  <if test="deviceName != null">device_name = #{deviceName},</if>
                  <if test="deviceStatus != null">device_status = #{deviceStatus},</if>
                  <if test="deviceCompany != null">device_company = #{deviceCompany},</if>
                  <if test="stationTypeNumber != null">station_type_number = #{stationTypeNumber},</if>
            </trim>
            where id = #{id}
      </update>

      <delete id="deleteYsDevicesById" parameterType="Integer">
            delete from ys_devices where id = #{id}
      </delete>

      <delete id="deleteHgDevicesByIds" parameterType="String">
            delete from t_devices where id in
            <foreach item="id" collection="array" open="(" separator="," close=")">
                  #{id}
            </foreach>
      </delete>
</mapper>