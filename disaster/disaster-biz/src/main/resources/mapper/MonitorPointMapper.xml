<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.pig4cloud.pigx.gis.mapper.MonitorPointMapper">

  <resultMap id="monitorPointMap" type="com.pig4cloud.pigx.gis.entity.MonitorPoint">
        <id property="id" column="id"/>
        <result property="geom" column="geom"/>
        <result property="monitorPointCode" column="monitor_point_code"/>
        <result property="monitorPointName" column="monitor_point_name"/>
        <result property="disasterType" column="disaster_type"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="volume" column="volume"/>
        <result property="address" column="address"/>
        <result property="threateningPopulation" column="threatening_population"/>
        <result property="threateningProperty" column="threatening_property"/>
        <result property="scaleLevel" column="scale_level"/>
        <result property="dangerLevel" column="danger_level"/>
        <result property="deviceNumber" column="device_number"/>
        <result property="areaCode" column="area_code"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>
</mapper>