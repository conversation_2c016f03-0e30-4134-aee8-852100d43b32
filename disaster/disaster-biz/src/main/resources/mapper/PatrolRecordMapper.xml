<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.gis.mapper.PatrolRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.pig4cloud.pigx.gis.domain.PatrolRecord">
        <id column="id" property="id" />
        <result column="patrol_task_id" property="patrolTaskId" />
        <result column="name" property="name" />
        <result column="risk_point_num" property="riskPointNum" />
        <result column="threatened_people_num" property="threatenedPeopleNum" />
        <result column="evacuation_people_num" property="evacuationPeopleNum" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, patrol_task_id, name, risk_point_num, threatened_people_num, 
        evacuation_people_num, create_user, update_user, create_time, update_time
    </sql>

    <!-- 查询条件 -->
    <sql id="selectPatrolRecordVo">
        select <include refid="Base_Column_List" /> from patrol_record
    </sql>

    <!-- 动态查询条件 -->
    <sql id="whereCondition">
        <where>
            <if test="patrolRecordVO.patrolTaskId != null">
                AND patrol_task_id = #{patrolRecordVO.patrolTaskId}
            </if>
            <if test="patrolRecordVO.name != null and patrolRecordVO.name != ''">
                AND name LIKE CONCAT('%', #{patrolRecordVO.name}, '%')
            </if>
            <if test="patrolRecordVO.minRiskPointNum != null">
                AND risk_point_num >= #{patrolRecordVO.minRiskPointNum}
            </if>
            <if test="patrolRecordVO.maxRiskPointNum != null">
                AND risk_point_num &lt;= #{patrolRecordVO.maxRiskPointNum}
            </if>
            <if test="patrolRecordVO.minThreatenedPeopleNum != null">
                AND threatened_people_num >= #{patrolRecordVO.minThreatenedPeopleNum}
            </if>
            <if test="patrolRecordVO.maxThreatenedPeopleNum != null">
                AND threatened_people_num &lt;= #{patrolRecordVO.maxThreatenedPeopleNum}
            </if>
            <if test="patrolRecordVO.minEvacuationPeopleNum != null">
                AND evacuation_people_num >= #{patrolRecordVO.minEvacuationPeopleNum}
            </if>
            <if test="patrolRecordVO.maxEvacuationPeopleNum != null">
                AND evacuation_people_num &lt;= #{patrolRecordVO.maxEvacuationPeopleNum}
            </if>
            <if test="patrolRecordVO.createUser != null">
                AND create_user = #{patrolRecordVO.createUser}
            </if>
            <if test="patrolRecordVO.createTimeStart != null and patrolRecordVO.createTimeStart != ''">
                AND create_time >= #{patrolRecordVO.createTimeStart}::timestamp
            </if>
            <if test="patrolRecordVO.createTimeEnd != null  and patrolRecordVO.createTimeEnd != ''">
                AND create_time &lt;= #{patrolRecordVO.createTimeEnd}::timestamp
            </if>
            <if test="patrolRecordVO.updateTimeStart != null and patrolRecordVO.updateTimeStart != ''">
                AND update_time >= #{patrolRecordVO.updateTimeStart}::timestamp
            </if>
            <if test="patrolRecordVO.updateTimeEnd != null and patrolRecordVO.updateTimeEnd != ''">
                AND update_time &lt;= #{patrolRecordVO.updateTimeEnd}::timestamp
            </if>
        </where>
    </sql>

    <!-- 分页查询巡查记录 -->
    <select id="selectPatrolRecordPage" resultMap="BaseResultMap">
        <include refid="selectPatrolRecordVo"/>
        <include refid="whereCondition"/>
        ORDER BY create_time DESC
    </select>

    <!-- 查询巡查记录列表 -->
    <select id="selectPatrolRecordList" resultMap="BaseResultMap">
        <include refid="selectPatrolRecordVo"/>
        <include refid="whereCondition"/>
        ORDER BY create_time DESC
    </select>

    <!-- 根据巡查任务ID查询巡查记录 -->
    <select id="selectByPatrolTaskId" resultMap="BaseResultMap">
        <include refid="selectPatrolRecordVo"/>
        WHERE patrol_task_id = #{patrolTaskId}
        ORDER BY create_time DESC
    </select>

</mapper>
