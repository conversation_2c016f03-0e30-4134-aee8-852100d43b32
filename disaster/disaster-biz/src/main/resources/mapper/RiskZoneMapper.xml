<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.gis.mapper.RiskZoneMapper">

    <resultMap type="com.pig4cloud.pigx.gis.domain.RiskZone" id="RiskZoneResult">
        <result property="id"    column="id"    />
        <result property="geom"    column="geom"    />
        <result property="tybh"    column="tybh"    />
        <result property="fj"    column="fj"    />
        <result property="xplx"    column="xplx"    />
        <result property="fxqmc"    column="fxqmc"    />
        <result property="xz"    column="xz"    />
        <result property="xzc"    column="xzc"    />
        <result property="ywbh"    column="ywbh"    />
        <result property="mjm"    column="mjm"    />
        <result property="mjkm"    column="mjkm"    />
        <result property="fxdj"    column="fxdj"    />
        <result property="dzd"    column="dzd"    />
        <result property="szz"    column="szz"    />
        <result property="xdm"    column="xdm"    />
        <result property="xzd"    column="xzd"    />
        <result property="wxhs"    column="wxhs"    />
        <result property="wxrs"    column="wxrs"    />
        <result property="wxcc"    column="wxcc"    />
        <result property="zz"    column="zz"    />
        <result property="zzdh"    column="zzdh"    />
        <result property="gtsz"    column="gtsz"    />
        <result property="gtszdh"    column="gtszdh"    />
        <result property="jszc"    column="jszc"    />
        <result property="jszcdh"    column="jszcdh"    />
        <result property="csj"    column="csj"    />
        <result property="csjdh"    column="csjdh"    />
        <result property="xcy"    column="xcy"    />
        <result property="xcydh"    column="xcydh"    />
        <result property="jcy"    column="jcy"    />
        <result property="jcydh"    column="jcydh"    />
        <result property="ryqd"    column="ryqd"    />
        <result property="x"    column="x"    />
        <result property="y"    column="y"    />
        <result property="wd"    column="wd"    />
        <result property="jd"    column="jd"    />
        <result property="sd"    column="sd"    />
        <result property="yx"    column="yx"    />
        <result property="cz"    column="cz"    />
        <result property="pg"    column="pg"    />
        <result property="pd"    column="pd"    />
        <result property="pdgc"    column="pdgc"    />
        <result property="pdigc"    column="pdigc"    />
        <result property="spcd"    column="spcd"    />
        <result property="zdkd"    column="zdkd"    />
        <result property="px"    column="px"    />
        <result property="poxing"    column="poxing"    />
        <result property="gkdj"    column="gkdj"    />
    </resultMap>

    <sql id="selectRiskZoneVo">
        select id, ST_AsGeoJSON(ST_Force2D(geom)) AS geom, tybh, fj, xplx, fxqmc, xz, xzc, ywbh, mjm, mjkm, fxdj, dzd, szz, xdm, xzd, wxhs, wxrs, wxcc, zz, zzdh, gtsz, gtszdh, jszc, jszcdh, csj, csjdh, xcy, xcydh, jcy, jcydh, ryqd, x, y, wd, jd, sd, yx, cz, pg, pd, pdgc, pdigc, spcd, zdkd, px, poxing, gkdj,qx,picture_detail from t_risk_zone
    </sql>
    <update id="editPicture">
            update t_risk_zone set picture_detail = #{param.pictureDetail} where id = #{param.id}
    </update>

    <select id="selectDeptListByIds" resultType="com.pig4cloud.pigx.admin.api.entity.SysDept">
        select
            *
        from sys_dept
        where dept_id in
        <foreach collection="deptIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectRiskZonePage" parameterType="com.pig4cloud.pigx.gis.vo.RiskZoneVO" resultMap="RiskZoneResult">
        <include refid="selectRiskZoneVo"/>
        <where>
            <if test="entity.geom != null  and entity.geom != ''"> and ST_Intersects( geom, ST_GeomFromText(#{entity.geom}, 4326 )) </if>
            <if test="entity.tybh != null  and entity.tybh != ''"> and tybh = #{entity.tybh}</if>
            <if test="entity.fj != null  and entity.fj != ''"> and fj = #{entity.fj}</if>
            <if test="entity.fjList != null and entity.fjList.size > 0">
                and fj in
                <foreach item="item" index="index" collection="entity.fjList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="entity.xplx != null  and entity.xplx != ''"> and xplx = #{entity.xplx}</if>
            <if test="entity.fxqmc != null  and entity.fxqmc != ''"> and fxqmc like concat('%', #{entity.fxqmc},'%')</if>
            <if test="entity.xz != null  and entity.xz != ''"> and xz = like concat('%', #{entity.xz},'%')</if>
			<if test="entity.qxList != null and entity.qxList.size > 0">
				and qx in
				<foreach item="item" index="index" collection="entity.qxList" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
            <if test="entity.xzList != null and entity.xzList.size > 0">
                and xz in
                <foreach item="item" index="index" collection="entity.xzList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="entity.quId != null"> and qx = (select name from sys_dept where dept_id = #{entity.quId})</if>
            <if test="entity.townId != null"> and xz = (select name from sys_dept where dept_id = #{entity.townId})</if>
            <if test="entity.villageId != null"> and xzc = (select name from sys_dept where dept_id = #{entity.villageId})</if>
            <if test="entity.xzc != null  and entity.xzc != ''"> and xzc = like concat('%', #{entity.xzc},'%')</if>
            <if test="entity.xzcList != null and entity.xzcList.size > 0">
                and xzc in
                <foreach item="item" index="index" collection="entity.xzcList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="entity.ywbh != null  and entity.ywbh != ''"> and ywbh = #{entity.ywbh}</if>
            <if test="entity.fxdj != null  and entity.fxdj != ''"> and fxdj = #{entity.fxdj}</if>
            <if test="entity.dzd != null  and entity.dzd != ''"> and dzd = #{entity.dzd}</if>
        </where>
        order by id
    </select>

    <select id="selectStatistics" parameterType="com.pig4cloud.pigx.gis.vo.RiskZoneVO" resultType="com.pig4cloud.pigx.gis.domain.EchartsProperty">
        select count(1) as value, ${statisticsField} as name
        from t_risk_zone
        <where>
            <if test="geom != null  and geom != ''"> and ST_Intersects( geom, ST_GeomFromText(#{geom}, 4326 )) </if>
            <if test="tybh != null  and tybh != ''"> and tybh = #{tybh}</if>
            <if test="fj != null  and fj != ''"> and fj = #{fj}</if>
            <if test="fjList != null and fjList.size > 0">
                and fj in
                <foreach item="item" index="index" collection="fjList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="xplx != null  and xplx != ''"> and xplx = #{xplx}</if>
            <if test="fxqmc != null  and fxqmc != ''"> and fxqmc like concat('%', #{fxqmc},'%')</if>
            <if test="xz != null  and xz != ''">and xz = like concat('%', #{xz},'%')</if>
            <if test="xzList != null and xzList.size > 0">
                and xz in
                <foreach item="item" index="index" collection="xzList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="townId != null"> and xz = (select name from sys_dept where dept_id = #{townId})</if>
            <if test="villageId != null"> and xzc = (select name from sys_dept where dept_id = #{villageId})</if>
            <if test="xzc != null  and xzc != ''">and xzc = like concat('%', #{xzc},'%')</if>
            <if test="ywbh != null  and ywbh != ''">and ywbh = #{ywbh}</if>
            <if test="fxdj != null  and fxdj != ''">and fxdj = #{fxdj}</if>
            <if test="dzd != null  and dzd != ''">and dzd = #{dzd}</if>
        </where>
        group by ${statisticsField}
        order by value desc
    </select>

    <select id="selectGroupByFj" resultType="java.lang.String">
        select fj as name
        from t_risk_zone
        <where>
            <if test="quId != null"> and qx = (select name from sys_dept where dept_id = #{quId})</if>
            <if test="townId != null"> and xz = (select name from sys_dept where dept_id = #{townId})</if>
            <if test="villageId != null"> and xzc = (select name from sys_dept where dept_id = #{villageId})</if>
        </where>
        group by name
        order by name desc
    </select>

    <select id="selectGroupByXz" resultType="java.lang.String">
        select xz as name
        from t_risk_zone
        <where>
			<if test="quId != null"> and qx = (select name from sys_dept where dept_id = #{quId})</if>
            <if test="townId != null"> and xz = (select name from sys_dept where dept_id = #{townId})</if>
        </where>
        group by name
        order by name desc
    </select>

    <select id="selectGroupByXzc" parameterType="java.lang.String" resultType="java.lang.String">
        select xzc as name
        from t_risk_zone
        where xz = #{xz}
        <if test="level == 3">
            and xzc = (select name from sys_dept where dept_id = #{villageId})
        </if>
        group by name
        order by name desc
    </select>


	<select id="selectGroupByQx" resultType="java.lang.String">
		select qx as name
		from t_risk_zone
		<where>
			<if test="quId != null"> and qx = (select name from sys_dept where dept_id = #{quId})</if>
		</where>
		group by name
		order by name desc

	</select>


    <select id="getQxRiskZoneCount" resultType="com.pig4cloud.pigx.gis.vo.RiskZonesCountVO">
		SELECT
		qx AS areaName,
		COUNT(*) AS count
		FROM
		t_risk_zone
		<where>
			qx IS NOT NULL
			AND qx != ''
		</where>
		GROUP BY
		qx
		ORDER BY
		count DESC
	</select>


	<select id="getXzRiskZoneCount" resultType="com.pig4cloud.pigx.gis.vo.RiskZonesCountVO">
		SELECT
		xz AS areaName,
		COUNT(*) AS count
		FROM
		t_risk_zone
		<where>
			xz IS NOT NULL
			AND xz != ''
			and qx = (select name from sys_dept where dept_id = #{quId})
		</where>
		GROUP BY
		xz
		ORDER BY
		count DESC
	</select>



	<select id="getXzcRiskZoneCount" resultType="com.pig4cloud.pigx.gis.vo.RiskZonesCountVO">
		SELECT
		xzc AS areaName,
		COUNT(*) AS count
		FROM
		t_risk_zone
		<where>
			xzc IS NOT NULL
			AND xzc != ''
			and qx = (select name from sys_dept where dept_id = #{quId})
			and xz = (select name from sys_dept where dept_id = #{townId})
		</where>
		GROUP BY
		xzc
		ORDER BY
		count DESC
	</select>

	<select id="getSingleXzcRiskZoneCount" resultType="com.pig4cloud.pigx.gis.vo.RiskZonesCountVO">
		SELECT
		xzc AS areaName,
		COUNT(*) AS count
		FROM
		t_risk_zone
		<where>
			xzc IS NOT NULL
			AND xzc != ''
			and qx = (select name from sys_dept where dept_id = #{quId})
			and xz = (select name from sys_dept where dept_id = #{townId})
			and xzc = (select name from sys_dept where dept_id = #{villageId})
		</where>
		GROUP BY
		xzc
		ORDER BY
		count DESC
	</select>
    <select id="selectTaskLocation" resultType="com.pig4cloud.pigx.gis.vo.TaskLocationVO">
		select id AS dangerOrRiskPrimaryKeyId,
		fxqmc as name,
		tybh as provinceId
		FROM
		t_risk_zone
		<where>
			<if test="villageName != null and villageName != ''">
				and xzc = #{villageName}
			</if>
		</where>
		order by fxqmc
	</select>
    <select id="selectByTybh" resultType="com.pig4cloud.pigx.gis.domain.RiskZone">
		select * from t_risk_zone where tybh = #{tybh}
	</select>

    <select id="selectCountByFj" resultType="java.lang.Long">
        select count(*) from t_risk_zone
        <where>
            <if test="entity.quId != null"> and qx = (select name from sys_dept where dept_id = #{entity.quId})</if>
            <if test="entity.townId != null"> and xz = (select name from sys_dept where dept_id = #{entity.townId})</if>
            <if test="entity.villageId != null"> and xzc = (select name from sys_dept where dept_id = #{entity.villageId})</if>
        </where>
    </select>
    <select id="getInfo" resultType="com.pig4cloud.pigx.gis.vo.RiskZoneQrCodeVO">
            select trz.* , sf.file_name as fileName
            from t_risk_zone trz
                     left join sys_file sf on trz.picture_detail = sf.id
                     where trz.id = #{id}
    </select>

    <!-- <select id="selectRiskZoneAndUser" resultType="com.daspatial.gis.domain.YsRiskZoneUserDTO">
         select zz.id, zz.tybh, zz.fxqmc, zz.xz, zz.xzc, bb.user_id, bb.user_name, bb.parent_id as town_id, bb.dept_id as village_id
         from ys_risk_zone zz
         left join(
             select aa.parent_id, aa.dept_id, aa.dept_name, aa.parent_dept_name, uu.user_id, uu.user_name from
             (
                 select sd.parent_id, sd.dept_id, sd.dept_name,
                     (select dept_name from sys_dept pp where pp.dept_id = sd.parent_id) as parent_dept_name
                 from sys_dept sd
             ) aa
             inner join sys_user uu  on uu.dept_id = aa.dept_id
         ) bb on zz.xz = bb.parent_dept_name and zz.xzc = bb.dept_name
         where zz.tybh in
         <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
             #{item}
         </foreach>
     </select>

     <insert id="insertYsRiskZone" parameterType="com.daspatial.gis.domain.YsRiskZone" useGeneratedKeys="true" keyProperty="id">
         insert into ys_risk_zone
         <trim prefix="(" suffix=")" suffixOverrides=",">
             <if test="geom != null and geom != ''">geom,</if>
             <if test="tybh != null and tybh != ''">tybh,</if>
             <if test="fj != null and fj != ''">fj,</if>
             <if test="xplx != null">xplx,</if>
             <if test="fxqmc != null">fxqmc,</if>
             <if test="xz != null">xz,</if>
             <if test="xzc != null">xzc,</if>
             <if test="ywbh != null">ywbh,</if>
             <if test="mjm != null">mjm,</if>
             <if test="mjkm != null">mjkm,</if>
             <if test="fxdj != null">fxdj,</if>
             <if test="gkdj != null">gkdj,</if>
             <if test="dzd != null">dzd,</if>
             <if test="szz != null">szz,</if>
             <if test="xdm != null">xdm,</if>
             <if test="xzd != null">xzd,</if>
             <if test="wxhs != null">wxhs,</if>
             <if test="wxrs != null">wxrs,</if>
             <if test="wxcc != null">wxcc,</if>
             <if test="zz != null">zz,</if>
             <if test="zzdh != null">zzdh,</if>
             <if test="gtsz != null">gtsz,</if>
             <if test="gtszdh != null">gtszdh,</if>
             <if test="jszc != null">jszc,</if>
             <if test="jszcdh != null">jszcdh,</if>
             <if test="csj != null">csj,</if>
             <if test="csjdh != null">csjdh,</if>
             <if test="xcy != null">xcy,</if>
             <if test="xcydh != null">xcydh,</if>
             <if test="jcy != null">jcy,</if>
             <if test="jcydh != null">jcydh,</if>
             <if test="ryqd != null">ryqd,</if>
             <if test="x != null">x,</if>
             <if test="y != null">y,</if>
             <if test="wd != null">wd,</if>
             <if test="jd != null">jd,</if>
             <if test="sd != null">sd,</if>
             <if test="yx != null">yx,</if>
             <if test="cz != null">cz,</if>
             <if test="pg != null">pg,</if>
             <if test="pd != null">pd,</if>
             <if test="pdgc != null">pdgc,</if>
             <if test="pdigc != null">pdigc,</if>
             <if test="spcd != null">spcd,</if>
             <if test="zdkd != null">zdkd,</if>
             <if test="px != null">px,</if>
             <if test="poxing != null">poxing,</if>
          </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
             <if test="geom != null and geom != ''">ST_Force3DZ(ST_GeomFromText(#{geom}, 4326)),</if>
             <if test="tybh != null and tybh != ''">#{tybh},</if>
             <if test="fj != null and fj != ''">#{fj},</if>
             <if test="xplx != null and xplx != ''">#{xplx},</if>
             <if test="fxqmc != null">#{fxqmc},</if>
             <if test="xz != null">#{xz},</if>
             <if test="xzc != null">#{xzc},</if>
             <if test="ywbh != null">#{ywbh},</if>
             <if test="mjm != null">#{mjm},</if>
             <if test="mjkm != null">#{mjkm},</if>
             <if test="fxdj != null">#{fxdj},</if>
             <if test="gkdj != null">#{gkdj},</if>
             <if test="dzd != null">#{dzd},</if>
             <if test="szz != null">#{szz},</if>
             <if test="xdm != null">#{xdm},</if>
             <if test="xzd != null">#{xzd},</if>
             <if test="wxhs != null">#{wxhs},</if>
             <if test="wxrs != null">#{wxrs},</if>
             <if test="wxcc != null">#{wxcc},</if>
             <if test="zz != null">#{zz},</if>
             <if test="zzdh != null">#{zzdh},</if>
             <if test="gtsz != null">#{gtsz},</if>
             <if test="gtszdh != null">#{gtszdh},</if>
             <if test="jszc != null">#{jszc},</if>
             <if test="jszcdh != null">#{jszcdh},</if>
             <if test="csj != null">#{csj},</if>
             <if test="csjdh != null">#{csjdh},</if>
             <if test="xcy != null">#{xcy},</if>
             <if test="xcydh != null">#{xcydh},</if>
             <if test="jcy != null">#{jcy},</if>
             <if test="jcydh != null">#{jcydh},</if>
             <if test="ryqd != null">#{ryqd},</if>
             <if test="x != null">#{x},</if>
             <if test="y != null">#{y},</if>
             <if test="wd != null">#{wd},</if>
             <if test="jd != null">#{jd},</if>
             <if test="sd != null">#{sd},</if>
             <if test="yx != null">#{yx},</if>
             <if test="cz != null">#{cz},</if>
             <if test="pg != null">#{pg},</if>
             <if test="pd != null">#{pd},</if>
             <if test="pdgc != null">#{pdgc},</if>
             <if test="pdigc != null">#{pdigc},</if>
             <if test="spcd != null">#{spcd},</if>
             <if test="zdkd != null">#{zdkd},</if>
             <if test="px != null">#{px},</if>
             <if test="poxing != null">#{poxing},</if>
          </trim>
     </insert>

     <update id="updateYsRiskZoneByTybh" parameterType="com.daspatial.gis.domain.YsRiskZone">
         update ys_risk_zone
         <trim prefix="SET" suffixOverrides=",">
             <if test="geom != null and geom != ''">geom = ST_Force3DZ(ST_GeomFromText(#{geom}, 4326)),</if>
             <if test="fj != null ">fj = #{fj},</if>
             <if test="xplx != null ">xplx = #{xplx},</if>
             <if test="fxqmc != null">fxqmc = #{fxqmc},</if>
             <if test="xz != null">xz = #{xz},</if>
             <if test="xzc != null">xzc = #{xzc},</if>
             <if test="ywbh != null">ywbh = #{ywbh},</if>
             <if test="mjm != null">mjm = #{mjm},</if>
             <if test="mjkm != null">mjkm = #{mjkm},</if>
             <if test="fxdj != null">fxdj = #{fxdj},</if>
             <if test="gkdj != null">gkdj = #{gkdj},</if>
             <if test="dzd != null">dzd = #{dzd},</if>
             <if test="szz != null">szz = #{szz},</if>
             <if test="xdm != null">xdm = #{xdm},</if>
             <if test="xzd != null">xzd = #{xzd},</if>
             <if test="wxhs != null">wxhs = #{wxhs},</if>
             <if test="wxrs != null">wxrs = #{wxrs},</if>
             <if test="wxcc != null">wxcc = #{wxcc},</if>
             <if test="zz != null">zz = #{zz},</if>
             <if test="zzdh != null">zzdh = #{zzdh},</if>
             <if test="gtsz != null">gtsz = #{gtsz},</if>
             <if test="gtszdh != null">gtszdh = #{gtszdh},</if>
             <if test="jszc != null">jszc = #{jszc},</if>
             <if test="jszcdh != null">jszcdh = #{jszcdh},</if>
             <if test="csj != null">csj = #{csj},</if>
             <if test="csjdh != null">csjdh = #{csjdh},</if>
             <if test="xcy != null">xcy = #{xcy},</if>
             <if test="xcydh != null">xcydh = #{xcydh},</if>
             <if test="jcy != null">jcy = #{jcy},</if>
             <if test="jcydh != null">jcydh = #{jcydh},</if>
             <if test="ryqd != null">ryqd = #{ryqd},</if>
             <if test="x != null">x = #{x},</if>
             <if test="y != null">y = #{y},</if>
             <if test="wd != null">wd = #{wd},</if>
             <if test="jd != null">jd = #{jd},</if>
             <if test="sd != null">sd = #{sd},</if>
             <if test="yx != null">yx = #{yx},</if>
             <if test="cz != null">cz = #{cz},</if>
             <if test="pg != null">pg = #{pg},</if>
             <if test="pd != null">pd = #{pd},</if>
             <if test="pdgc != null">pdgc = #{pdgc},</if>
             <if test="pdigc != null">pdigc = #{pdigc},</if>
             <if test="spcd != null">spcd = #{spcd},</if>
             <if test="zdkd != null">zdkd = #{zdkd},</if>
             <if test="px != null">px = #{px},</if>
             <if test="poxing != null">poxing = #{poxing},</if>
         </trim>
         where tybh = #{tybh}
     </update>

     <delete id="deleteYsRiskZoneById" parameterType="Integer">
         delete from ys_risk_zone where id = #{id}
     </delete>

     <delete id="deleteYsRiskZoneByIds" parameterType="String">
         delete from ys_risk_zone where id in
         <foreach item="id" collection="array" open="(" separator="," close=")">
             #{id}
         </foreach>
     </delete>

     <select id="getIntersectList" resultMap="YsRiskZoneResult">
         <include refid="selectYsRiskZoneVo"/>
         WHERE ST_Intersects(geom, ST_GeomFromText(#{geomText}, 4326));
     </select>-->
</mapper>
