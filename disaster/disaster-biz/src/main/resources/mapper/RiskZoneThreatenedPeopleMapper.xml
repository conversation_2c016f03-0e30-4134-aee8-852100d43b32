<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pig4cloud.pigx.gis.mapper.RiskZoneThreatenedPeopleMapper">


	<select id="getList" resultType="com.pig4cloud.pigx.gis.domain.RiskZoneThreatenedPeople">
		select * from t_risk_zone_threatened_people where risk_zone_id = #{riskZoneId}
		order by create_time desc
	</select>
</mapper>