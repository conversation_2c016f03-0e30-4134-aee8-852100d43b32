-- ----------------------------
-- Table structure for patrol_record
-- ----------------------------
DROP TABLE IF EXISTS `patrol_record`;
CREATE TABLE `patrol_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `patrol_task_id` bigint DEFAULT NULL COMMENT '巡查任务ID',
  `name` varchar(255) DEFAULT NULL COMMENT '巡查名称',
  `risk_point_num` int DEFAULT NULL COMMENT '风险点数量',
  `threatened_people_num` int DEFAULT NULL COMMENT '威胁住户数量',
  `evacuation_people_num` int DEFAULT NULL COMMENT '撤离人员数量',
  `create_user` bigint DEFAULT NULL COMMENT '创建人ID',
  `update_user` bigint DEFAULT NULL COMMENT '修改人ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_patrol_task_id` (`patrol_task_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_name` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='巡查记录表';
