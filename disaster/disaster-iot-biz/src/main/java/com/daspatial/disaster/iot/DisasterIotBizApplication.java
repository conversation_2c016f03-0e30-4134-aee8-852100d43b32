package com.daspatial.disaster.iot;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class DisasterIotBizApplication {

    public static void main(String[] args) {
        SpringApplication.run(DisasterIotBizApplication.class, args);
        System.out.println("  disaster iot monitoring device data receiving program system successfully started.  \n");
    }

}
