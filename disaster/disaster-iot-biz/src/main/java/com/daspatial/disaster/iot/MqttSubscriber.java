package com.daspatial.disaster.iot;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttMessage;

import java.util.Arrays;

/**
 * 无人船水质检测
 *
 */
@Slf4j
public class MqttSubscriber {

    public static void main(String[] args) {
        // Mosquitto Broker默认端口
        String broker = "tcp://**************:1883";
        String topic = "$dp";
        String clientId = "das20250902";
        String username = "oZ1lF1qS6iW8tH8kH1iX1q";
        String password = "dE3gB3rQ1aJ9qO0rS5dY2s";

        try {
            MqttClient client = new MqttClient(broker, clientId);
            MqttConnectOptions connOpts = new MqttConnectOptions();
            connOpts.setUserName(username);
            connOpts.setPassword(password.toCharArray());
            connOpts.setCleanSession(true);
            connOpts.setConnectionTimeout(15);

            log.info("Connecting to broker: " + broker);
            client.connect(connOpts);
            log.info("Connected");

            client.subscribe(topic);
            log.info("Subscribed to topic: " + topic);

            client.setCallback(new MqttCallback() {
                @Override
                public void connectionLost(Throwable cause) {
                    log.info("Connection lost");
                }

                @Override
                public void messageArrived(String topic, MqttMessage message) {
                    if(message.getPayload().length > 3){
                        System.out.println(message.getPayload()[0]);
                        System.out.println(new String(Arrays.copyOfRange(message.getPayload(), 1, 3)));
                    }
                    int dataType = message.getPayload()[0];
                    byte highByte = message.getPayload()[1], lowByte = message.getPayload()[2];
                    int dataSize = ((highByte & 0xFF) << 8) | (lowByte & 0xFF);
                    System.out.println("dataType:" + dataType);
                    System.out.println("dataSize:" + dataSize);
                    log.info("Received message: " + new String(Arrays.copyOfRange(message.getPayload(), 3, message.getPayload().length)));
                }

                @Override
                public void deliveryComplete(org.eclipse.paho.client.mqttv3.IMqttDeliveryToken token) {
                    // Not used in this example
                }
            });

            // Keep the program running to receive messages
            while (true) {
                Thread.sleep(3000);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
