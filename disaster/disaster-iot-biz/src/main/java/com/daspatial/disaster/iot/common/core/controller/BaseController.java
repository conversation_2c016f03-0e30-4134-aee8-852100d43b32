package com.daspatial.disaster.iot.common.core.controller;

import cn.hutool.core.util.IdUtil;
import com.daspatial.disaster.iot.common.core.domain.AjaxResult;
import com.daspatial.disaster.iot.common.core.domain.R;
//import com.daspatial.disaster.iot.common.exception.base.BaseException;
import com.daspatial.disaster.iot.common.utils.DateUtils;
//import com.daspatial.disaster.iot.common.utils.PageUtils;
//import com.daspatial.disaster.iot.common.utils.SecurityUtils;
import com.daspatial.disaster.iot.common.utils.StringUtils;
//import com.github.pagehelper.PageHelper;
//import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.multipart.MultipartFile;

import java.beans.PropertyEditorSupport;
import java.io.File;
import java.io.IOException;
import java.util.Date;

/**
 * web层通用数据处理
 *
 * <AUTHOR>
 */
public class BaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 将前台传递过来的日期格式的字符串，自动转化为Date类型
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // Date 类型转换
        binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                setValue(DateUtils.parseDate(text));
            }
        });
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected R<Boolean> toR(int rows) {
        return rows > 0 ? R.ok(true) : R.ok(false);
    }

    /**
     * 响应返回结果
     *
     * @param flag 是否成功
     * @return 操作结果
     */
    protected R<Boolean> toR(boolean flag) {
        return flag ? R.ok(true) : R.ok(false);
    }

//    /**
//     * 上传文件路径赋值
//     */
//    public File getResourcePath(MultipartFile file) throws IOException {
//        if (file == null) {
//            throw new BaseException("文件不能为空");
//        }
//        String path = String.format("%s%s%s%s%s", GisAdminConfig.getResourcePath(), File.separator, IdUtil.simpleUUID(), File.separator, file.getOriginalFilename());
//        File filePath = new File(path);
//        if (!filePath.getParentFile().exists()) {
//            filePath.getParentFile().mkdirs();
//        }
//        file.transferTo(filePath);
//        return filePath;
//    }

    /**
     * 返回成功
     */
    public AjaxResult success() {
        return AjaxResult.success();
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error() {
        return AjaxResult.error();
    }

    /**
     * 返回成功消息
     */
    public AjaxResult success(String message) {
        return AjaxResult.success(message);
    }

    /**
     * 返回成功消息
     */
    public AjaxResult success(Object data) {
        return AjaxResult.success(data);
    }

    /**
     * 返回失败消息
     */
    public AjaxResult error(String message) {
        return AjaxResult.error(message);
    }

    /**
     * 返回警告消息
     */
    public AjaxResult warn(String message) {
        return AjaxResult.warn(message);
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    protected AjaxResult toAjax(int rows) {
        return rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    protected AjaxResult toAjax(boolean result) {
        return result ? success() : error();
    }

    /**
     * 页面跳转
     */
    public String redirect(String url) {
        return StringUtils.format("redirect:{}", url);
    }

//    /**
//     * 设置请求分页数据
//     */
//    protected void startPage() {
//        PageUtils.startPage();
//    }
//
//    /**
//     * 设置请求排序数据
//     */
//    protected void startOrderBy() {
//        PageDomain pageDomain = TableSupport.buildPageRequest();
//        if (StringUtils.isNotEmpty(pageDomain.getOrderBy())) {
//            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
//            PageHelper.orderBy(orderBy);
//        }
//    }
//
//    /**
//     * 清理分页的线程变量
//     */
//    protected void clearPage() {
//        PageUtils.clearPage();
//    }
//
//    /**
//     * 响应请求分页数据
//     */
//    protected TableDataInfo getDataTable(List<?> list) {
//        TableDataInfo rspData = new TableDataInfo();
//        rspData.setCode(HttpStatus.SUCCESS);
//        rspData.setMsg("查询成功");
//        rspData.setRows(list);
//        rspData.setTotal(new PageInfo(list).getTotal());
//        return rspData;
//    }
//
//    /**
//     * 获取用户缓存信息
//     */
//    public LoginUser getLoginUser() {
//        return SecurityUtils.getLoginUser();
//    }
//
//    /**
//     * 获取登录用户id
//     */
//    public Long getUserId() {
//        return getLoginUser().getUserId();
//    }
//
//    /**
//     * 获取登录部门id
//     */
//    public Long getDeptId() {
//        return getLoginUser().getDeptId();
//    }
//
//    /**
//     * 获取登录用户名
//     */
//    public String getUsername() {
//        return getLoginUser().getUsername();
//    }
}
