package com.daspatial.disaster.iot.common.core.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

import java.io.File;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FileTree {

    /**
     * 文件名
     */
    private String name;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 是否是文件夹
     */
    private Boolean isDirectory;

    public FileTree(File file) {
        String filePath = file.getPath().replace("\\", "");
        String name = file.getName();
        name = StringUtils.isEmpty(name) ? filePath : name;


        this.name = name;
        this.isDirectory = !file.isFile();
        this.path = file.getPath();
    }
}
