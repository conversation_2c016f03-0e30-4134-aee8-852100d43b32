package com.daspatial.disaster.iot.common.enums;

import lombok.Getter;

/**
 * 上传的数据格式类型
 *
 * <AUTHOR>
 */
@Getter
public enum DataPointsType {

    ONE(1, "监测数据实时上传"),         //类型一:适用于单个或多个监测类型的监测数据实时上传
    TWO(2, "监测类型的历史数据上传"),      //类型二:适用于单个或多个监测类型的历史数据上传
    THREE(3, "二进制文件数据上传");          //类型三:适用于文件类型的数据上传

    private final Integer code;

    private final String info;

    DataPointsType(Integer code, String info) {
        this.code = code;
        this.info = info;
    }

}
