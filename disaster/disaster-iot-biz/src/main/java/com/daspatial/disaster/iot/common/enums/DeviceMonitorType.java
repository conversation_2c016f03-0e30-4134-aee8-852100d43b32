package com.daspatial.disaster.iot.common.enums;

import lombok.Getter;

/**
 * 监测类型
 *
 * <AUTHOR>
 */
@Getter
public enum DeviceMonitorType {

    L1("L1", "变形监测"),
    L2("L2", "物理场监测"),
    L3("L3", "影响因素监测"),
    L4("L4", "宏观现象监测"),

    L1_LF("L1_LF", "变形监测-裂缝"),
    L1_GP("L1_GP", "变形监测-地表位移"),
    L1_SW("L1_SW", "变形监测-深部位移"),
    L1_JS("L1_JS", "变形监测-加速度"),
    L1_QJ("L1_QJ", "变形监测-倾角"),
    L1_ZD("L1_ZD", "变形监测-振动"),

    L2_LF("L2_LF", "物理场监测-应力"),
    L2_TY("L2_TY", "物理场监测-土压力"),
    L2_CS("L2_CS", "物理场监测-次声"),
    L2_DS("L2_DS", "物理场监测-地声"),

    L3_YL("L3_YL", "影响因素监测-雨量"),
    L3_QW("L3_QW", "影响因素监测-气温"),
    L3_TW("L3_TW", "影响因素监测-土壤温度"),
    L3_HS("L3_HS", "影响因素监测-土壤含水率"),
    L3_DB("L3_DB", "影响因素监测-地表水"),
    L3_DX("L3_DX", "影响因素监测-地下水"),
    L3_SY("L3_SY", "影响因素监测-孔隙水压力"),
    L3_ST("L3_ST", "影响因素监测-渗透压力"),
    L3_LS("L3_LS", "影响因素监测-流速"),
    L3_CJ("L3_CJ", "影响因素监测-沉降"),
    L3_QY("L3_QY", "影响因素监测-气压"),

    L4_SP("L4_SP", "影响因素监测-视频"),
    L4_NW("L4_NW", "影响因素监测-泥(水)位"),
    L4_LD("L4_LD", "影响因素监测-雷达"),
    L4_LB("L4_LB", "影响因素监测-预警喇叭"),

    S1_ZT_1("S1_ZT_1", "监测设备-状态数据"),
    S1_NE_1("S1_NE_1", ""),
    S1_MV_1("S1_MV_1","");

    private final String code;

    private final String info;

    DeviceMonitorType(String code, String info) {
        this.code = code;
        this.info = info;
    }

}
