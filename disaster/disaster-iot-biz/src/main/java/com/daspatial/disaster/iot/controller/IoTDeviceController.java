package com.daspatial.disaster.iot.controller;

import com.daspatial.disaster.iot.common.core.controller.BaseController;
import com.daspatial.disaster.iot.common.core.domain.R;
import com.daspatial.disaster.iot.common.enums.DataPointsType;
import com.daspatial.disaster.iot.domain.dto.*;
import com.daspatial.disaster.iot.mapper.DeviceMapper;
import com.daspatial.disaster.iot.service.IIoTDeviceService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 接收监测数据上传服务接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Slf4j
@RestController
@RequestMapping("/v2/devices")
@RequiredArgsConstructor
public class IoTDeviceController extends BaseController {

    private final IIoTDeviceService deviceService;
    private final DeviceMapper deviceMapper;

    /**
     * 监测数据上报<br><br/>
     * 监测数据上报，type=(1~2数据格式类型)，<br><br/>
     * 二进制文件数据上报，type=3&deviceId=(设备id)，适用于GNSS原始数据、文件以及图像等数据的上传
     *
     * @return
     */
    @PostMapping("/datapoints")
    public R<Boolean> dataPoints(@RequestParam Integer type, @RequestParam(required = false) String deviceId, @Valid @RequestBody DeviceRealtimeDataDTO dto) {
        log.info("http v2 message === type: {}, deviceId: {}, message : {}", type, deviceId, dto);
//        log.info("http v2 message === type:{}, deviceId:{}, deviceSn:{}, body:{}", type, deviceId, deviceSn, jsonStr);

        if (type == null || type < DataPointsType.ONE.getCode() || type > DataPointsType.THREE.getCode()) {
            return R.fail("type参数错误，请检查");
        }
        // type等于1或2时候，校验参数是否正确
        if (DataPointsType.ONE.getCode().equals(type) || DataPointsType.TWO.getCode().equals(type)) {
            long count = deviceMapper.selectCountByClientIdAndDeviceKey(dto.getDeviceId(), dto.getApikey());
            if (count == 0) {
                return R.fail("校验错误，请检查");
            }
        }
        boolean flag = deviceService.dataPoints(type, deviceId, dto);
        return toR(true);
    }

}
