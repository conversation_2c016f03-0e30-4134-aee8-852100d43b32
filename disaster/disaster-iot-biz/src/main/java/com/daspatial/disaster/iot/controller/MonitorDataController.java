package com.daspatial.disaster.iot.controller;

import com.daspatial.disaster.iot.common.core.controller.BaseController;
import com.daspatial.disaster.iot.common.core.domain.R;
import com.daspatial.disaster.iot.common.utils.DateUtils;
import com.daspatial.disaster.iot.domain.*;
import com.daspatial.disaster.iot.domain.dto.*;
import com.daspatial.disaster.iot.mapper.DeviceMapper;
import com.daspatial.disaster.iot.service.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 接收监测数据上传服务接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Slf4j
@RestController
@RequestMapping("/v1/monitorData")
@RequiredArgsConstructor
public class MonitorDataController extends BaseController {

    private final IYlService ylService;
    private final ILfwyService lfwyService;
    private final ITrhslService trhslService;
    private final IQjService qjService;
    private final IGnssService gnssService;
    private final IJsdService jsdService;
    private final IZdService zdService;
    private final ObjectMapper mapper = new ObjectMapper();

    /**
     * 上传雨量数据
     *
     * @return
     */
    @PostMapping("/AddRainRecord")
    public R<Boolean> addRainRecord(@Valid @RequestBody RainDTO dto) {
        try {
            log.info("入参：{}", mapper.writeValueAsString(dto));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        Yl entity = new Yl();
        entity.setStation(dto.getSensorCode());
        entity.setValue(Double.parseDouble(dto.getValue()));
        entity.setClttm(DateUtils.dateToLocalDateTime(dto.getDataTime()));
        return toR(ylService.insertYl(entity));
    }

    /**
     * 上传裂缝数据
     *
     * @return
     */
    @PostMapping("/AddCreviceRecord")
    public R<Boolean> addCreviceRecord(@Valid @RequestBody CreviceDTO dto) {
        try {
            log.info("入参：{}", mapper.writeValueAsString(dto));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        Lfwy entity = new Lfwy();
        entity.setStation(dto.getSensorCode());
        entity.setValue(Double.parseDouble(dto.getValue()));
        entity.setClttm(DateUtils.dateToLocalDateTime(dto.getDataTime()));
        return toR(lfwyService.insertLfwy(entity));
    }


    /**
     * 上传土壤含水率数据
     *
     * @return
     */
    @PostMapping("/AddMoistureRecord")
    public R<Boolean> addMoistureRecord(@Valid @RequestBody MoistureDTO dto) {
        try {
            log.info("入参：{}", mapper.writeValueAsString(dto));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        Trhsl entity = new Trhsl();
        entity.setStation(dto.getSensorCode());
        entity.setValue(Double.parseDouble(dto.getValue()));
        entity.setClttm(DateUtils.dateToLocalDateTime(dto.getDataTime()));
        return toR(trhslService.insertTrhsl(entity));
    }

    /**
     * 上传倾角数据
     *
     * @return
     */
    @PostMapping("/AddInclinatorRecord")
    public R<Boolean> addInclinatorRecord(@Valid @RequestBody InclinatorDTO dto) {
        try {
            log.info("入参：{}", mapper.writeValueAsString(dto));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        Qj entity = new Qj();
        entity.setStation(dto.getSensorCode());
        entity.setX(Double.parseDouble(dto.getValueX()));
        entity.setY(Double.parseDouble(dto.getValueY()));
        entity.setZ(Double.parseDouble(dto.getValueZ()));
        entity.setAngle(Double.parseDouble(dto.getDirection()));
        entity.setTrend(Double.parseDouble(dto.getIncline()));
        entity.setClttm(DateUtils.dateToLocalDateTime(dto.getDataTime()));
        return toR(qjService.insertQj(entity));
    }

    /**
     * 上传GNSS位移数据
     *
     * @return
     */
    @PostMapping("/AddGNSSRecord")
    public R<Boolean> addGNSSRecord(@Valid @RequestBody GnssDTO dto) {
        try {
            log.info("入参：{}", mapper.writeValueAsString(dto));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        Gnss entity = new Gnss();
        entity.setStation(dto.getSensorCode());
        entity.setGpstotalx(Double.parseDouble(dto.getValueX()));
        entity.setGpstotaly(Double.parseDouble(dto.getValueY()));
        entity.setGpstotalz(Double.parseDouble(dto.getValueZ()));
        entity.setClttm(DateUtils.dateToLocalDateTime(dto.getDataTime()));
        return toR(gnssService.insertGnss(entity));
    }

    /**
     * 上传加速度数据
     *
     * @return
     */
    @PostMapping("/AddAccelerationRecord")
    public R<Boolean> AddAccelerationRecord(@Valid @RequestBody AccelerationDTO dto) {
        try {
            log.info("入参：{}", mapper.writeValueAsString(dto));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        Jsd entity = new Jsd();
        entity.setStation(dto.getSensorCode());
        entity.setGx(Double.parseDouble(dto.getGX()));
        entity.setGy(Double.parseDouble(dto.getGY()));
        entity.setGz(Double.parseDouble(dto.getGZ()));
        entity.setClttm(DateUtils.dateToLocalDateTime(dto.getDataTime()));
        return toR(jsdService.insertJsd(entity));
    }

    /**
     * 上传振动数据
     *
     * @return
     */
    @PostMapping("/AddVibrationRecord")
    public R<Boolean> addVibrationRecord(@Valid @RequestBody VibrationDTO dto) {
        try {
            log.info("入参：{}", mapper.writeValueAsString(dto));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        Zd entity = new Zd();
        entity.setStation(dto.getSensorCode());
        entity.setPlx(Double.parseDouble(dto.getPLX()));
        entity.setPly(Double.parseDouble(dto.getPLY()));
        entity.setPlz(Double.parseDouble(dto.getPLZ()));
        entity.setValue(Double.parseDouble(dto.getValue()));
        entity.setSjx(Double.parseDouble(dto.getSJX()));
        entity.setSjy(Double.parseDouble(dto.getSJY()));
        entity.setSjz(Double.parseDouble(dto.getSJZ()));
        entity.setSjvalue(Double.parseDouble(dto.getSJValue()));
        entity.setClttm(DateUtils.dateToLocalDateTime(dto.getDataTime()));
        return toR(zdService.insertZd(entity));
    }


}
