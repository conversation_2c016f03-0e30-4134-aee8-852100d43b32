package com.daspatial.disaster.iot.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 监测设备
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Data
@TableName("t_device")
@EqualsAndHashCode(callSuper = true)
public class Device extends Model<Device> {


    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 空间字段
     */
    private String geom;

    /**
     * 监测设备名称
     */
    private String deviceName;

    /**
     * 省级平台统一编号
     */
    private String provincialCode;

    /**
     * 监测类型，1隐患点，2风险区
     */
    private String monitorDataType;

    /**
     * 传感器类型
     */
    private String sensorType;

    /**
     * 是否同步到部平台
     */
    private Boolean syncFlag;

    /**
     * 市
     */
    private String city;

    /**
     * 区县
     */
    private String county;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 监测点名称
     */
    private String monitorPointName;

    /**
     * 监测点统一编号
     */
    private String monitorPointCode;

    /**
     * 设备状态
     */
    private String deviceStatus;

    /**
     * 设备ID(clientID)
     */
    private String clientId;

    /**
     * 设备key
     */
    private String deviceKey;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 通讯方式，0: GPRS/3G/4G  1: NB-Iot
     */
    private String network;

    /**
     * 接入协议，0: MQTT  1: HTTP  2: COAP
     */
    private String protocol;

    /**
     * 设备类型，0: 单参数  1: 多参数  2: 本地组网
     */
    private String deviceType;

    /**
     * 设备厂商
     */
    private String deviceCompany;

    /**
     * 所属部门ID
     */
    private Long deptId;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记，0未删除，1已删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private String delFlag;

}