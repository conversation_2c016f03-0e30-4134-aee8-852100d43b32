package com.daspatial.disaster.iot.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 地下水数据对象 tbl_jc_dxs
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@TableName("tbl_jc_dxs")
@EqualsAndHashCode(callSuper = true)
public class Dxs extends Model<Dxs> {

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     *
     */
    private String area;

    /**
     * 站点编号
     */
    private String station;

    /**
     * 传感器编码
     */
    private String sensor;

    /**
     * 监测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime clttm;

    /**
     * 所监测层位的地下水的温度，单位：℃（摄氏度）
     */
    private Double temp;

    /**
     * 所监测层位的稳定地下水面相对于基准面的高程，单位：m（米）
     */
    private Double value;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime systm;

}
