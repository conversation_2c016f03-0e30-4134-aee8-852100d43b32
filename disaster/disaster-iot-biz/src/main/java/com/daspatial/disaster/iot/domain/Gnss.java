package com.daspatial.disaster.iot.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 地位移,GNSS数据对象 tbl_jc_gnss
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@TableName("tbl_jc_gnss")
@EqualsAndHashCode(callSuper = true)
public class Gnss extends Model<Gnss> {

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private String area;

    /**
     * 站点编号
     */
    private String station;

    /**
     * 传感器编号
     */
    private String sensor;

    /**
     * 监测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime clttm;

    /**
     * GNSS原始数据（RTCM3.X格式原始观测数据与星历数据）
     */
    private String gpsinitial;

    /**
     * 与测站初始位置差值，X方向位移，单位：mm(毫米)
     */
    private Double gpstotalx;

    /**
     * 与测站初始位置差值，Y方向位移，单位：mm(毫米)
     */
    private Double gpstotaly;

    /**
     * 与GNSS监测点初始位置差值，X、Y、Z向位移，mm(毫米)
     */
    private Double gpstotalz;

    private LocalDateTime systm;

}
