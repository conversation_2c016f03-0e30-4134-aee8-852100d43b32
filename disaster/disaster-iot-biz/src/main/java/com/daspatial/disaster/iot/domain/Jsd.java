package com.daspatial.disaster.iot.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 加速度数据对象 tbl_jc_jsd
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@TableName("tbl_jc_jsd")
@EqualsAndHashCode(callSuper = true)
public class Jsd extends Model<Jsd> {

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 行政区划编码
     */
    private String area;

    /**
     * 站点编码
     */
    private String station;

    /**
     * 传感器编码
     */
    private String sensor;

    /**
     * 监测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime clttm;

    /**
     * 一个采样周期内 X 轴方向加速度的最大变化量，单位：mg（加速度）
     */
    private Double gx;

    /**
     * 一个采样周期内 Y 轴方向加速度的最大变化量，单位：mg（加速度）
     */
    private Double gy;

    /**
     * 一个采样周期内 Z 轴方向加速度的最大变化量，单位：mg（加速度）
     */
    private Double gz;


    private LocalDateTime systm;


}
