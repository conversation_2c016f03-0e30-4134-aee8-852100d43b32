package com.daspatial.disaster.iot.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 雷达数据对象 tbl_jc_ld
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@TableName("tbl_jc_ld")
@EqualsAndHashCode(callSuper = true)
public class Ld extends Model<Ld> {

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;


    private String area;

    /**
     * 站点编号
     */
    private String station;

    /**
     * 传感器编码
     */
    private String sensor;

    /**
     * 监测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime clttm;

    /**
     * 以雷达为原点，监测物体在 X轴方向坐标，单位：m（米）
     */
    private Double x;

    /**
     * 以雷达为原点，监测物体在 Y轴方向坐标，单位：m（米）
     */
    private Double y;

    /**
     * 以雷达为原点，监测物体在 Z轴方向坐标，单位：m（米）
     */
    private Double z;

    /**
     * 监测物体移动速度，单位：m/s（米/秒）
     */
    private Double speed;

    private LocalDateTime systm;


}
