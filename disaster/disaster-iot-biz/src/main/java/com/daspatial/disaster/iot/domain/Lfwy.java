package com.daspatial.disaster.iot.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 裂缝数据对象 tbl_jc_lfwy
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@TableName("tbl_jc_lfwy")
@EqualsAndHashCode(callSuper = true)
public class Lfwy extends Model<Lfwy> {

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 行政区划代码
     */
    private String area;

    /**
     * 站点编号
     */
    private String station;

    /**
     * 传感器编码
     */
    private String sensor;

    /**
     * 监测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime clttm;

    /**
     * 裂缝张开度，表明位移随时间的累计变化量值，单位：mm（毫米）
     */
    private Double value;

    private LocalDateTime systm;

}
