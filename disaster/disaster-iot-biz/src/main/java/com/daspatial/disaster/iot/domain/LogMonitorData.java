package com.daspatial.disaster.iot.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 监测设备实时数据记录对象 tbl_log_monitor_data
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@TableName("tbl_log_monitor_data")
@EqualsAndHashCode(callSuper = true)
public class LogMonitorData extends Model<LogMonitorData> {

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 省平台生成的设备ID(clientID)
     */
    private String deviceId;

    /**
     * 站点编号
     */
    private String station;

    /**
     * 监测数据
     */
    private String data;

    /**
     * 入库时间
     */
    private LocalDateTime systm;

}
