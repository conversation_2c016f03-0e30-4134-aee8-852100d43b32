package com.daspatial.disaster.iot.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 倾角数据对象 tbl_jc_qj
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@TableName("tbl_jc_qj")
@EqualsAndHashCode(callSuper = true)
public class Qj extends Model<Qj> {

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private String area;

    /**
     * 站点编号
     */
    private String station;

    /**
     * 传感器编码
     */
    private String sensor;

    /**
     * 监测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime clttm;

    /**
     * X 轴与水平面的夹角，上传绝对角度值。范围为-90°～90°
     */
    private Double x;

    /**
     * Y 轴与水平面的夹角，上传绝对角度值。范围为-90°～90°
     */
    private Double y;

    /**
     * Z 轴与水平面的夹角，上传绝对角度值。范围为-90°～90°
     */
    private Double z;

    /**
     * XY 轴所形成的平面与水平面的夹角。范围为-90°～90°
     */
    private Double angle;

    /**
     * 方位角：X 轴在水平面的投影与磁北的夹角。范围为 0°～360°
     */
    private Double trend;


    private LocalDateTime systm;


}
