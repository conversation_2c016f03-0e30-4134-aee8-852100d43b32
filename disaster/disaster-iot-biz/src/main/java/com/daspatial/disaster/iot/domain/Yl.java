package com.daspatial.disaster.iot.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 雨量数据对象 tbl_jc_yl
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@TableName("tbl_jc_yl")
@EqualsAndHashCode(callSuper = true)
public class Yl extends Model<Yl> {

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private String area;

    /**
     * 站点编号
     */
    private String station;

    /**
     * 传感器编码
     */
    private String sensor;

    /**
     * 监测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime clttm;

    /**
     * 表示一次数据上报间隔内的降雨量，单位：mm（毫米）
     */
    private Double value;

    /**
     * 当日雨量累积值，单位：mm（毫米）
     */
    private Double totalvalue;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime systm;

}
