package com.daspatial.disaster.iot.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 振动数据对象 tbl_jc_zd
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Data
@TableName("tbl_jc_zd")
@EqualsAndHashCode(callSuper = true)
public class Zd extends Model<Zd> {

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private String area;

    /**
     * 站点编号
     */
    private String station;

    /**
     * 传感器编号
     */
    private String sensor;

    /**
     * 监测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime clttm;

    /**
     * 传感器 X 轴振动频率，单位：Hz（赫兹）
     */
    private Double plx;

    /**
     * 传感器 Y 轴振动频率
     */
    private Double ply;

    /**
     * 传感器 Z 轴振动频率
     */
    private Double plz;

    /**
     * 振动幅度mm
     */
    private Double value;

    /**
     * 传感器初始位置为原点，X 轴瞬间位移
     */
    private Double sjx;

    /**
     * 传感器初始位置为原点，Y 轴瞬间位移
     */
    private Double sjy;

    /**
     * 传感器初始位置为原点，Z 轴瞬间位移
     */
    private Double sjz;

    /**
     * 传感器初始位置为原点，合方向上瞬间位移
     */
    private Double sjvalue;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime systm;


}
