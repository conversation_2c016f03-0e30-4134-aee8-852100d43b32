package com.daspatial.disaster.iot.domain.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 监测设备数据上传，加速度数据DTO
 *
 * <AUTHOR>
 */
@Data
public class AccelerationDTO implements Serializable {

    /**
     * 传感器编号
     */
    @NotBlank(message = "传感器编号不能为空")
    private String sensorCode;

    /**
     * 南北方向加速度分量值
     */
    private String gX;

    /**
     * 东西方向加速度分量值
     */
    private String gY;

    /**
     * 垂直方向加速度分量值
     */
    private String gZ;

    /**
     * 数据时间
     */
    @NotNull(message = "数据时间不能为空")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date dataTime;
}
