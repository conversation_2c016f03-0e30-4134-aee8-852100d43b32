package com.daspatial.disaster.iot.domain.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 监测数据上报DTO
 *
 * <AUTHOR>
 */
@Data
public class DeviceRealtimeDataDTO implements Serializable {

    /**
     * 设备id, 鉴权信息
     */
    @NotBlank(message = "设备id不能为空")
    private String deviceId;

    /**
     * 设备key, 鉴权信息
     */
    @NotBlank(message = "设备key不能为空")
    private String apikey;

    /**
     * 监测数据
     */
    @NotNull(message = "监测数据不能为空")
    private Map<String, Object> data;
}
