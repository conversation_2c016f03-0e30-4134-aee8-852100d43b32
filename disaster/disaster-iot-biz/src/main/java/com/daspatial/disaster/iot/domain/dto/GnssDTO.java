package com.daspatial.disaster.iot.domain.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 监测设备数据上传，GNSS位移数据DTO
 *
 * <AUTHOR>
 */
@Data
public class GnssDTO implements Serializable {

    /**
     * 传感器编号
     */
    @NotBlank(message = "传感器编号不能为空")
    private String sensorCode;

    /**
     * 南北方向相对位移值
     */
    private String valueX;

    /**
     * 东西方向相对位移值
     */
    private String valueY;

    /**
     * 垂直方向相对位移值
     */
    private String valueZ;

    /**
     * 数据时间
     */
    @NotNull(message = "数据时间不能为空")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date dataTime;
}
