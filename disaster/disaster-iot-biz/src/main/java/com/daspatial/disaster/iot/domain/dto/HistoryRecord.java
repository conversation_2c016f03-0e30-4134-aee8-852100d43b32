package com.daspatial.disaster.iot.domain.dto;

import com.daspatial.disaster.iot.common.utils.DateUtils;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 监测设备上报的历史监测数据对象
 *
 * <AUTHOR>
 */
public class HistoryRecord implements Serializable {

    /**
     * 监测时间
     */
    @Setter
    @Getter
    private String tm;

    /**
     * 监测时间转换后的格式
     */
    private LocalDateTime clttm;

    /**
     * 监测数据的值，多个值用逗号分隔
     */
    @Setter
    @Getter
    private String value;


    // 尝试常见格式
    private static String[] PATTERNS = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd HH:mm:ss.SSS",
            "yyyy-MM-dd'T'HH:mm:ss.SSSZZ",
    };

    public LocalDateTime getClttm() {
        if (tm == null || tm.trim().isEmpty()) {
            return null;
        }
        try {
            // 处理时间戳格式 (如1533199952449)
            if (tm.matches("\\d+")) {
                return LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(tm)), DateUtils.ZONE_GMT8);
            }
            // 处理ISO格式 (如2018-08-02T08:52:32.449Z)
            try {
                ZonedDateTime zdt = ZonedDateTime.parse(tm);
                return zdt.withZoneSameInstant(ZoneOffset.ofHours(8)).toLocalDateTime();
            } catch (DateTimeParseException ignored1) {
            }

            for (String pattern : PATTERNS) {
                try {
                    return LocalDateTime.parse(tm, DateTimeFormatter.ofPattern(pattern));
                } catch (DateTimeParseException ignored2) {

                }
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("日期格式无效: " + tm);
        }
        throw new IllegalArgumentException("日期格式无效: " + tm);
    }

}
