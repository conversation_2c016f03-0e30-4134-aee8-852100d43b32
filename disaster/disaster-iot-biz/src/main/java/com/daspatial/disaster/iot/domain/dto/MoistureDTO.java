package com.daspatial.disaster.iot.domain.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 监测设备数据上传，土壤含水率数据DTO
 *
 * <AUTHOR>
 */
@Data
public class MoistureDTO implements Serializable {

    /**
     * 传感器编号
     */
    @NotBlank(message = "传感器编号不能为空")
    private String sensorCode;

    /**
     * 含水率
     */
    @NotBlank(message = "含水率不能为空")
    private String value;

    /**
     * 数据时间
     */
    @NotNull(message = "数据时间不能为空")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date dataTime;
}
