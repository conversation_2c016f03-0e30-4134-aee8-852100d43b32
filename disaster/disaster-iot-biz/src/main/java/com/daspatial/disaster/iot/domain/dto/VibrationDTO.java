package com.daspatial.disaster.iot.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 监测设备数据上传，振动数据DTO
 *
 * <AUTHOR>
 */
@Data
public class VibrationDTO implements Serializable {

    /**
     * 传感器编号
     */
    @NotBlank(message = "传感器编号不能为空")
    private String sensorCode;

    /**
     * 南北方向振动频率值
     */
    @JsonProperty("PLX")
    private String PLX;

    /**
     * 东西方向振动频率值
     */
    @JsonProperty("PLY")
    private String PLY;

    /**
     * 垂直方向振动频率值
     */
    @JsonProperty("PLZ")
    private String PLZ;

    /**
     * 振动幅度
     */
    private String value;

    /**
     * 南北方向瞬间位移值
     */
    @JsonProperty("SJX")
    private String SJX;

    /**
     * 东西方向瞬间位移值
     */
    @JsonProperty("SJY")
    private String SJY;

    /**
     * 垂直方向瞬间位移值
     */
    @JsonProperty("SJZ")
    private String SJZ;

    /**
     * 合方向瞬间位移值
     */
    @JsonProperty("SJValue")
    private String SJValue;

    /**
     * 数据时间
     */
    @NotNull(message = "数据时间不能为空")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date dataTime;
}
