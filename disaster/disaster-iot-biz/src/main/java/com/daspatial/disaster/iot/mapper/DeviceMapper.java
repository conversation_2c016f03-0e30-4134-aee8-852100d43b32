package com.daspatial.disaster.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.daspatial.disaster.iot.domain.Device;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DeviceMapper extends BaseMapper<Device> {

    /**
     * 根据设备id查询设备编号
     *
     * @param clientId
     * @return
     */
    String selectDeviceSnByClientId(String clientId);

    /**
     * 根据设备id，设备key查询设备详情
     *
     * @param clientId
     * @param deviceKey
     * @return
     */
    long selectCountByClientIdAndDeviceKey(@Param("clientId") String clientId, @Param("deviceKey") String deviceKey);

    /**
     * 根据设备编号查询设备id
     *
     * @param deviceSn
     * @return
     */
    String selectClientIdByDeviceSn(String deviceSn);

    int updateDeviceStatus(@Param("clientId") String clientId, @Param("deviceStatus") String deviceStatus);

}