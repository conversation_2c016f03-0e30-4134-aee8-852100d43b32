package com.daspatial.disaster.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.daspatial.disaster.iot.domain.Gnss;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 地位移,GNSS数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Mapper
public interface GnssMapper extends BaseMapper<Gnss> {

    /**
     * 查询地位移,GNSS数据
     *
     * @param id 地位移,GNSS数据主键
     * @return 地位移, GNSS数据
     */
    Gnss selectGnssById(Long id);

    /**
     * 查询地位移,GNSS数据列表
     *
     * @param gnss 地位移,GNSS数据
     * @return 地位移, GNSS数据集合
     */
    List<Gnss> selectGnssList(Gnss gnss);

    /**
     * 新增地位移,GNSS数据
     *
     * @param gnss 地位移,GNSS数据
     * @return 结果
     */
    int insertGnss(Gnss gnss);

    /**
     * 修改地位移,GNSS数据
     *
     * @param gnss 地位移,GNSS数据
     * @return 结果
     */
    int updateGnss(Gnss gnss);

    /**
     * 删除地位移,GNSS数据
     *
     * @param id 地位移,GNSS数据主键
     * @return 结果
     */
    int deleteGnssById(Long id);

    /**
     * 批量删除地位移,GNSS数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteGnssByIds(Long[] ids);
}
