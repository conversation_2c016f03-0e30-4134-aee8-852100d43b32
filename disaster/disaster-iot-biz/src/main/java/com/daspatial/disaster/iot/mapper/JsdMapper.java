package com.daspatial.disaster.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.daspatial.disaster.iot.domain.Jsd;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 加速度数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Mapper
public interface JsdMapper extends BaseMapper<Jsd> {

    /**
     * 查询加速度数据
     *
     * @param id 加速度数据主键
     * @return 加速度数据
     */
    Jsd selectJsdById(Long id);

    /**
     * 查询加速度数据列表
     *
     * @param jsd 加速度数据
     * @return 加速度数据集合
     */
    List<Jsd> selectJsdList(Jsd jsd);

    /**
     * 新增加速度数据
     *
     * @param jsd 加速度数据
     * @return 结果
     */
    int insertJsd(Jsd jsd);

    /**
     * 修改加速度数据
     *
     * @param jsd 加速度数据
     * @return 结果
     */
    int updateJsd(Jsd jsd);

    /**
     * 删除加速度数据
     *
     * @param id 加速度数据主键
     * @return 结果
     */
    int deleteJsdById(Long id);

    /**
     * 批量删除加速度数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteJsdByIds(Long[] ids);
}
