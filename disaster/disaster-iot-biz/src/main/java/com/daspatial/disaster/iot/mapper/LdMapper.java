package com.daspatial.disaster.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.daspatial.disaster.iot.domain.Ld;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 雷达数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Mapper
public interface LdMapper extends BaseMapper<Ld> {

    /**
     * 查询雷达数据
     *
     * @param id 雷达数据主键
     * @return 雷达数据
     */
    Ld selectLdById(Long id);

    /**
     * 查询雷达数据列表
     *
     * @param ld 雷达数据
     * @return 雷达数据集合
     */
    List<Ld> selectLdList(Ld ld);

    /**
     * 新增雷达数据
     *
     * @param ld 雷达数据
     * @return 结果
     */
    int insertLd(Ld ld);

    /**
     * 修改雷达数据
     *
     * @param ld 雷达数据
     * @return 结果
     */
    int updateLd(Ld ld);

    /**
     * 删除雷达数据
     *
     * @param id 雷达数据主键
     * @return 结果
     */
    int deleteLdById(Long id);

    /**
     * 批量删除雷达数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteLdByIds(Long[] ids);
}
