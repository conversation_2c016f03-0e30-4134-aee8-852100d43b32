package com.daspatial.disaster.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.daspatial.disaster.iot.domain.Lfwy;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 裂缝数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Mapper
public interface LfwyMapper extends BaseMapper<Lfwy> {
    /**
     * 查询裂缝数据
     *
     * @param id 裂缝数据主键
     * @return 裂缝数据
     */
    Lfwy selectLfwyById(Long id);

    /**
     * 查询裂缝数据列表
     *
     * @param lfwy 裂缝数据
     * @return 裂缝数据集合
     */
    List<Lfwy> selectLfwyList(Lfwy lfwy);

    /**
     * 新增裂缝数据
     *
     * @param lfwy 裂缝数据
     * @return 结果
     */
    int insertLfwy(Lfwy lfwy);

    /**
     * 修改裂缝数据
     *
     * @param lfwy 裂缝数据
     * @return 结果
     */
    int updateLfwy(Lfwy lfwy);

    /**
     * 删除裂缝数据
     *
     * @param id 裂缝数据主键
     * @return 结果
     */
    int deleteLfwyById(Long id);

    /**
     * 批量删除裂缝数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteLfwyByIds(Long[] ids);
}
