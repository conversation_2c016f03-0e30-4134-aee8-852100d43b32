package com.daspatial.disaster.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.daspatial.disaster.iot.domain.Nw;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 泥水位Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Mapper
public interface NwMapper extends BaseMapper<Nw> {

    /**
     * 查询泥水位
     *
     * @param id 泥水位主键
     * @return 泥水位
     */
    Nw selectNwById(Long id);

    /**
     * 查询泥水位列表
     *
     * @param nw 泥水位
     * @return 泥水位集合
     */
    List<Nw> selectNwList(Nw nw);

    /**
     * 新增泥水位
     *
     * @param nw 泥水位
     * @return 结果
     */
    int insertNw(Nw nw);

    /**
     * 修改泥水位
     *
     * @param nw 泥水位
     * @return 结果
     */
    int updateNw(Nw nw);

    /**
     * 删除泥水位
     *
     * @param id 泥水位主键
     * @return 结果
     */
    int deleteNwById(Long id);

    /**
     * 批量删除泥水位
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteNwByIds(Long[] ids);
}
