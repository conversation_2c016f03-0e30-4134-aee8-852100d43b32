package com.daspatial.disaster.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.daspatial.disaster.iot.domain.Qj;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 倾角数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Mapper
public interface QjMapper extends BaseMapper<Qj> {

    /**
     * 查询倾角数据
     *
     * @param id 倾角数据主键
     * @return 倾角数据
     */
    Qj selectQjById(Long id);

    /**
     * 查询倾角数据列表
     *
     * @param qj 倾角数据
     * @return 倾角数据集合
     */
    List<Qj> selectQjList(Qj qj);

    /**
     * 新增倾角数据
     *
     * @param qj 倾角数据
     * @return 结果
     */
    int insertQj(Qj qj);

    /**
     * 修改倾角数据
     *
     * @param qj 倾角数据
     * @return 结果
     */
    int updateQj(Qj qj);

    /**
     * 删除倾角数据
     *
     * @param id 倾角数据主键
     * @return 结果
     */
    int deleteQjById(Long id);

    /**
     * 批量删除倾角数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteQjByIds(Long[] ids);

}
