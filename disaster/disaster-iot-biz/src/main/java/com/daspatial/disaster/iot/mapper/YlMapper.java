package com.daspatial.disaster.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.daspatial.disaster.iot.domain.Yl;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 雨量数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Mapper
public interface YlMapper extends BaseMapper<Yl> {

    /**
     * 查询雨量数据
     *
     * @param id 雨量数据主键
     * @return 雨量数据
     */
    Yl selectYlById(Long id);

    /**
     * 查询雨量数据列表
     *
     * @param yl 雨量数据
     * @return 雨量数据集合
     */
    List<Yl> selectYlList(Yl yl);

    /**
     * 新增雨量数据
     *
     * @param yl 雨量数据
     * @return 结果
     */
    int insertYl(Yl yl);

    /**
     * 修改雨量数据
     *
     * @param yl 雨量数据
     * @return 结果
     */
    int updateYl(Yl yl);

    /**
     * 删除雨量数据
     *
     * @param id 雨量数据主键
     * @return 结果
     */
    int deleteYlById(Long id);

    /**
     * 批量删除雨量数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteYlByIds(Long[] ids);
}
