package com.daspatial.disaster.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.daspatial.disaster.iot.domain.Zd;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 振动数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Mapper
public interface ZdMapper extends BaseMapper<Zd> {

    /**
     * 查询振动数据
     *
     * @param id 振动数据主键
     * @return 振动数据
     */
    Zd selectZdById(Long id);

    /**
     * 查询振动数据列表
     *
     * @param zd 振动数据
     * @return 振动数据集合
     */
    List<Zd> selectZdList(Zd zd);

    /**
     * 新增振动数据
     *
     * @param zd 振动数据
     * @return 结果
     */
    int insertZd(Zd zd);

    /**
     * 修改振动数据
     *
     * @param zd 振动数据
     * @return 结果
     */
    int updateZd(Zd zd);

    /**
     * 删除振动数据
     *
     * @param id 振动数据主键
     * @return 结果
     */
    int deleteZdById(Long id);

    /**
     * 批量删除振动数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteZdByIds(Long[] ids);
}
