package com.daspatial.disaster.iot.mqtt;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import com.daspatial.disaster.iot.common.enums.DataPointsType;
import com.daspatial.disaster.iot.domain.dto.DeviceRealtimeDataDTO;
import com.daspatial.disaster.iot.service.IIoTDeviceService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttMessage;

import java.util.Arrays;
import java.util.Map;

@Slf4j
public class MqttConsumerCallBack implements MqttCallback {

    @Getter
    @Setter
    private IIoTDeviceService iotDeviceService;

    public MqttConsumerCallBack(IIoTDeviceService iotDeviceService) {
        this.iotDeviceService = iotDeviceService;
    }

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 客户端断开连接的回调
     */
    @Override
    public void connectionLost(Throwable throwable) {
        log.info("Connection lost");
    }

    /**
     * 消息到达的回调
     */
    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        try {
            byte[] fileByteArr = null;

            // 解析报文，根据省平台规范，报文格式如下：
            // Byte1，数据格式类型：1：单个或多个监测类型的监测数据实时上传，2：单个或多个监测类型的历史数据上传，3：适用于文件类型的数据上传
            int type = message.getPayload()[0];
            // Byte2-3 ，后面json字符串数据包大小
            int dataSize = ((message.getPayload()[1] & 0xFF) << 8) | (message.getPayload()[2] & 0xFF);
            // Byte4-Byte n，json字符串数据包
            int jsonStartIndex = 3;
            int jsonEndIndex = jsonStartIndex + dataSize;
            // 将消息转为字符串
            String rawMessage = new String(Arrays.copyOfRange(message.getPayload(), jsonStartIndex, jsonEndIndex));
            // 处理文件数据上传
            if (DataPointsType.THREE.getCode().equals(type)) {
                // Byte n+1 ~ n+2，文件数据流的大小
                int fileDataSize = ((message.getPayload()[jsonEndIndex] & 0xFF) << 8) | (message.getPayload()[jsonEndIndex + 1] & 0xFF);
                log.info("====fileDataSize : {}", fileDataSize);
                // Byte n+3 ~ end，文件数据流
                fileByteArr = Arrays.copyOfRange(message.getPayload(), jsonEndIndex + 2, message.getPayload().length);
                log.info("====fileByteArr length : {}, fileDataSize : {}", fileByteArr.length, fileDataSize);
            }
            log.info("mqtt message subject topic : {}, qos : {}, retained : {}, messageId : {},  === type: {}, dataSize: {}, fileDataSize: {}, message: {}",
                    topic, message.getQos(), message.isRetained(), message.getId(), type, dataSize, fileByteArr == null ? 0 : fileByteArr.length, rawMessage);

            // 将JSON字符串转换为Map对象
            Map<String, Object> jsonMap = objectMapper.readValue(rawMessage, Map.class);
            String deviceId = jsonMap.keySet().iterator().next();
            // 组装实时数据对象
            DeviceRealtimeDataDTO dto = new DeviceRealtimeDataDTO();
            dto.setDeviceId(deviceId);
            dto.setData(jsonMap);
            // 保存数据
            iotDeviceService.dataPoints(type, deviceId, dto, fileByteArr);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 消息发布成功的回调
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
        log.info("Message received successfully");
    }
}
