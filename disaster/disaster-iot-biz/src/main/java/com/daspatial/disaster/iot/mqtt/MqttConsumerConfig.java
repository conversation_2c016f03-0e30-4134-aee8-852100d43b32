package com.daspatial.disaster.iot.mqtt;

import cn.hutool.core.util.IdUtil;
import com.daspatial.disaster.iot.service.IIoTDeviceService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * Mqtt消费者
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
@Component
@RequiredArgsConstructor
public class MqttConsumerConfig {

    private final IIoTDeviceService iotDeviceService;

    @Value("${iot.mqtt.username}")
    private String username;

    @Value("${iot.mqtt.password}")
    private String password;

    @Value("${iot.mqtt.url}")
    private String brokerUrl;

    @Value("${iot.mqtt.client.id:iot-biz}")
    private String clientId;

    @Value("${iot.mqtt.default.topic}")
    private String defaultTopic;

    @Value("${iot.mqtt.topic}")
    private String topic;

    /**
     * 客户端对象
     */
    private MqttClient client;

    /**
     * 在bean初始化后连接到服务器
     */
    @PostConstruct
    public void init() {
        connect();
    }

    /**
     * 客户端连接服务端
     */
    public void connect() {
        try {
            clientId = clientId + IdUtil.getSnowflakeNextIdStr();
            // 创建MQTT客户端对象
            client = new MqttClient(brokerUrl, clientId, new MemoryPersistence());
            // 连接设置
            MqttConnectOptions options = new MqttConnectOptions();
            // 是否清空session，设置为false表示服务器会保留客户端的连接记录，客户端重连之后能获取到服务器在客户端断开连接期间推送的消息
            // 设置为true表示每次连接到服务端都是以新的身份
            options.setCleanSession(true);
            // 设置连接用户名
            options.setUserName(username);
            // 设置连接密码
            options.setPassword(password.toCharArray());
            // 设置超时时间，单位为秒
            options.setConnectionTimeout(30);
            // 设置心跳时间 单位为秒，表示服务器每隔1.5*20秒的时间向客户端发送心跳判断客户端是否在线
            options.setKeepAliveInterval(60);
            // 设置遗嘱消息的话题，若客户端和服务器之间的连接意外断开，服务器将发布客户端的遗嘱信息
            options.setWill("willTopic", (clientId + "与服务器断开连接").getBytes(), 0, false);
            // 设置回调
            client.setCallback(new MqttConsumerCallBack(iotDeviceService));

            log.info("Connecting to broker: " + brokerUrl);
            client.connect(options);
            log.info("Connected");
            // 订阅主题
            // 消息等级，和主题数组一一对应，服务端将按照指定等级给订阅了主题的客户端推送消息
            // 订阅主题
            client.subscribe(topic);
        } catch (MqttException e) {
            e.printStackTrace();
        }
    }

    /**
     * 断开连接
     */
    public void disConnect() {
        try {
            client.disconnect();
        } catch (MqttException e) {
            e.printStackTrace();
        }
    }

//    /**
//     * 订阅主题
//     */
//    public void subscribe(String topic, int qos) {
//        try {
//            client.subscribe(topic, qos);
//        } catch (MqttException e) {
//            e.printStackTrace();
//        }
//    }
}