package com.daspatial.disaster.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.daspatial.disaster.iot.domain.Dxs;

import java.util.List;

/**
 * 地下水数据Service接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface IDxsService extends IService<Dxs> {

    /**
     * 查询地下水数据
     *
     * @param area 地下水数据主键
     * @return 地下水数据
     */
    Dxs selectDxsByArea(String area);

    /**
     * 查询地下水数据列表
     *
     * @param dxs 地下水数据
     * @return 地下水数据集合
     */
    List<Dxs> selectDxsList(Dxs dxs);

    /**
     * 新增地下水数据
     *
     * @param dxs 地下水数据
     * @return 结果
     */
    int insertDxs(Dxs dxs);

    /**
     * 修改地下水数据
     *
     * @param dxs 地下水数据
     * @return 结果
     */
    int updateDxs(Dxs dxs);

    /**
     * 批量删除地下水数据
     *
     * @param areas 需要删除的地下水数据主键集合
     * @return 结果
     */
    int deleteDxsByAreas(String[] areas);

    /**
     * 删除地下水数据信息
     *
     * @param area 地下水数据主键
     * @return 结果
     */
    int deleteDxsByArea(String area);
}
