package com.daspatial.disaster.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.daspatial.disaster.iot.domain.Gnss;

import java.util.List;

/**
 * 地位移,GNSS数据Service接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface IGnssService extends IService<Gnss> {

    /**
     * 查询地位移,GNSS数据
     *
     * @param id 地位移,GNSS数据主键
     * @return 地位移, GNSS数据
     */
    Gnss selectGnssById(Long id);

    /**
     * 查询地位移,GNSS数据列表
     *
     * @param gnss 地位移,GNSS数据
     * @return 地位移, GNSS数据集合
     */
    List<Gnss> selectGnssList(Gnss gnss);

    /**
     * 新增地位移,GNSS数据
     *
     * @param gnss 地位移,GNSS数据
     * @return 结果
     */
    int insertGnss(Gnss gnss);

    /**
     * 修改地位移,GNSS数据
     *
     * @param gnss 地位移,GNSS数据
     * @return 结果
     */
    int updateGnss(Gnss gnss);

    /**
     * 批量删除地位移,GNSS数据
     *
     * @param ids 需要删除的地位移,GNSS数据主键集合
     * @return 结果
     */
    int deleteGnssByIds(Long[] ids);

    /**
     * 删除地位移,GNSS数据信息
     *
     * @param id 地位移,GNSS数据主键
     * @return 结果
     */
    int deleteGnssById(Long id);
}
