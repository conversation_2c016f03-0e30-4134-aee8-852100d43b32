package com.daspatial.disaster.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.daspatial.disaster.iot.domain.Jsd;

import java.util.List;

/**
 * 加速度数据Service接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface IJsdService extends IService<Jsd> {

    /**
     * 查询加速度数据
     *
     * @param id 加速度数据主键
     * @return 加速度数据
     */
    Jsd selectJsdById(Long id);

    /**
     * 查询加速度数据列表
     *
     * @param jsd 加速度数据
     * @return 加速度数据集合
     */
    List<Jsd> selectJsdList(Jsd jsd);

    /**
     * 新增加速度数据
     *
     * @param jsd 加速度数据
     * @return 结果
     */
    int insertJsd(Jsd jsd);

    /**
     * 修改加速度数据
     *
     * @param jsd 加速度数据
     * @return 结果
     */
    int updateJsd(Jsd jsd);

    /**
     * 批量删除加速度数据
     *
     * @param ids 需要删除的加速度数据主键集合
     * @return 结果
     */
    int deleteJsdByIds(Long[] ids);

    /**
     * 删除加速度数据信息
     *
     * @param id 加速度数据主键
     * @return 结果
     */
    int deleteJsdById(Long id);
}
