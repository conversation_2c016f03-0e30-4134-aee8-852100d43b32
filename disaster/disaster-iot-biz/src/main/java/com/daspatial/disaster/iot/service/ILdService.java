package com.daspatial.disaster.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.daspatial.disaster.iot.domain.Ld;

import java.util.List;

/**
 * 雷达数据Service接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface ILdService extends IService<Ld> {

    /**
     * 查询雷达数据
     *
     * @param id 雷达数据主键
     * @return 雷达数据
     */
    Ld selectLdById(Long id);

    /**
     * 查询雷达数据列表
     *
     * @param ld 雷达数据
     * @return 雷达数据集合
     */
    List<Ld> selectLdList(Ld ld);

    /**
     * 新增雷达数据
     *
     * @param ld 雷达数据
     * @return 结果
     */
    int insertLd(Ld ld);

    /**
     * 修改雷达数据
     *
     * @param ld 雷达数据
     * @return 结果
     */
    int updateLd(Ld ld);

    /**
     * 批量删除雷达数据
     *
     * @param ids 需要删除的雷达数据主键集合
     * @return 结果
     */
    int deleteLdByIds(Long[] ids);

    /**
     * 删除雷达数据信息
     *
     * @param id 雷达数据主键
     * @return 结果
     */
    int deleteLdById(Long id);
}
