package com.daspatial.disaster.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.daspatial.disaster.iot.domain.Lfwy;

import java.util.List;

/**
 * 裂缝数据Service接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface ILfwyService extends IService<Lfwy> {

    /**
     * 查询裂缝数据
     *
     * @param id 裂缝数据主键
     * @return 裂缝数据
     */
    Lfwy selectLfwyById(Long id);

    /**
     * 查询裂缝数据列表
     *
     * @param lfwy 裂缝数据
     * @return 裂缝数据集合
     */
    List<Lfwy> selectLfwyList(Lfwy lfwy);

    /**
     * 新增裂缝数据
     *
     * @param lfwy 裂缝数据
     * @return 结果
     */
    int insertLfwy(Lfwy lfwy);

    /**
     * 修改裂缝数据
     *
     * @param lfwy 裂缝数据
     * @return 结果
     */
    int updateLfwy(Lfwy lfwy);

    /**
     * 批量删除裂缝数据
     *
     * @param ids 需要删除的裂缝数据主键集合
     * @return 结果
     */
    int deleteLfwyByIds(Long[] ids);

    /**
     * 删除裂缝数据信息
     *
     * @param id 裂缝数据主键
     * @return 结果
     */
    int deleteLfwyById(Long id);
}
