package com.daspatial.disaster.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.daspatial.disaster.iot.domain.Nw;

import java.util.List;

/**
 * 泥水位Service接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface INwService extends IService<Nw> {

    /**
     * 查询泥水位
     *
     * @param id 泥水位主键
     * @return 泥水位
     */
    Nw selectNwById(Long id);

    /**
     * 查询泥水位列表
     *
     * @param nw 泥水位
     * @return 泥水位集合
     */
    List<Nw> selectNwList(Nw nw);

    /**
     * 新增泥水位
     *
     * @param nw 泥水位
     * @return 结果
     */
    int insertNw(Nw nw);

    /**
     * 修改泥水位
     *
     * @param nw 泥水位
     * @return 结果
     */
    int updateNw(Nw nw);

    /**
     * 批量删除泥水位
     *
     * @param ids 需要删除的泥水位主键集合
     * @return 结果
     */
    int deleteNwByIds(Long[] ids);

    /**
     * 删除泥水位信息
     *
     * @param id 泥水位主键
     * @return 结果
     */
    int deleteNwById(Long id);
}
