package com.daspatial.disaster.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.daspatial.disaster.iot.domain.Qj;

import java.util.List;

/**
 * 倾角数据Service接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface IQjService extends IService<Qj> {

    /**
     * 查询倾角数据
     *
     * @param id 倾角数据主键
     * @return 倾角数据
     */
    Qj selectQjById(Long id);

    /**
     * 查询倾角数据列表
     *
     * @param qj 倾角数据
     * @return 倾角数据集合
     */
    List<Qj> selectQjList(Qj qj);

    /**
     * 新增倾角数据
     *
     * @param qj 倾角数据
     * @return 结果
     */
    int insertQj(Qj qj);

    /**
     * 修改倾角数据
     *
     * @param qj 倾角数据
     * @return 结果
     */
    int updateQj(Qj qj);

    /**
     * 批量删除倾角数据
     *
     * @param ids 需要删除的倾角数据主键集合
     * @return 结果
     */
    int deleteQjByIds(Long[] ids);

    /**
     * 删除倾角数据信息
     *
     * @param id 倾角数据主键
     * @return 结果
     */
    int deleteQjById(Long id);
}
