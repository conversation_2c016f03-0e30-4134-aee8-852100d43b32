package com.daspatial.disaster.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.daspatial.disaster.iot.domain.Trhsl;

import java.util.List;

/**
 * 土壤含水率数据Service接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface ITrhslService extends IService<Trhsl> {

    /**
     * 查询土壤含水率数据
     *
     * @param id 土壤含水率数据主键
     * @return 土壤含水率数据
     */
    Trhsl selectTrhslById(Long id);

    /**
     * 查询土壤含水率数据列表
     *
     * @param trhsl 土壤含水率数据
     * @return 土壤含水率数据集合
     */
    List<Trhsl> selectTrhslList(Trhsl trhsl);

    /**
     * 新增土壤含水率数据
     *
     * @param trhsl 土壤含水率数据
     * @return 结果
     */
    int insertTrhsl(Trhsl trhsl);

    /**
     * 修改土壤含水率数据
     *
     * @param trhsl 土壤含水率数据
     * @return 结果
     */
    int updateTrhsl(Trhsl trhsl);

    /**
     * 批量删除土壤含水率数据
     *
     * @param ids 需要删除的土壤含水率数据主键集合
     * @return 结果
     */
    int deleteTrhslByIds(Long[] ids);

    /**
     * 删除土壤含水率数据信息
     *
     * @param id 土壤含水率数据主键
     * @return 结果
     */
    int deleteTrhslById(Long id);
}
