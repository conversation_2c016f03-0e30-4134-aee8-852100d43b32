package com.daspatial.disaster.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.daspatial.disaster.iot.domain.Yl;

import java.util.List;

/**
 * 雨量数据Service接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface IYlService extends IService<Yl> {

    /**
     * 查询雨量数据
     *
     * @param id 雨量数据主键
     * @return 雨量数据
     */
    Yl selectYlById(Long id);

    /**
     * 查询雨量数据列表
     *
     * @param yl 雨量数据
     * @return 雨量数据集合
     */
    List<Yl> selectYlList(Yl yl);

    /**
     * 新增雨量数据
     *
     * @param yl 雨量数据
     * @return 结果
     */
    int insertYl(Yl yl);

    /**
     * 修改雨量数据
     *
     * @param yl 雨量数据
     * @return 结果
     */
    int updateYl(Yl yl);

    /**
     * 批量删除雨量数据
     *
     * @param ids 需要删除的雨量数据主键集合
     * @return 结果
     */
    int deleteYlByIds(Long[] ids);

    /**
     * 删除雨量数据信息
     *
     * @param id 雨量数据主键
     * @return 结果
     */
    int deleteYlById(Long id);
}
