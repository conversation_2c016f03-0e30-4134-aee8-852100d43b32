package com.daspatial.disaster.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.daspatial.disaster.iot.domain.Zd;

import java.util.List;

/**
 * 振动数据Service接口
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
public interface IZdService extends IService<Zd> {

    /**
     * 查询振动数据
     *
     * @param id 振动数据主键
     * @return 振动数据
     */
    Zd selectZdById(Long id);

    /**
     * 查询振动数据列表
     *
     * @param zd 振动数据
     * @return 振动数据集合
     */
    List<Zd> selectZdList(Zd zd);

    /**
     * 新增振动数据
     *
     * @param zd 振动数据
     * @return 结果
     */
    int insertZd(Zd zd);

    /**
     * 修改振动数据
     *
     * @param zd 振动数据
     * @return 结果
     */
    int updateZd(Zd zd);

    /**
     * 批量删除振动数据
     *
     * @param ids 需要删除的振动数据主键集合
     * @return 结果
     */
    int deleteZdByIds(Long[] ids);

    /**
     * 删除振动数据信息
     *
     * @param id 振动数据主键
     * @return 结果
     */
    int deleteZdById(Long id);
}
