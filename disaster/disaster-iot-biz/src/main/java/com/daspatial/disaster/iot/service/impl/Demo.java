package com.daspatial.disaster.iot.service.impl;

import com.daspatial.disaster.iot.common.utils.DateUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;

public class Demo {

    public static void main(String[] args) {
        System.out.println(ZoneId.systemDefault());
        System.out.println(ZoneId.systemDefault() == ZoneId.of("GMT+8"));
        System.out.println(ZoneId.systemDefault().equals( ZoneId.of("GMT+8")));
        // 2025-08-28 06:17:59
        String tm = "1756333079000";

        LocalDateTime a = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(tm)), ZoneId.systemDefault()); // 或指定时区ZoneId.of("UTC")
        LocalDateTime d = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(tm)), ZoneId.of("GMT+8")); // 或指定时区
        System.out.println(a);
        System.out.println(d);
        System.out.println("===============");
        LocalDateTime b = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(tm)), ZoneId.of("UTC")); // 或指定时区
        LocalDateTime c = LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(tm)), ZoneId.of("GMT")); // 或指定时区
        System.out.println(b);
        System.out.println(c);


        System.out.println("===============");
        String tm2 = "2025-09-02T22:06:53.000Z";
        LocalDateTime dd =  LocalDateTime.parse(tm2, DateTimeFormatter.ISO_DATE_TIME.withZone(ZoneId.of("UTC")));
        LocalDateTime ff =  LocalDateTime.parse(tm2, DateTimeFormatter.ISO_DATE_TIME.withZone(DateUtils.ZONE_GMT8));

        ZonedDateTime zdt = ZonedDateTime.parse(tm2);
        LocalDateTime result = zdt.withZoneSameInstant(ZoneOffset.ofHours(8)).toLocalDateTime();

        System.out.println(dd);
        System.out.println(ff);
        System.out.println(zdt);
        System.out.println(result);


    }
}
