package com.daspatial.disaster.iot.service.impl;

import com.daspatial.disaster.iot.common.utils.DateUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class Demo2 {

    public static void main(String[] args) {

        //

        System.out.println("===============");
        String tm2 = "2025-13-01T11:00:00.000Z";
        LocalDateTime dd =  LocalDateTime.parse(tm2, DateTimeFormatter.ISO_DATE_TIME.withZone(ZoneId.of("UTC")));
        LocalDateTime ff =  LocalDateTime.parse(tm2, DateTimeFormatter.ISO_DATE_TIME.withZone(DateUtils.ZONE_GMT8));

        ZonedDateTime zdt = ZonedDateTime.parse(tm2);
        LocalDateTime result = zdt.withZoneSameInstant(ZoneOffset.ofHours(8)).toLocalDateTime();

        System.out.println(dd);
        System.out.println(ff);
        System.out.println(zdt);
        System.out.println(result);
    }
}
