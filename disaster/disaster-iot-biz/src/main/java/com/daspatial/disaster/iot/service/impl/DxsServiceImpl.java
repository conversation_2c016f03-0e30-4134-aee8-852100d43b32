package com.daspatial.disaster.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.daspatial.disaster.iot.mapper.DxsMapper;
import com.daspatial.disaster.iot.domain.Dxs;
import com.daspatial.disaster.iot.service.IDxsService;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 地下水数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class DxsServiceImpl extends ServiceImpl<DxsMapper, Dxs> implements IDxsService {

    @Resource
    private DxsMapper dxsMapper;

    /**
     * 查询地下水数据
     *
     * @param area 地下水数据主键
     * @return 地下水数据
     */
    @Override
    public Dxs selectDxsByArea(String area) {
        return dxsMapper.selectDxsByArea(area);
    }

    /**
     * 查询地下水数据列表
     *
     * @param dxs 地下水数据
     * @return 地下水数据
     */
    @Override
    public List<Dxs> selectDxsList(Dxs dxs) {
        return dxsMapper.selectDxsList(dxs);
    }

    /**
     * 新增地下水数据
     *
     * @param dxs 地下水数据
     * @return 结果
     */
    @Override
    public int insertDxs(Dxs dxs) {
        dxs.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return dxsMapper.insertDxs(dxs);
    }

    /**
     * 修改地下水数据
     *
     * @param dxs 地下水数据
     * @return 结果
     */
    @Override
    public int updateDxs(Dxs dxs) {
        return dxsMapper.updateDxs(dxs);
    }

    /**
     * 批量删除地下水数据
     *
     * @param areas 需要删除的地下水数据主键
     * @return 结果
     */
    @Override
    public int deleteDxsByAreas(String[] areas) {
        return dxsMapper.deleteDxsByAreas(areas);
    }

    /**
     * 删除地下水数据信息
     *
     * @param area 地下水数据主键
     * @return 结果
     */
    @Override
    public int deleteDxsByArea(String area) {
        return dxsMapper.deleteDxsByArea(area);
    }

}
