package com.daspatial.disaster.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.daspatial.disaster.iot.mapper.GnssMapper;
import com.daspatial.disaster.iot.domain.Gnss;
import com.daspatial.disaster.iot.service.IGnssService;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 地位移,GNSS数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class GnssServiceImpl extends ServiceImpl<GnssMapper, Gnss> implements IGnssService {

    @Resource
    private GnssMapper gnssMapper;

    /**
     * 查询地位移,GNSS数据
     *
     * @param id 地位移,GNSS数据主键
     * @return 地位移, GNSS数据
     */
    @Override
    public Gnss selectGnssById(Long id) {
        return gnssMapper.selectGnssById(id);
    }

    /**
     * 查询地位移,GNSS数据列表
     *
     * @param gnss 地位移,GNSS数据
     * @return 地位移, GNSS数据
     */
    @Override
    public List<Gnss> selectGnssList(Gnss gnss) {
        return gnssMapper.selectGnssList(gnss);
    }

    /**
     * 新增地位移,GNSS数据
     *
     * @param gnss 地位移,GNSS数据
     * @return 结果
     */
    @Override
    public int insertGnss(Gnss gnss) {
        gnss.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return gnssMapper.insertGnss(gnss);
    }

    /**
     * 修改地位移,GNSS数据
     *
     * @param gnss 地位移,GNSS数据
     * @return 结果
     */
    @Override
    public int updateGnss(Gnss gnss) {
        return gnssMapper.updateGnss(gnss);
    }

    /**
     * 批量删除地位移,GNSS数据
     *
     * @param ids 需要删除的地位移,GNSS数据主键
     * @return 结果
     */
    @Override
    public int deleteGnssByIds(Long[] ids) {
        return gnssMapper.deleteGnssByIds(ids);
    }

    /**
     * 删除地位移,GNSS数据信息
     *
     * @param id 地位移,GNSS数据主键
     * @return 结果
     */
    @Override
    public int deleteGnssById(Long id) {
        return gnssMapper.deleteGnssById(id);
    }
}
