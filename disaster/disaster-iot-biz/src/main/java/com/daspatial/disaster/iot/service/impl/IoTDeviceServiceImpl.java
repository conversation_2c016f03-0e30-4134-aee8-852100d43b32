package com.daspatial.disaster.iot.service.impl;

import com.daspatial.disaster.iot.common.enums.DataPointsType;
import com.daspatial.disaster.iot.common.enums.DeviceMonitorType;
import com.daspatial.disaster.iot.common.utils.DateUtils;
import com.daspatial.disaster.iot.domain.*;
import com.daspatial.disaster.iot.domain.dto.DeviceRealtimeDataDTO;
import com.daspatial.disaster.iot.domain.dto.HistoryRecord;
import com.daspatial.disaster.iot.mapper.*;
import com.daspatial.disaster.iot.service.IIoTDeviceService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class IoTDeviceServiceImpl implements IIoTDeviceService {

    private final DeviceMapper deviceMapper;
    private final LogMonitorDataMapper logMonitorDataMapper;
    private final YlMapper ylMapper;
    private final NwMapper nwMapper;
    private final LfwyMapper lfwyMapper;
    private final TrhslMapper trhslMapper;
    private final QjMapper qjMapper;
    private final GnssMapper gnssMapper;
    private final JsdMapper jsdMapper;
    private final ZdMapper zdMapper;

    @Value("${iot.mqtt.file.dir}")
    private String fileDir;

    private final ObjectMapper mapper = new ObjectMapper();

    /**
     * 打印和保存监控数据
     *
     * @param type
     * @param deviceId
     * @param dto
     */
    private void printAndSaveLogMonitorData(Integer type, String deviceId, String deviceSn, DeviceRealtimeDataDTO dto) {
        try {
            String jsonStr = mapper.writeValueAsString(dto);
            LogMonitorData logMonitorData = new LogMonitorData();
            logMonitorData.setType(type);
            logMonitorData.setDeviceId(deviceId);
            logMonitorData.setStation(deviceSn);
            logMonitorData.setData(jsonStr);
            logMonitorData.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
            logMonitorDataMapper.insert(logMonitorData);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean dataPoints(Integer type, String deviceId, DeviceRealtimeDataDTO dto) {
        return dataPoints(type, deviceId, dto, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean dataPoints(Integer type, String deviceId, DeviceRealtimeDataDTO dto, byte[] fileByteArr) {
        // 数据点格式类型type，1：适用于单个或多个监测类型的监测数据实时上传，2：适用于单个或多个监测类型的历史数据上传，3：适用于文件类型的数据上传
        if (DataPointsType.THREE.getCode().equals(type)) {
            if (deviceId == null) {
                throw new IllegalArgumentException("参数错误，请检查");
            }
            return true;
        }

        deviceId = dto.getDeviceId();
        String deviceSn = deviceMapper.selectDeviceSnByClientId(deviceId);
        // 打印并保存上传的监控数据到日志表中
        printAndSaveLogMonitorData(type, deviceId, deviceSn, dto);

        try {
            if (DataPointsType.ONE.getCode().equals(type)) {
                analyzeSaveRealtimeData(deviceId, dto, deviceSn);
            } else if (DataPointsType.TWO.getCode().equals(type)) {
                analyzeSaveHistoricalData(deviceId, dto, deviceSn);
            } else if (DataPointsType.THREE.getCode().equals(type)) {
                analyzeSaveFileData(deviceId, dto, deviceSn, fileByteArr);
            }
            return true;
        } catch (Exception e) {
            log.error("dataPoints error.", e);
            throw e;
        }
    }

    /**
     * 解析并保存单个或多个监测类型的监测数据实时上传
     *
     * @param deviceId
     * @param dto
     * @param deviceSn
     */
    private void analyzeSaveRealtimeData(String deviceId, DeviceRealtimeDataDTO dto, String deviceSn) {
        Map<String, Object> objectMap = dto.getData();
        if (!objectMap.containsKey(deviceId)) {
            throw new RuntimeException("实时数据参数data值错误，请检查");
        }
        Map<String, Object> monitorDataMap = (Map<String, Object>) objectMap.get(deviceId);
        boolean executeFlag = false;
        for (String monitorDataKey : monitorDataMap.keySet()) {
            // 如果监测数据的key是S1_开头，但值不是S1_ZT_1 则跳过该数据处理流程，直接返回
            if (monitorDataKey.startsWith("S1_") && monitorDataKey.equals(DeviceMonitorType.S1_ZT_1.getCode())) {
                executeFlag = true;
                break;
            }
        }
        if (executeFlag) {
            return;
        }

        if (monitorDataMap.containsKey(DeviceMonitorType.S1_ZT_1.getCode())) {
            // 设备状态数据上传解析，更新设备状态为在线
            // deviceMapper.updateDeviceStatus(deviceId, "在线");
        } else {
            // 监测数据解析
            for (Map.Entry<String, Object> entry : monitorDataMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue().toString();
                if (key.startsWith(DeviceMonitorType.L3_YL.getCode())) {
                    // 雨量数据
                    saveYl(deviceId, deviceSn, key, value, LocalDateTime.now(ZoneId.of("GMT+8")));
                } else if (key.startsWith(DeviceMonitorType.L4_NW.getCode())) {
                    // 泥水位数据
                    saveNw(deviceId, deviceSn, key, value, LocalDateTime.now(ZoneId.of("GMT+8")));
                } else if (key.startsWith(DeviceMonitorType.L1_LF.getCode())) {
                    // 裂缝（拉线位移）数据
                    saveLfwy(deviceId, deviceSn, key, value, LocalDateTime.now(ZoneId.of("GMT+8")));
                } else if (key.startsWith(DeviceMonitorType.L3_HS.getCode())) {
                    // 土壤含水率设备
                    saveTrhsl(deviceId, deviceSn, key, value, LocalDateTime.now(ZoneId.of("GMT+8")));
                } else if (key.startsWith(DeviceMonitorType.L1_QJ.getCode())) {
                    // 倾角数据
                    saveQj(deviceId, deviceSn, key, value, LocalDateTime.now(ZoneId.of("GMT+8")));
                } else if (key.startsWith(DeviceMonitorType.L1_GP.getCode())) {
                    // 地表位移,GNSS数据
                    saveGnss(deviceId, deviceSn, key, value, LocalDateTime.now(ZoneId.of("GMT+8")));
                } else if (key.startsWith(DeviceMonitorType.L1_JS.getCode())) {
                    // 加速度数据
                    saveJsd(deviceId, deviceSn, key, value, LocalDateTime.now(ZoneId.of("GMT+8")));
                }
            }
        }
    }

    /**
     * 解析并保存单个或多个监测类型的历史数据上传
     *
     * @param deviceId
     * @param dto
     * @param deviceSn
     */
    private void analyzeSaveHistoricalData(String deviceId, DeviceRealtimeDataDTO dto, String deviceSn) {
        Map<String, Object> objectMap = dto.getData();
        if (!objectMap.containsKey(deviceId)) {
            throw new RuntimeException("历史数据参数data值错误，请检查");
        }
        Map<String, Object> monitorDataMap = (Map<String, Object>) objectMap.get(deviceId);
        if (monitorDataMap.containsKey(DeviceMonitorType.S1_ZT_1.getCode())) {
            // 设备状态数据上传解析，更新设备状态为在线
            // deviceMapper.updateDeviceStatus(deviceId, "在线");
        } else if (monitorDataMap.containsKey(DeviceMonitorType.S1_NE_1.getCode())) {

        } else {
            // 监测数据解析
            for (Map.Entry<String, Object> entry : monitorDataMap.entrySet()) {
                String key = entry.getKey();
                List<HistoryRecord> recordList = getHistoryData((Map<String, String>) entry.getValue());
                if (recordList == null || recordList.isEmpty()) {
                    continue;
                }
                if (key.startsWith(DeviceMonitorType.L3_YL.getCode())) {
                    // 雨量数据
                    for (HistoryRecord his : recordList) {
                        saveYl(deviceId, deviceSn, key, his.getValue(), his.getClttm());
                    }
                } else if (key.startsWith(DeviceMonitorType.L4_NW.getCode())) {
                    // 泥水位数据
                    for (HistoryRecord his : recordList) {
                        saveNw(deviceId, deviceSn, key, his.getValue(), his.getClttm());
                    }
                } else if (key.startsWith(DeviceMonitorType.L1_LF.getCode())) {
                    // 裂缝（拉线位移）数据
                    for (HistoryRecord his : recordList) {
                        saveLfwy(deviceId, deviceSn, key, his.getValue(), his.getClttm());
                    }
                } else if (key.startsWith(DeviceMonitorType.L3_HS.getCode())) {
                    // 土壤含水率设备
                    for (HistoryRecord his : recordList) {
                        saveTrhsl(deviceId, deviceSn, key, his.getValue(), his.getClttm());
                    }
                } else if (key.startsWith(DeviceMonitorType.L1_QJ.getCode())) {
                    // 倾角数据
                    for (HistoryRecord his : recordList) {
                        saveQj(deviceId, deviceSn, key, his.getValue(), his.getClttm());
                    }
                } else if (key.startsWith(DeviceMonitorType.L1_GP.getCode())) {
                    // 地表位移,GNSS数据
                    for (HistoryRecord his : recordList) {
                        saveGnss(deviceId, deviceSn, key, his.getValue(), his.getClttm());
                    }
                } else if (key.startsWith(DeviceMonitorType.L1_JS.getCode())) {
                    // 加速度数据
                    for (HistoryRecord his : recordList) {
                        saveJsd(deviceId, deviceSn, key, his.getValue(), his.getClttm());
                    }
                }
            }
        }
    }

    private void analyzeSaveFileData(String deviceId, DeviceRealtimeDataDTO dto, String deviceSn, byte[] fileByteArr) {
        Map<String, Object> objectMap = dto.getData();
        log.info("analyzeSaveFileData objectMap : {}", objectMap);
        if (fileByteArr == null || fileByteArr.length < 1) {
            log.error("analyzeSaveFileData fileByteArr is empty. deviceId: {}", deviceId);
            return;
        }
//        if (!objectMap.containsKey(deviceId)) {
//            throw new RuntimeException("文件类型数据参数data值错误，请检查");
//        }
        // 数据格式: {"did":"420000063889","ds_id":"L4_SP_1","file_type":"jpg","at":"2025-09-03T01:00:01.000Z","desc":"0-787-628849"}
        String did = objectMap.containsKey("did") ? objectMap.get("did").toString() : null;
        String dsId = objectMap.containsKey("ds_id") ? objectMap.get("ds_id").toString() : null;
        String fileType = objectMap.containsKey("file_type") ? objectMap.get("file_type").toString() : "null";
        String at = objectMap.containsKey("at") ? objectMap.get("at").toString() : "iot_" + System.currentTimeMillis();
        String desc = objectMap.containsKey("desc") ? objectMap.get("desc").toString() : null;

        LocalDateTime aa = DateUtils.parseToLocalDateTime(at);
        // 将文件写入到本地磁盘
        Path filePath = Paths.get(fileDir, deviceId, dsId, at + "." + fileType);
        log.info("====filePath: {}", filePath);
        File file = filePath.toFile();
        if (!file.getParentFile().exists()) {
            file.mkdirs();
        }
        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(fileByteArr);
            log.info("The file has been successfully written, path: {}", filePath);
        } catch (IOException e) {
            log.error("Error occurred while writing file.", e);
        }
    }

    /**
     * 保存雨量设备数据
     *
     * @param deviceId
     * @param key
     * @param value
     * @param clttm
     * @return
     */
    private int saveYl(String deviceId, String deviceSn, String key, String value, LocalDateTime clttm) {
        String[] arr = value.split(",");
        Yl entity = new Yl();
        entity.setArea(deviceId);
        entity.setStation(deviceSn);
        entity.setSensor(key);
        entity.setClttm(clttm);
        entity.setValue(Double.parseDouble(arr[0]));
        if (arr.length == 2) {
            entity.setTotalvalue(Double.parseDouble(arr[1]));
        }
        entity.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return ylMapper.insertYl(entity);
    }

    /**
     * 保存泥水位设备数据
     *
     * @param deviceId
     * @param key
     * @param value
     * @param clttm
     * @return
     */
    private int saveNw(String deviceId, String deviceSn, String key, String value, LocalDateTime clttm) {
        Nw entity = new Nw();
        entity.setArea(deviceId);
        entity.setStation(deviceSn);
        entity.setSensor(key);
        entity.setClttm(clttm);
        entity.setValue(Double.parseDouble(value));
        entity.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return nwMapper.insertNw(entity);
    }

    /**
     * 保存裂缝设备数据
     *
     * @param deviceId
     * @param key
     * @param value
     * @param clttm
     * @return
     */
    private int saveLfwy(String deviceId, String deviceSn, String key, String value, LocalDateTime clttm) {
        Lfwy entity = new Lfwy();
        entity.setArea(deviceId);
        entity.setStation(deviceSn);
        entity.setSensor(key);
        entity.setClttm(clttm);
        entity.setValue(Double.parseDouble(value));
        entity.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return lfwyMapper.insertLfwy(entity);
    }

    /**
     * 保存土壤含水率设备数据
     *
     * @param deviceId
     * @param key
     * @param value
     * @param clttm
     * @return
     */
    private int saveTrhsl(String deviceId, String deviceSn, String key, String value, LocalDateTime clttm) {
        Trhsl entity = new Trhsl();
        entity.setArea(deviceId);
        entity.setStation(deviceSn);
        entity.setSensor(key);
        entity.setClttm(clttm);
        entity.setValue(Double.parseDouble(value));
        entity.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return trhslMapper.insertTrhsl(entity);
    }

    /**
     * 保存倾角设备数据
     *
     * @param deviceId
     * @param key
     * @param value
     * @param clttm
     * @return
     */
    private int saveQj(String deviceId, String deviceSn, String key, String value, LocalDateTime clttm) {
        String[] arr = value.split(",");
        if (arr.length != 5) {
            throw new IllegalArgumentException("倾角监测数据错误，请检查");
        }
        Qj entity = new Qj();
        entity.setArea(deviceId);
        entity.setStation(deviceSn);
        entity.setSensor(key);
        entity.setClttm(clttm);
        entity.setX(Double.parseDouble(arr[0]));
        entity.setY(Double.parseDouble(arr[1]));
        entity.setZ(Double.parseDouble(arr[2]));
        entity.setAngle(Double.parseDouble(arr[3]));
        entity.setTrend(Double.parseDouble(arr[4]));
        entity.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return qjMapper.insertQj(entity);
    }

    /**
     * 保存GNSS位移设备数据
     *
     * @param deviceId
     * @param key
     * @param value
     * @param clttm
     * @return
     */
    private int saveGnss(String deviceId, String deviceSn, String key, String value, LocalDateTime clttm) {
        String[] arr = value.split(",");
        if (arr.length != 3) {
            throw new IllegalArgumentException("GNSS监测数据错误，请检查");
        }
        Gnss entity = new Gnss();
        entity.setArea(deviceId);
        entity.setStation(deviceSn);
        entity.setSensor(key);
        entity.setClttm(clttm);
        entity.setGpstotalx(Double.parseDouble(arr[0]));
        entity.setGpstotaly(Double.parseDouble(arr[1]));
        entity.setGpstotalz(Double.parseDouble(arr[2]));
        entity.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return gnssMapper.insertGnss(entity);
    }

    /**
     * 保存加速度设备数据
     *
     * @param deviceId
     * @param key
     * @param value
     * @param clttm
     * @return
     */
    private int saveJsd(String deviceId, String deviceSn, String key, String value, LocalDateTime clttm) {
        String[] arr = value.split(",");
        if (arr.length != 3) {
            throw new IllegalArgumentException("加速度监测数据错误，请检查");
        }
        Jsd entity = new Jsd();
        entity.setArea(deviceId);
        entity.setStation(deviceSn);
        entity.setSensor(key);
        entity.setClttm(clttm);
        entity.setGx(Double.parseDouble(arr[0]));
        entity.setGy(Double.parseDouble(arr[1]));
        entity.setGz(Double.parseDouble(arr[2]));
        entity.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return jsdMapper.insertJsd(entity);
    }

    /**
     * 组装监测设备的历史数据对象
     *
     * @param valueMap
     * @return
     */
    private List<HistoryRecord> getHistoryData(Map<String, String> valueMap) {
        Set<String> set = valueMap.keySet();
        if (set.isEmpty()) {
            return null;
        }
        List<HistoryRecord> rtList = new ArrayList<>();
        for (String tmpKey : set) {
            String value = valueMap.get(tmpKey);
            HistoryRecord d = new HistoryRecord();
            d.setTm(tmpKey);
            d.setValue(value);
            rtList.add(d);
        }
        return rtList;
    }

}
