package com.daspatial.disaster.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.daspatial.disaster.iot.mapper.JsdMapper;
import com.daspatial.disaster.iot.domain.Jsd;
import com.daspatial.disaster.iot.service.IJsdService;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 加速度数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class JsdServiceImpl extends ServiceImpl<JsdMapper, Jsd> implements IJsdService {

    @Resource
    private JsdMapper jsdMapper;

    /**
     * 查询加速度数据
     *
     * @param id 加速度数据主键
     * @return 加速度数据
     */
    @Override
    public Jsd selectJsdById(Long id) {
        return jsdMapper.selectJsdById(id);
    }

    /**
     * 查询加速度数据列表
     *
     * @param jsd 加速度数据
     * @return 加速度数据
     */
    @Override
    public List<Jsd> selectJsdList(Jsd jsd) {
        return jsdMapper.selectJsdList(jsd);
    }

    /**
     * 新增加速度数据
     *
     * @param jsd 加速度数据
     * @return 结果
     */
    @Override
    public int insertJsd(Jsd jsd) {
        jsd.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return jsdMapper.insertJsd(jsd);
    }

    /**
     * 修改加速度数据
     *
     * @param jsd 加速度数据
     * @return 结果
     */
    @Override
    public int updateJsd(Jsd jsd) {
        return jsdMapper.updateJsd(jsd);
    }

    /**
     * 批量删除加速度数据
     *
     * @param ids 需要删除的加速度数据主键
     * @return 结果
     */
    @Override
    public int deleteJsdByIds(Long[] ids) {
        return jsdMapper.deleteJsdByIds(ids);
    }

    /**
     * 删除加速度数据信息
     *
     * @param id 加速度数据主键
     * @return 结果
     */
    @Override
    public int deleteJsdById(Long id) {
        return jsdMapper.deleteJsdById(id);
    }
}
