package com.daspatial.disaster.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.daspatial.disaster.iot.mapper.LdMapper;
import com.daspatial.disaster.iot.domain.Ld;
import com.daspatial.disaster.iot.service.ILdService;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 雷达数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class LdServiceImpl extends ServiceImpl<LdMapper, Ld> implements ILdService {

    @Resource
    private LdMapper ldMapper;

    /**
     * 查询雷达数据
     *
     * @param id 雷达数据主键
     * @return 雷达数据
     */
    @Override
    public Ld selectLdById(Long id) {
        return ldMapper.selectLdById(id);
    }

    /**
     * 查询雷达数据列表
     *
     * @param ld 雷达数据
     * @return 雷达数据
     */
    @Override
    public List<Ld> selectLdList(Ld ld) {
        return ldMapper.selectLdList(ld);
    }

    /**
     * 新增雷达数据
     *
     * @param ld 雷达数据
     * @return 结果
     */
    @Override
    public int insertLd(Ld ld) {
        ld.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return ldMapper.insertLd(ld);
    }

    /**
     * 修改雷达数据
     *
     * @param ld 雷达数据
     * @return 结果
     */
    @Override
    public int updateLd(Ld ld) {
        return ldMapper.updateLd(ld);
    }

    /**
     * 批量删除雷达数据
     *
     * @param ids 需要删除的雷达数据主键
     * @return 结果
     */
    @Override
    public int deleteLdByIds(Long[] ids) {
        return ldMapper.deleteLdByIds(ids);
    }

    /**
     * 删除雷达数据信息
     *
     * @param id 雷达数据主键
     * @return 结果
     */
    @Override
    public int deleteLdById(Long id) {
        return ldMapper.deleteLdById(id);
    }
}
