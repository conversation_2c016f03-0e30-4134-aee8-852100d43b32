package com.daspatial.disaster.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.daspatial.disaster.iot.mapper.LfwyMapper;
import com.daspatial.disaster.iot.domain.Lfwy;
import com.daspatial.disaster.iot.service.ILfwyService;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 裂缝数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class LfwyServiceImpl extends ServiceImpl<LfwyMapper, Lfwy> implements ILfwyService {

    @Resource
    private LfwyMapper lfwyMapper;

    /**
     * 查询裂缝数据
     *
     * @param id 裂缝数据主键
     * @return 裂缝数据
     */
    @Override
    public Lfwy selectLfwyById(Long id) {
        return lfwyMapper.selectLfwyById(id);
    }

    /**
     * 查询裂缝数据列表
     *
     * @param lfwy 裂缝数据
     * @return 裂缝数据
     */
    @Override
    public List<Lfwy> selectLfwyList(Lfwy lfwy) {
        return lfwyMapper.selectLfwyList(lfwy);
    }

    /**
     * 新增裂缝数据
     *
     * @param lfwy 裂缝数据
     * @return 结果
     */
    @Override
    public int insertLfwy(Lfwy lfwy) {
        lfwy.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return lfwyMapper.insertLfwy(lfwy);
    }

    /**
     * 修改裂缝数据
     *
     * @param lfwy 裂缝数据
     * @return 结果
     */
    @Override
    public int updateLfwy(Lfwy lfwy) {
        return lfwyMapper.updateLfwy(lfwy);
    }

    /**
     * 批量删除裂缝数据
     *
     * @param ids 需要删除的裂缝数据主键
     * @return 结果
     */
    @Override
    public int deleteLfwyByIds(Long[] ids) {
        return lfwyMapper.deleteLfwyByIds(ids);
    }

    /**
     * 删除裂缝数据信息
     *
     * @param id 裂缝数据主键
     * @return 结果
     */
    @Override
    public int deleteLfwyById(Long id) {
        return lfwyMapper.deleteLfwyById(id);
    }
}
