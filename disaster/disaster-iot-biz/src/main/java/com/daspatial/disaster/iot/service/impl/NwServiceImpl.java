package com.daspatial.disaster.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.daspatial.disaster.iot.mapper.NwMapper;
import com.daspatial.disaster.iot.domain.Nw;
import com.daspatial.disaster.iot.service.INwService;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 泥水位Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class NwServiceImpl extends ServiceImpl<NwMapper, Nw> implements INwService {

    @Resource
    private NwMapper nwMapper;

    /**
     * 查询泥水位
     *
     * @param id 泥水位主键
     * @return 泥水位
     */
    @Override
    public Nw selectNwById(Long id) {
        return nwMapper.selectNwById(id);
    }

    /**
     * 查询泥水位列表
     *
     * @param nw 泥水位
     * @return 泥水位
     */
    @Override
    public List<Nw> selectNwList(Nw nw) {
        return nwMapper.selectNwList(nw);
    }

    /**
     * 新增泥水位
     *
     * @param nw 泥水位
     * @return 结果
     */
    @Override
    public int insertNw(Nw nw) {
        nw.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return nwMapper.insertNw(nw);
    }

    /**
     * 修改泥水位
     *
     * @param nw 泥水位
     * @return 结果
     */
    @Override
    public int updateNw(Nw nw) {
        return nwMapper.updateNw(nw);
    }

    /**
     * 批量删除泥水位
     *
     * @param ids 需要删除的泥水位主键
     * @return 结果
     */
    @Override
    public int deleteNwByIds(Long[] ids) {
        return nwMapper.deleteNwByIds(ids);
    }

    /**
     * 删除泥水位信息
     *
     * @param id 泥水位主键
     * @return 结果
     */
    @Override
    public int deleteNwById(Long id) {
        return nwMapper.deleteNwById(id);
    }
}
