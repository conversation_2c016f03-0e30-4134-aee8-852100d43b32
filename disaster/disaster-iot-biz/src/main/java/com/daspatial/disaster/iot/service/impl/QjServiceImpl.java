package com.daspatial.disaster.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.daspatial.disaster.iot.mapper.QjMapper;
import com.daspatial.disaster.iot.domain.Qj;
import com.daspatial.disaster.iot.service.IQjService;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 倾角数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class QjServiceImpl extends ServiceImpl<QjMapper, Qj> implements IQjService {

    @Resource
    private QjMapper qjMapper;

    /**
     * 查询倾角数据
     *
     * @param id 倾角数据主键
     * @return 倾角数据
     */
    @Override
    public Qj selectQjById(Long id) {
        return qjMapper.selectQjById(id);
    }

    /**
     * 查询倾角数据列表
     *
     * @param qj 倾角数据
     * @return 倾角数据
     */
    @Override
    public List<Qj> selectQjList(Qj qj) {
        return qjMapper.selectQjList(qj);
    }

    /**
     * 新增倾角数据
     *
     * @param qj 倾角数据
     * @return 结果
     */
    @Override
    public int insertQj(Qj qj) {
        qj.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return qjMapper.insertQj(qj);
    }

    /**
     * 修改倾角数据
     *
     * @param qj 倾角数据
     * @return 结果
     */
    @Override
    public int updateQj(Qj qj) {
        return qjMapper.updateQj(qj);
    }

    /**
     * 批量删除倾角数据
     *
     * @param ids 需要删除的倾角数据主键
     * @return 结果
     */
    @Override
    public int deleteQjByIds(Long[] ids) {
        return qjMapper.deleteQjByIds(ids);
    }

    /**
     * 删除倾角数据信息
     *
     * @param id 倾角数据主键
     * @return 结果
     */
    @Override
    public int deleteQjById(Long id) {
        return qjMapper.deleteQjById(id);
    }
}
