package com.daspatial.disaster.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.daspatial.disaster.iot.mapper.TrhslMapper;
import com.daspatial.disaster.iot.domain.Trhsl;
import com.daspatial.disaster.iot.service.ITrhslService;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 土壤含水率数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class TrhslServiceImpl extends ServiceImpl<TrhslMapper, Trhsl> implements ITrhslService {

    @Resource
    private TrhslMapper trhslMapper;

    /**
     * 查询土壤含水率数据
     *
     * @param id 土壤含水率数据主键
     * @return 土壤含水率数据
     */
    @Override
    public Trhsl selectTrhslById(Long id) {
        return trhslMapper.selectTrhslById(id);
    }

    /**
     * 查询土壤含水率数据列表
     *
     * @param trhsl 土壤含水率数据
     * @return 土壤含水率数据
     */
    @Override
    public List<Trhsl> selectTrhslList(Trhsl trhsl) {
        return trhslMapper.selectTrhslList(trhsl);
    }

    /**
     * 新增土壤含水率数据
     *
     * @param trhsl 土壤含水率数据
     * @return 结果
     */
    @Override
    public int insertTrhsl(Trhsl trhsl) {
        trhsl.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return trhslMapper.insertTrhsl(trhsl);
    }

    /**
     * 修改土壤含水率数据
     *
     * @param trhsl 土壤含水率数据
     * @return 结果
     */
    @Override
    public int updateTrhsl(Trhsl trhsl) {
        return trhslMapper.updateTrhsl(trhsl);
    }

    /**
     * 批量删除土壤含水率数据
     *
     * @param ids 需要删除的土壤含水率数据主键
     * @return 结果
     */
    @Override
    public int deleteTrhslByIds(Long[] ids) {
        return trhslMapper.deleteTrhslByIds(ids);
    }

    /**
     * 删除土壤含水率数据信息
     *
     * @param id 土壤含水率数据主键
     * @return 结果
     */
    @Override
    public int deleteTrhslById(Long id) {
        return trhslMapper.deleteTrhslById(id);
    }
}
