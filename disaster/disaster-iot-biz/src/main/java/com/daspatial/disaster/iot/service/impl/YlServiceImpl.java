package com.daspatial.disaster.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.daspatial.disaster.iot.mapper.YlMapper;
import com.daspatial.disaster.iot.domain.Yl;
import com.daspatial.disaster.iot.service.IYlService;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 雨量数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class YlServiceImpl extends ServiceImpl<YlMapper, Yl> implements IYlService {

    @Resource
    private YlMapper ylMapper;

    /**
     * 查询雨量数据
     *
     * @param id 雨量数据主键
     * @return 雨量数据
     */
    @Override
    public Yl selectYlById(Long id) {
        return ylMapper.selectYlById(id);
    }

    /**
     * 查询雨量数据列表
     *
     * @param yl 雨量数据
     * @return 雨量数据
     */
    @Override
    public List<Yl> selectYlList(Yl yl) {
        return ylMapper.selectYlList(yl);
    }

    /**
     * 新增雨量数据
     *
     * @param yl 雨量数据
     * @return 结果
     */
    @Override
    public int insertYl(Yl yl) {
        yl.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return ylMapper.insertYl(yl);
    }

    /**
     * 修改雨量数据
     *
     * @param yl 雨量数据
     * @return 结果
     */
    @Override
    public int updateYl(Yl yl) {
        return ylMapper.updateYl(yl);
    }

    /**
     * 批量删除雨量数据
     *
     * @param ids 需要删除的雨量数据主键
     * @return 结果
     */
    @Override
    public int deleteYlByIds(Long[] ids) {
        return ylMapper.deleteYlByIds(ids);
    }

    /**
     * 删除雨量数据信息
     *
     * @param id 雨量数据主键
     * @return 结果
     */
    @Override
    public int deleteYlById(Long id) {
        return ylMapper.deleteYlById(id);
    }
}
