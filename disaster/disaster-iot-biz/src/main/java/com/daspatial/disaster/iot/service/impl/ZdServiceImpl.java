package com.daspatial.disaster.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.daspatial.disaster.iot.common.utils.DateUtils;
import com.daspatial.disaster.iot.domain.dto.VibrationDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import com.daspatial.disaster.iot.mapper.ZdMapper;
import com.daspatial.disaster.iot.domain.Zd;
import com.daspatial.disaster.iot.service.IZdService;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 振动数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
public class ZdServiceImpl extends ServiceImpl<ZdMapper, Zd> implements IZdService {

    @Resource
    private ZdMapper zdMapper;

    /**
     * 查询振动数据
     *
     * @param id 振动数据主键
     * @return 振动数据
     */
    @Override
    public Zd selectZdById(Long id) {
        return zdMapper.selectZdById(id);
    }

    /**
     * 查询振动数据列表
     *
     * @param zd 振动数据
     * @return 振动数据
     */
    @Override
    public List<Zd> selectZdList(Zd zd) {
        return zdMapper.selectZdList(zd);
    }

    /**
     * 新增振动数据
     *
     * @param zd 振动数据
     * @return 结果
     */
    @Override
    public int insertZd(Zd zd) {
        zd.setSystm(LocalDateTime.now(ZoneId.of("GMT+8")));
        return zdMapper.insertZd(zd);
    }

    /**
     * 修改振动数据
     *
     * @param zd 振动数据
     * @return 结果
     */
    @Override
    public int updateZd(Zd zd) {
        return zdMapper.updateZd(zd);
    }

    /**
     * 批量删除振动数据
     *
     * @param ids 需要删除的振动数据主键
     * @return 结果
     */
    @Override
    public int deleteZdByIds(Long[] ids) {
        return zdMapper.deleteZdByIds(ids);
    }

    /**
     * 删除振动数据信息
     *
     * @param id 振动数据主键
     * @return 结果
     */
    @Override
    public int deleteZdById(Long id) {
        return zdMapper.deleteZdById(id);
    }
}
