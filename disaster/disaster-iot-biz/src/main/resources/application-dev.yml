spring:
#  data:
#    redis:
#      host: 127.0.0.1  # Redis地址
#      port: 16379
#      database: 1
#      password: das321
  # 数据库相关配置
  datasource:
    driver-class-name: org.postgresql.Driver
    username: postgres
    password: agjfrtgfgf42gfdgfd
    url: **************************************************

iot:
  mqtt:
    # MQTT服务地址，端口号默认1883，如果有多个，用逗号隔开
    url: tcp://**************:1883
    username: oZ1lF1qS6iW8tH8kH1iX1q
    password: dE3gB3rQ1aJ9qO0rS5dY2s
    # 客户端id(不能重复)
    client:
      id: iot-biz
    # MQTT默认的消息推送主题，实际可在调用接口是指定
    default:
      topic: topic
    topic: $dp
    file:
      dir: E:/companyProject/disaster-iot-biz/files