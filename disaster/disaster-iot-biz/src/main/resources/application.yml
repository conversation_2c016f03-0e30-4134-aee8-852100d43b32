gisadmin:
  version: 202508

server:
  port: 9932
  servlet:
    context-path: /iot
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

spring:
  application:
    name: @project.artifactId@
  profiles:
    active: dev
  #  cache:
  #    type: redis
  #文件上传
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  main:
    allow-bean-definition-overriding: true

logging:
  level:
    org.springframework: warn
    com.daspatial.disaster.iot: info

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

#--------------如下配置尽量不要变动-------------
# mybatis-plus 配置
mybatis-plus:
  type-aliases-package: com.daspatial.disaster.**.domain
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  global-config:
    banner: false
    db-config:
      id-type: auto
      where-strategy: not_empty
      insert-strategy: not_empty
      update-strategy: not_null
  configuration:
    jdbc-type-for-null: 'null'
    call-setters-on-nulls: true
    shrink-whitespaces-in-sql: true
    #开启打印
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    #关闭打印
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl