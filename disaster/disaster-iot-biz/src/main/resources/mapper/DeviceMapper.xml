<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.daspatial.disaster.iot.mapper.DeviceMapper">

  <resultMap id="deviceMap" type="com.daspatial.disaster.iot.domain.Device">
        <id property="id" column="id"/>
        <result property="geom" column="geom"/>
        <result property="deviceName" column="device_name"/>
        <result property="provincialCode" column="provincial_code"/>
        <result property="monitorDataType" column="monitor_data_type"/>
        <result property="sensorType" column="sensor_type"/>
        <result property="syncFlag" column="sync_flag"/>
        <result property="city" column="city"/>
        <result property="county" column="county"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="monitorPointName" column="monitor_point_name"/>
        <result property="monitorPointCode" column="monitor_point_code"/>
        <result property="deviceStatus" column="device_status"/>
        <result property="clientId" column="client_id"/>
        <result property="deviceKey" column="device_key"/>
        <result property="deviceSn" column="device_sn"/>
        <result property="deviceModel" column="device_model"/>
        <result property="network" column="network"/>
        <result property="protocol" column="protocol"/>
        <result property="deviceType" column="device_type"/>
        <result property="deviceCompany" column="device_company"/>
        <result property="deptId" column="dept_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
  </resultMap>

      <select id="selectDeviceSnByClientId" resultType="java.lang.String">
            select device_sn from t_device
            where client_id = #{clientId}
      </select>

      <select id="selectCountByClientIdAndDeviceKey" resultType="java.lang.Long">
            select count(*) from t_device
            where client_id = #{clientId} and device_key = #{deviceKey}
      </select>

      <select id="selectClientIdByDeviceSn" resultType="java.lang.String">
            select client_id from t_device
            where device_sn = #{deviceSn}
      </select>

      <update id="updateDeviceStatus">
            update t_devices set device_status = #{deviceStatus}
            where client_id = #{clientId}
      </update>

</mapper>