<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daspatial.disaster.iot.mapper.DxsMapper">
    
    <resultMap type="com.daspatial.disaster.iot.domain.Dxs" id="DxsResult">
        <result property="id"    column="id"    />
        <result property="area"    column="area"    />
        <result property="station"    column="station"    />
        <result property="sensor"    column="sensor"    />
        <result property="clttm"    column="clttm"    />
        <result property="temp"    column="temp"    />
        <result property="value"    column="value"    />
        <result property="systm"    column="systm"    />
    </resultMap>

    <sql id="selectDxsVo">
        select id, area, station, sensor, clttm, temp, value, systm from tbl_jc_dxs
    </sql>

    <select id="selectDxsList" parameterType="Dxs" resultMap="DxsResult">
        <include refid="selectDxsVo"/>
        <where>  
            <if test="temp != null "> and temp = #{temp}</if>
            <if test="value != null "> and value = #{value}</if>
            <if test="systm != null "> and systm = #{systm}</if>
        </where>
    </select>
    
    <select id="selectDxsByArea" parameterType="String" resultMap="DxsResult">
        <include refid="selectDxsVo"/>
        where area = #{area}
    </select>
        
    <insert id="insertDxs" parameterType="Dxs">
        insert into tbl_jc_dxs
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="area != null">area,</if>
            <if test="station != null">station,</if>
            <if test="sensor != null">sensor,</if>
            <if test="clttm != null">clttm,</if>
            <if test="temp != null">temp,</if>
            <if test="value != null">value,</if>
            <if test="systm != null">systm,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="area != null">#{area},</if>
            <if test="station != null">#{station},</if>
            <if test="sensor != null">#{sensor},</if>
            <if test="clttm != null">#{clttm},</if>
            <if test="temp != null">#{temp},</if>
            <if test="value != null">#{value},</if>
            <if test="systm != null">#{systm},</if>
         </trim>
        ON CONFLICT (station, sensor, clttm)
        DO NOTHING
    </insert>

    <update id="updateDxs" parameterType="Dxs">
        update tbl_jc_dxs
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="station != null">station = #{station},</if>
            <if test="sensor != null">sensor = #{sensor},</if>
            <if test="clttm != null">clttm = #{clttm},</if>
            <if test="temp != null">temp = #{temp},</if>
            <if test="value != null">value = #{value},</if>
            <if test="systm != null">systm = #{systm},</if>
        </trim>
        where area = #{area}
    </update>

    <delete id="deleteDxsByArea" parameterType="String">
        delete from tbl_jc_dxs where area = #{area}
    </delete>

    <delete id="deleteDxsByAreas" parameterType="String">
        delete from tbl_jc_dxs where area in 
        <foreach item="area" collection="array" open="(" separator="," close=")">
            #{area}
        </foreach>
    </delete>
</mapper>