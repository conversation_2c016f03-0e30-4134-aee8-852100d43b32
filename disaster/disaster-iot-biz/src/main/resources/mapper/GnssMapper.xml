<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daspatial.disaster.iot.mapper.GnssMapper">
    
    <resultMap type="com.daspatial.disaster.iot.domain.Gnss" id="GnssResult">
        <result property="id"    column="id"    />
        <result property="area"    column="area"    />
        <result property="station"    column="station"    />
        <result property="sensor"    column="sensor"    />
        <result property="clttm"    column="clttm"    />
        <result property="gpsinitial"    column="gpsinitial"    />
        <result property="gpstotalx"    column="gpstotalx"    />
        <result property="gpstotaly"    column="gpstotaly"    />
        <result property="gpstotalz"    column="gpstotalz"    />
        <result property="systm"    column="systm"    />
    </resultMap>

    <sql id="selectGnssVo">
        select id, area, station, sensor, clttm, gpsinitial, gpstotalx, gpstotaly, gpstotalz, systm from tbl_jc_gnss
    </sql>

    <select id="selectGnssList" parameterType="Gnss" resultMap="GnssResult">
        <include refid="selectGnssVo"/>
        <where>  
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="station != null  and station != ''"> and station = #{station}</if>
            <if test="sensor != null  and sensor != ''"> and sensor = #{sensor}</if>
            <if test="clttm != null "> and clttm = #{clttm}</if>
            <if test="gpsinitial != null  and gpsinitial != ''"> and gpsinitial = #{gpsinitial}</if>
            <if test="gpstotalx != null "> and gpstotalx = #{gpstotalx}</if>
            <if test="gpstotaly != null "> and gpstotaly = #{gpstotaly}</if>
            <if test="gpstotalz != null "> and gpstotalz = #{gpstotalz}</if>
            <if test="systm != null "> and systm = #{systm}</if>
        </where>
    </select>
    
    <select id="selectGnssById" parameterType="Long" resultMap="GnssResult">
        <include refid="selectGnssVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertGnss" parameterType="Gnss" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_jc_gnss
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="area != null">area,</if>
            <if test="station != null">station,</if>
            <if test="sensor != null and sensor != ''">sensor,</if>
            <if test="clttm != null">clttm,</if>
            <if test="gpsinitial != null">gpsinitial,</if>
            <if test="gpstotalx != null">gpstotalx,</if>
            <if test="gpstotaly != null">gpstotaly,</if>
            <if test="gpstotalz != null">gpstotalz,</if>
            <if test="systm != null">systm,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="area != null">#{area},</if>
            <if test="station != null">#{station},</if>
            <if test="sensor != null and sensor != ''">#{sensor},</if>
            <if test="clttm != null">#{clttm},</if>
            <if test="gpsinitial != null">#{gpsinitial},</if>
            <if test="gpstotalx != null">#{gpstotalx},</if>
            <if test="gpstotaly != null">#{gpstotaly},</if>
            <if test="gpstotalz != null">#{gpstotalz},</if>
            <if test="systm != null">#{systm},</if>
         </trim>
        ON CONFLICT (station, sensor, clttm)
        DO NOTHING
    </insert>

    <update id="updateGnss" parameterType="Gnss">
        update tbl_jc_gnss
        <trim prefix="SET" suffixOverrides=",">
            <if test="area != null">area = #{area},</if>
            <if test="station != null">station = #{station},</if>
            <if test="sensor != null and sensor != ''">sensor = #{sensor},</if>
            <if test="clttm != null">clttm = #{clttm},</if>
            <if test="gpsinitial != null">gpsinitial = #{gpsinitial},</if>
            <if test="gpstotalx != null">gpstotalx = #{gpstotalx},</if>
            <if test="gpstotaly != null">gpstotaly = #{gpstotaly},</if>
            <if test="gpstotalz != null">gpstotalz = #{gpstotalz},</if>
            <if test="systm != null">systm = #{systm},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteGnssById" parameterType="Long">
        delete from tbl_jc_gnss where id = #{id}
    </delete>

    <delete id="deleteGnssByIds" parameterType="String">
        delete from tbl_jc_gnss where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>