<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daspatial.disaster.iot.mapper.JsdMapper">
    
    <resultMap type="com.daspatial.disaster.iot.domain.Jsd" id="JsdResult">
        <result property="id"    column="id"    />
        <result property="area"    column="area"    />
        <result property="station"    column="station"    />
        <result property="sensor"    column="sensor"    />
        <result property="clttm"    column="clttm"    />
        <result property="gx"    column="gx"    />
        <result property="gy"    column="gy"    />
        <result property="gz"    column="gz"    />
        <result property="systm"    column="systm"    />
    </resultMap>

    <sql id="selectJsdVo">
        select id, area, station, sensor, clttm, gx, gy, gz, systm from tbl_jc_jsd
    </sql>

    <select id="selectJsdList" parameterType="Jsd" resultMap="JsdResult">
        <include refid="selectJsdVo"/>
        <where>  
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="station != null  and station != ''"> and station = #{station}</if>
            <if test="sensor != null  and sensor != ''"> and sensor = #{sensor}</if>
            <if test="clttm != null "> and clttm = #{clttm}</if>
            <if test="gx != null "> and gx = #{gx}</if>
            <if test="gy != null "> and gy = #{gy}</if>
            <if test="gz != null "> and gz = #{gz}</if>
            <if test="systm != null "> and systm = #{systm}</if>
        </where>
    </select>
    
    <select id="selectJsdById" parameterType="Long" resultMap="JsdResult">
        <include refid="selectJsdVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertJsd" parameterType="Jsd" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_jc_jsd
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="area != null">area,</if>
            <if test="station != null">station,</if>
            <if test="sensor != null and sensor != ''">sensor,</if>
            <if test="clttm != null">clttm,</if>
            <if test="gx != null">gx,</if>
            <if test="gy != null">gy,</if>
            <if test="gz != null">gz,</if>
            <if test="systm != null">systm,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="area != null">#{area},</if>
            <if test="station != null">#{station},</if>
            <if test="sensor != null and sensor != ''">#{sensor},</if>
            <if test="clttm != null">#{clttm},</if>
            <if test="gx != null">#{gx},</if>
            <if test="gy != null">#{gy},</if>
            <if test="gz != null">#{gz},</if>
            <if test="systm != null">#{systm},</if>
         </trim>
        ON CONFLICT (station, sensor, clttm)
        DO NOTHING
    </insert>

    <update id="updateJsd" parameterType="Jsd">
        update tbl_jc_jsd
        <trim prefix="SET" suffixOverrides=",">
            <if test="area != null">area = #{area},</if>
            <if test="station != null">station = #{station},</if>
            <if test="sensor != null and sensor != ''">sensor = #{sensor},</if>
            <if test="clttm != null">clttm = #{clttm},</if>
            <if test="gx != null">gx = #{gx},</if>
            <if test="gy != null">gy = #{gy},</if>
            <if test="gz != null">gz = #{gz},</if>
            <if test="systm != null">systm = #{systm},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteJsdById" parameterType="Long">
        delete from tbl_jc_jsd where id = #{id}
    </delete>

    <delete id="deleteJsdByIds" parameterType="String">
        delete from tbl_jc_jsd where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>