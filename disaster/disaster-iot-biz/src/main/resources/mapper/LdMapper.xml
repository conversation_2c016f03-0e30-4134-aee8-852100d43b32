<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daspatial.disaster.iot.mapper.LdMapper">
    
    <resultMap type="com.daspatial.disaster.iot.domain.Ld" id="LdResult">
        <result property="id"    column="id"    />
        <result property="area"    column="area"    />
        <result property="station"    column="station"    />
        <result property="sensor"    column="sensor"    />
        <result property="clttm"    column="clttm"    />
        <result property="x"    column="x"    />
        <result property="y"    column="y"    />
        <result property="z"    column="z"    />
        <result property="speed"    column="speed"    />
        <result property="systm"    column="systm"    />
    </resultMap>

    <sql id="selectLdVo">
        select id, area, station, sensor, clttm, x, y, z, speed, systm from tbl_jc_ld
    </sql>

    <select id="selectLdList" parameterType="Ld" resultMap="LdResult">
        <include refid="selectLdVo"/>
        <where>  
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="station != null  and station != ''"> and station = #{station}</if>
            <if test="sensor != null  and sensor != ''"> and sensor = #{sensor}</if>
            <if test="clttm != null "> and clttm = #{clttm}</if>
            <if test="x != null "> and x = #{x}</if>
            <if test="y != null "> and y = #{y}</if>
            <if test="z != null "> and z = #{z}</if>
            <if test="speed != null "> and speed = #{speed}</if>
            <if test="systm != null "> and systm = #{systm}</if>
        </where>
    </select>
    
    <select id="selectLdById" parameterType="Long" resultMap="LdResult">
        <include refid="selectLdVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLd" parameterType="Ld" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_jc_ld
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="area != null">area,</if>
            <if test="station != null">station,</if>
            <if test="sensor != null and sensor != ''">sensor,</if>
            <if test="clttm != null">clttm,</if>
            <if test="x != null">x,</if>
            <if test="y != null">y,</if>
            <if test="z != null">z,</if>
            <if test="speed != null">speed,</if>
            <if test="systm != null">systm,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="area != null">#{area},</if>
            <if test="station != null">#{station},</if>
            <if test="sensor != null and sensor != ''">#{sensor},</if>
            <if test="clttm != null">#{clttm},</if>
            <if test="x != null">#{x},</if>
            <if test="y != null">#{y},</if>
            <if test="z != null">#{z},</if>
            <if test="speed != null">#{speed},</if>
            <if test="systm != null">#{systm},</if>
         </trim>
        ON CONFLICT (station, sensor, clttm)
        DO NOTHING
    </insert>

    <update id="updateLd" parameterType="Ld">
        update tbl_jc_ld
        <trim prefix="SET" suffixOverrides=",">
            <if test="area != null">area = #{area},</if>
            <if test="station != null">station = #{station},</if>
            <if test="sensor != null and sensor != ''">sensor = #{sensor},</if>
            <if test="clttm != null">clttm = #{clttm},</if>
            <if test="x != null">x = #{x},</if>
            <if test="y != null">y = #{y},</if>
            <if test="z != null">z = #{z},</if>
            <if test="speed != null">speed = #{speed},</if>
            <if test="systm != null">systm = #{systm},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLdById" parameterType="Long">
        delete from tbl_jc_ld where id = #{id}
    </delete>

    <delete id="deleteLdByIds" parameterType="String">
        delete from tbl_jc_ld where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>