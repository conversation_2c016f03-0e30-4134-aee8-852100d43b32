<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daspatial.disaster.iot.mapper.LfwyMapper">
    
    <resultMap type="com.daspatial.disaster.iot.domain.Lfwy" id="LfwyResult">
        <result property="id"    column="id"    />
        <result property="area"    column="area"    />
        <result property="station"    column="station"    />
        <result property="sensor"    column="sensor"    />
        <result property="clttm"    column="clttm"    />
        <result property="value"    column="value"    />
        <result property="systm"    column="systm"    />
    </resultMap>

    <sql id="selectLfwyVo">
        select id, area, station, sensor, clttm, value, systm from tbl_jc_lfwy
    </sql>

    <select id="selectLfwyList" parameterType="Lfwy" resultMap="LfwyResult">
        <include refid="selectLfwyVo"/>
        <where>  
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="station != null  and station != ''"> and station = #{station}</if>
            <if test="sensor != null  and sensor != ''"> and sensor = #{sensor}</if>
            <if test="clttm != null "> and clttm = #{clttm}</if>
            <if test="value != null "> and value = #{value}</if>
            <if test="systm != null "> and systm = #{systm}</if>
        </where>
    </select>
    
    <select id="selectLfwyById" parameterType="Long" resultMap="LfwyResult">
        <include refid="selectLfwyVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLfwy" parameterType="Lfwy" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_jc_lfwy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="area != null">area,</if>
            <if test="station != null">station,</if>
            <if test="sensor != null and sensor != ''">sensor,</if>
            <if test="clttm != null">clttm,</if>
            <if test="value != null">value,</if>
            <if test="systm != null">systm,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="area != null">#{area},</if>
            <if test="station != null">#{station},</if>
            <if test="sensor != null and sensor != ''">#{sensor},</if>
            <if test="clttm != null">#{clttm},</if>
            <if test="value != null">#{value},</if>
            <if test="systm != null">#{systm},</if>
         </trim>
        ON CONFLICT (station, sensor, clttm)
        DO NOTHING
    </insert>

    <update id="updateLfwy" parameterType="Lfwy">
        update tbl_jc_lfwy
        <trim prefix="SET" suffixOverrides=",">
            <if test="area != null">area = #{area},</if>
            <if test="station != null">station = #{station},</if>
            <if test="sensor != null and sensor != ''">sensor = #{sensor},</if>
            <if test="clttm != null">clttm = #{clttm},</if>
            <if test="value != null">value = #{value},</if>
            <if test="systm != null">systm = #{systm},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLfwyById" parameterType="Long">
        delete from tbl_jc_lfwy where id = #{id}
    </delete>

    <delete id="deleteLfwyByIds" parameterType="String">
        delete from tbl_jc_lfwy where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>