<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daspatial.disaster.iot.mapper.LogMonitorDataMapper">
    
    <resultMap type="com.daspatial.disaster.iot.domain.LogMonitorData" id="LogMonitorDataResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="deviceId"    column="device_id"    />
        <result property="station"    column="station"    />
        <result property="data"    column="data"    />
        <result property="systm"    column="systm"    />
    </resultMap>

    <sql id="selectLogMonitorDataVo">
        select id, type, device_id, station, data, systm from tbl_log_monitor_data
    </sql>
</mapper>