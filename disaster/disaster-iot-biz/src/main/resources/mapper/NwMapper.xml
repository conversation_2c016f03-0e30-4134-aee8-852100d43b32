<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daspatial.disaster.iot.mapper.NwMapper">
    
    <resultMap type="com.daspatial.disaster.iot.domain.Nw" id="NwResult">
        <result property="id"    column="id"    />
        <result property="area"    column="area"    />
        <result property="station"    column="station"    />
        <result property="sensor"    column="sensor"    />
        <result property="clttm"    column="clttm"    />
        <result property="value"    column="value"    />
        <result property="systm"    column="systm"    />
    </resultMap>

    <sql id="selectNwVo">
        select id, area, station, sensor, clttm, value, systm from tbl_jc_nw
    </sql>

    <select id="selectNwList" parameterType="Nw" resultMap="NwResult">
        <include refid="selectNwVo"/>
        <where>  
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="station != null  and station != ''"> and station = #{station}</if>
            <if test="sensor != null  and sensor != ''"> and sensor = #{sensor}</if>
            <if test="clttm != null  and clttm != ''"> and clttm = #{clttm}</if>
            <if test="value != null  and value != ''"> and value = #{value}</if>
            <if test="systm != null  and systm != ''"> and systm = #{systm}</if>
        </where>
    </select>
    
    <select id="selectNwById" parameterType="Long" resultMap="NwResult">
        <include refid="selectNwVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertNw" parameterType="Nw" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_jc_nw
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="area != null">area,</if>
            <if test="station != null">station,</if>
            <if test="sensor != null and sensor != ''">sensor,</if>
            <if test="clttm != null">clttm,</if>
            <if test="value != null">value,</if>
            <if test="systm != null">systm,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="area != null">#{area},</if>
            <if test="station != null">#{station},</if>
            <if test="sensor != null and sensor != ''">#{sensor},</if>
            <if test="clttm != null ">#{clttm},</if>
            <if test="value != null">#{value},</if>
            <if test="systm != null">#{systm},</if>
         </trim>
        ON CONFLICT (station, sensor, clttm)
        DO NOTHING
    </insert>

    <update id="updateNw" parameterType="Nw">
        update tbl_jc_nw
        <trim prefix="SET" suffixOverrides=",">
            <if test="area != null">area = #{area},</if>
            <if test="station != null">station = #{station},</if>
            <if test="sensor != null and sensor != ''">sensor = #{sensor},</if>
            <if test="clttm != null ">clttm = #{clttm},</if>
            <if test="value != null">value = #{value},</if>
            <if test="systm != null">systm = #{systm},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNwById" parameterType="Long">
        delete from tbl_jc_nw where id = #{id}
    </delete>

    <delete id="deleteNwByIds" parameterType="String">
        delete from tbl_jc_nw where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>