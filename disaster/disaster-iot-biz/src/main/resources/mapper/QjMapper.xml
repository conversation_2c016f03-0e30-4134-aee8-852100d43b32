<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daspatial.disaster.iot.mapper.QjMapper">
    
    <resultMap type="com.daspatial.disaster.iot.domain.Qj" id="QjResult">
        <result property="id"    column="id"    />
        <result property="area"    column="area"    />
        <result property="station"    column="station"    />
        <result property="sensor"    column="sensor"    />
        <result property="clttm"    column="clttm"    />
        <result property="x"    column="x"    />
        <result property="y"    column="y"    />
        <result property="z"    column="z"    />
        <result property="angle"    column="angle"    />
        <result property="trend"    column="trend"    />
        <result property="systm"    column="systm"    />
    </resultMap>

    <sql id="selectQjVo">
        select id, area, station, sensor, clttm, x, y, z, angle, trend, systm from tbl_jc_qj
    </sql>

    <select id="selectQjList" parameterType="Qj" resultMap="QjResult">
        <include refid="selectQjVo"/>
        <where>  
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="station != null  and station != ''"> and station = #{station}</if>
            <if test="sensor != null  and sensor != ''"> and sensor = #{sensor}</if>
            <if test="clttm != null "> and clttm = #{clttm}</if>
            <if test="x != null "> and x = #{x}</if>
            <if test="y != null "> and y = #{y}</if>
            <if test="z != null "> and z = #{z}</if>
            <if test="angle != null "> and angle = #{angle}</if>
            <if test="trend != null "> and trend = #{trend}</if>
            <if test="systm != null "> and systm = #{systm}</if>
        </where>
    </select>
    
    <select id="selectQjById" parameterType="Long" resultMap="QjResult">
        <include refid="selectQjVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertQj" parameterType="Qj" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_jc_qj
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="area != null">area,</if>
            <if test="station != null">station,</if>
            <if test="sensor != null and sensor != ''">sensor,</if>
            <if test="clttm != null">clttm,</if>
            <if test="x != null">x,</if>
            <if test="y != null">y,</if>
            <if test="z != null">z,</if>
            <if test="angle != null">angle,</if>
            <if test="trend != null">trend,</if>
            <if test="systm != null">systm,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="area != null">#{area},</if>
            <if test="station != null">#{station},</if>
            <if test="sensor != null and sensor != ''">#{sensor},</if>
            <if test="clttm != null">#{clttm},</if>
            <if test="x != null">#{x},</if>
            <if test="y != null">#{y},</if>
            <if test="z != null">#{z},</if>
            <if test="angle != null">#{angle},</if>
            <if test="trend != null">#{trend},</if>
            <if test="systm != null">#{systm},</if>
         </trim>
        ON CONFLICT (station, sensor, clttm)
        DO NOTHING
    </insert>

    <update id="updateQj" parameterType="Qj">
        update tbl_jc_qj
        <trim prefix="SET" suffixOverrides=",">
            <if test="area != null">area = #{area},</if>
            <if test="station != null">station = #{station},</if>
            <if test="sensor != null and sensor != ''">sensor = #{sensor},</if>
            <if test="clttm != null">clttm = #{clttm},</if>
            <if test="x != null">x = #{x},</if>
            <if test="y != null">y = #{y},</if>
            <if test="z != null">z = #{z},</if>
            <if test="angle != null">angle = #{angle},</if>
            <if test="trend != null">trend = #{trend},</if>
            <if test="systm != null">systm = #{systm},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteQjById" parameterType="Long">
        delete from tbl_jc_qj where id = #{id}
    </delete>

    <delete id="deleteQjByIds" parameterType="String">
        delete from tbl_jc_qj where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>