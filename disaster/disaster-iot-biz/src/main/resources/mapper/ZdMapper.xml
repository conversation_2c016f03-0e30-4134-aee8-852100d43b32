<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daspatial.disaster.iot.mapper.ZdMapper">
    
    <resultMap type="com.daspatial.disaster.iot.domain.Zd" id="ZdResult">
        <result property="id"    column="id"    />
        <result property="area"    column="area"    />
        <result property="station"    column="station"    />
        <result property="sensor"    column="sensor"    />
        <result property="clttm"    column="clttm"    />
        <result property="plx"    column="plx"    />
        <result property="ply"    column="ply"    />
        <result property="plz"    column="plz"    />
        <result property="value"    column="value"    />
        <result property="sjx"    column="sjx"    />
        <result property="sjy"    column="sjy"    />
        <result property="sjz"    column="sjz"    />
        <result property="sjvalue"    column="sjvalue"    />
        <result property="systm"    column="systm"    />
    </resultMap>

    <sql id="selectZdVo">
        select id, area, station, sensor, clttm, plx, ply, plz, value, sjx, sjy, sjz, sjvalue, systm from tbl_jc_zd
    </sql>

    <select id="selectZdList" parameterType="Zd" resultMap="ZdResult">
        <include refid="selectZdVo"/>
        <where>  
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="station != null  and station != ''"> and station = #{station}</if>
            <if test="sensor != null  and sensor != ''"> and sensor = #{sensor}</if>
            <if test="clttm != null "> and clttm = #{clttm}</if>
            <if test="plx != null "> and plx = #{plx}</if>
            <if test="ply != null "> and ply = #{ply}</if>
            <if test="plz != null "> and plz = #{plz}</if>
            <if test="value != null "> and value = #{value}</if>
            <if test="sjx != null "> and sjx = #{sjx}</if>
            <if test="sjy != null "> and sjy = #{sjy}</if>
            <if test="sjz != null "> and sjz = #{sjz}</if>
            <if test="sjvalue != null "> and sjvalue = #{sjvalue}</if>
            <if test="systm != null "> and systm = #{systm}</if>
        </where>
    </select>
    
    <select id="selectZdById" parameterType="Long" resultMap="ZdResult">
        <include refid="selectZdVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertZd" parameterType="Zd" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_jc_zd
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="area != null">area,</if>
            <if test="station != null">station,</if>
            <if test="sensor != null and sensor != ''">sensor,</if>
            <if test="clttm != null">clttm,</if>
            <if test="plx != null">plx,</if>
            <if test="ply != null">ply,</if>
            <if test="plz != null">plz,</if>
            <if test="value != null">value,</if>
            <if test="sjx != null">sjx,</if>
            <if test="sjy != null">sjy,</if>
            <if test="sjz != null">sjz,</if>
            <if test="sjvalue != null">sjvalue,</if>
            <if test="systm != null">systm,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="area != null">#{area},</if>
            <if test="station != null">#{station},</if>
            <if test="sensor != null and sensor != ''">#{sensor},</if>
            <if test="clttm != null">#{clttm},</if>
            <if test="plx != null">#{plx},</if>
            <if test="ply != null">#{ply},</if>
            <if test="plz != null">#{plz},</if>
            <if test="value != null">#{value},</if>
            <if test="sjx != null">#{sjx},</if>
            <if test="sjy != null">#{sjy},</if>
            <if test="sjz != null">#{sjz},</if>
            <if test="sjvalue != null">#{sjvalue},</if>
            <if test="systm != null">#{systm},</if>
         </trim>
        ON CONFLICT (station, sensor, clttm)
        DO NOTHING
    </insert>

    <update id="updateZd" parameterType="Zd">
        update tbl_jc_zd
        <trim prefix="SET" suffixOverrides=",">
            <if test="area != null">area = #{area},</if>
            <if test="station != null">station = #{station},</if>
            <if test="sensor != null and sensor != ''">sensor = #{sensor},</if>
            <if test="clttm != null">clttm = #{clttm},</if>
            <if test="plx != null">plx = #{plx},</if>
            <if test="ply != null">ply = #{ply},</if>
            <if test="plz != null">plz = #{plz},</if>
            <if test="value != null">value = #{value},</if>
            <if test="sjx != null">sjx = #{sjx},</if>
            <if test="sjy != null">sjy = #{sjy},</if>
            <if test="sjz != null">sjz = #{sjz},</if>
            <if test="sjvalue != null">sjvalue = #{sjvalue},</if>
            <if test="systm != null">systm = #{systm},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZdById" parameterType="Long">
        delete from tbl_jc_zd where id = #{id}
    </delete>

    <delete id="deleteZdByIds" parameterType="String">
        delete from tbl_jc_zd where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>